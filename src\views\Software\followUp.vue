<script setup>
import { ref } from 'vue'
const isTrue = ref('0')
const barner = ref('0')
const barner_1 = ref('0')
const barnerFn = val => {
  barner.value = val
}
const isTrueFn = val => {
  isTrue.value = val
  barner.value = '0'
}
</script>
<template>
  <div class="main_box">
    <div class="section_1">
      <div class="section_1box">
        <div class="section_1box_top">智慧随访系统</div>
        <div class="section_1box_bottom">
          旨在提高医疗机构对患者随访工作的效率和质量。该系统结合了AI电话自动随访、随访录音、CRF表格回填等先进技术，支持按病种类型配置随访计划和自定义随访表单，适用于各类医疗机构进行患者随访管理。
        </div>
      </div>
    </div>
    <div class="section_2">
      <div class="section_2_1">AI智能+智慧随访系统双引擎</div>
      <div class="section_2_2"></div>
      <div class="section_2_3">TECHNOLOGY LEADING THE ERA</div>
      <div class="section_2_4">
        <div :class="isTrue === '0' ? 'section_2_text is' : 'section_2_text'" @click="isTrueFn('0')">
          <div class="section_2_text_1">大数据统计</div>
          <div class="section_2_text_2">多端同步 预警提示</div>
          <div v-show="isTrue === '0'" class="xian_1"></div>
        </div>
        <div :class="isTrue === '1' ? 'section_3_text is' : 'section_3_text'" @click="isTrueFn('1')">
          <div class="section_2_text_1">AI机器人</div>
          <div class="section_2_text_2">自动化回访 减少人力</div>
          <div v-show="isTrue === '1'" class="xian_1"></div>
        </div>
        <div :class="isTrue === '2' ? 'section_4_text is' : 'section_4_text'" @click="isTrueFn('2')">
          <div class="section_2_text_1">卫软穿戴</div>
          <div class="section_2_text_2">实时监测 安心保障</div>
          <div v-show="isTrue === '2'" class="xian_1"></div>
        </div>
        <div :class="isTrue === '3' ? 'section_5_text is' : 'section_5_text'" @click="isTrueFn('3')">
          <div class="section_2_text_1">运营管理</div>
          <div class="section_2_text_2">降低管理成本 强化企业风控</div>
          <div v-show="isTrue === '3'" class="xian_1"></div>
        </div>
      </div>
    </div>
    <div v-show="isTrue === '0'" class="section_3_1">
      <div class="section_3_left">
        <div
          :class="barner === '0' ? 'section_3_left_box ber_2' : 'section_3_left_box ber_1'"
          @mouseover="barnerFn('0')"
        >
          <span class="text_1">数据可视化</span>
          <span class="text_2">可视化大屏统计，实时同步、实时更新，便于患者及慢病风险统计。</span>
        </div>
        <div
          :class="barner === '1' ? 'section_4_left_box ber_2' : 'section_4_left_box ber_1'"
          @mouseover="barnerFn('1')"
        >
          <span class="text_1">多端同步</span>
          <span class="text_2">电脑、平板、手机三端数据同步，便于监控患者健康状况。</span>
        </div>
        <div
          :class="barner === '2' ? 'section_5_left_box ber_2' : 'section_5_left_box ber_1'"
          @mouseover="barnerFn('2')"
        >
          <span class="text_1">异常分析</span>
          <span class="text_2">数据异常筛查，异常数据自动标注并实时提示。</span>
        </div>
      </div>
      <div class="section_3_right">
        <img v-show="barner === '0'" src="@/assets/images/barner_1_1.png" alt="" style="border-radius: 0.9375rem" />
        <img v-show="barner === '1'" src="@/assets/images/sange.png" alt="" style="border-radius: 0.9375rem" />
        <img v-show="barner === '2'" src="@/assets/images/barner_1_3.png" alt="" style="border-radius: 0.9375rem" />
      </div>
    </div>
    <div v-show="isTrue === '1'" class="section_3_2">
      <div class="section_3_left">
        <div
          :class="barner === '0' ? 'section_3_left_box ber_2' : 'section_3_left_box ber_1'"
          @mouseover="barnerFn('0')"
        >
          <span class="text_1">智能引领</span>
          <span class="text_2">自动引领患者做相关检查，并将检查数据自动同步至PC端。</span>
        </div>
        <div
          :class="barner === '1' ? 'section_4_left_box ber_2' : 'section_4_left_box ber_1'"
          @mouseover="barnerFn('1')"
        >
          <span class="text_1">快速呼叫</span>
          <span class="text_2_s">当机器人为无人值守模式且检查项需医生辅助时，机器人自动发起远程会诊。</span>
        </div>
        <div
          :class="barner === '2' ? 'section_5_left_box ber_2' : 'section_5_left_box ber_1'"
          @mouseover="barnerFn('2')"
        >
          <span class="text_1">线上医生实时跟踪</span>
          <span class="text_2">线上医生可实时查看检查画面，实时展示机器人状态。</span>
        </div>
      </div>
      <div class="section_3_right">
        <img v-show="barner === '0'" src="@/assets/images/barner_2_1.png" alt="" style="border-radius: 0.9375rem" />
        <img v-show="barner === '1'" src="@/assets/images/barner_2_2.png" alt="" style="border-radius: 0.9375rem" />
        <img v-show="barner === '2'" src="@/assets/images/barner_2_3.png" alt="" style="border-radius: 0.9375rem" />
      </div>
    </div>
    <div v-show="isTrue === '2'" class="section_3_3">
      <div class="section_3_left">
        <div
          :class="barner === '0' ? 'section_3_left_box ber_2' : 'section_3_left_box ber_1'"
          @mouseover="barnerFn('0')"
        >
          <span class="text_1">穿戴日记</span>
          <span class="text_2">穿戴数据实时监测，并自动同步数据至电脑端、平板端和手机端。</span>
        </div>
        <div
          :class="barner === '1' ? 'section_4_left_box ber_2' : 'section_4_left_box ber_1'"
          @mouseover="barnerFn('1')"
        >
          <span class="text_1">智能预警</span>
          <span class="text_2">异常预警，中高风险智能分类，异常数据筛查并标注提示。</span>
        </div>
      </div>
      <div class="section_3_right">
        <img v-show="barner === '0'" src="@/assets/images/barner_3_1.png" alt="" style="border-radius: 0.9375rem" />
        <img v-show="barner === '1'" src="@/assets/images/barner_3_2.png" alt="" style="border-radius: 0.9375rem" />
      </div>
    </div>
    <div v-show="isTrue === '3'" class="section_3_4">
      <div class="section_3_left">
        <div
          :class="barner === '0' ? 'section_3_left_box ber_2' : 'section_3_left_box ber_1'"
          @mouseover="barnerFn('0')"
        >
          <span class="text_1">随访队列配置</span>
          <span class="text_2">队列自定义配置多样化，支持海量策略及问卷管理、报告导出项配置等。</span>
        </div>
        <div
          :class="barner === '1' ? 'section_4_left_box ber_2' : 'section_4_left_box ber_1'"
          @mouseover="barnerFn('1')"
        >
          <span class="text_1">随访表个性化设计</span>
          <span class="text_2">根据不同患者疾病状况，个性化配置多种随访表。</span>
        </div>
      </div>
      <div class="section_3_right">
        <img v-show="barner === '0'" src="@/assets/images/barner_4_1.png" alt="" style="border-radius: 0.9375rem" />
        <img v-show="barner === '1'" src="@/assets/images/barner_4_2.png" alt="" style="border-radius: 0.9375rem" />
      </div>
    </div>
    <div class="section_4">
      <div class="section_4_top">
        <div class="section_4_top_1">系统特色</div>
        <div class="section_4_top_2"></div>
        <div class="section_4_top_3">SYSTEM FEATURES</div>
      </div>
      <div class="section_4_bottom">
        <div class="section_4_bottom_box">
          <img class="section_4_bottom_box_img" alt="" src="@/assets/images/duanxin.png" />
          <div class="section_4_bottom_box_zhi">智能化随访</div>
          <div class="section_4_bottom_box_text">
            通过AI电话自动随访和随访录音等技术手段，实现智能化随访管理，提高工作效率和质量。
          </div>
        </div>
        <div class="section_5_bottom_box">
          <img class="section_4_bottom_box_img" alt="" src="@/assets/images/caidan.png" />
          <div class="section_4_bottom_box_zhi">自定义随访表单</div>
          <div class="section_4_bottom_box_text">
            支持用户自定义随访表单和评分标准设计，满足不同医疗机构的个性化需求。
          </div>
        </div>
        <div class="section_6_bottom_box">
          <img class="section_4_bottom_box_img" alt="" src="@/assets/images/shouji.png" />
          <div class="section_4_bottom_box_zhi">多平台支持</div>
          <div class="section_4_bottom_box_text">支持在不同设备和平台上使用，方便用户随时随地进行管理。</div>
        </div>
        <div class="section_7_bottom_box">
          <img class="section_4_bottom_box_img" alt="" src="@/assets/images/anquan.png" />
          <div class="section_4_bottom_box_zhi">数据安全保障</div>
          <div class="section_4_bottom_box_text">通过本地化部署和权限管理，确保患者数据和隐私的安全。</div>
        </div>
        <div class="section_8_bottom_box">
          <img class="section_4_bottom_box_img" alt="" src="@/assets/images/baibaoxiang.png" />
          <div class="section_4_bottom_box_zhi">国际医学标准问卷模板</div>
          <div class="section_4_bottom_box_text">提供多种国际医学标准问卷模板，方便用户参考和使用。</div>
        </div>
        <div class="section_9_bottom_box">
          <img class="section_4_bottom_box_img" alt="" src="@/assets/images/zhenguan.png" />
          <div class="section_4_bottom_box_zhi">科研支持</div>
          <div class="section_4_bottom_box_text">支持科研数据定制和导出，方便进行科研数据分析和统计。</div>
        </div>
      </div>
    </div>
    <div class="section_5">
      <div class="section_5_top">
        <div class="section_5_top_gong">功能特点</div>
        <div class="section_5_top_xian"></div>
        <div class="section_5_top_zimu">SPECIFICATIONS</div>
      </div>
      <div class="section_5_bottom">
        <div class="section_5_bottom_left"></div>
        <div class="section_5_bottom_right">
          <div class="section_5_bottom_right_text">
            <div class="group_9"></div>
            AI电话自动随访
          </div>
          <div class="section_5_bottom_right_text_1">
            系统可以根据预设的随访计划，自动拨打患者电话进行随访，并记录随访过程和结果。
          </div>
          <div class="section_5_bottom_right_text_2">
            <div class="group_9"></div>
            跨设备、跨平台使用
          </div>
          <div class="section_5_bottom_right_text_3">系统支持在不同设备和平台上使用，如电脑、平板、手机等。</div>
        </div>
      </div>
    </div>
    <div class="section_6">
      <div class="section_6_left">
        <div class="jiji">
          <div class="section_6_left_left">
            <div class="group_10"></div>
            随访录音
          </div>
          <div class="section_6_left_right">系统支持对随访过程进行录音，方便后续回顾和分析。</div>
        </div>
      </div>
      <div class="section_6_right"></div>
    </div>
    <div class="section_7">
      <div class="section_5_bottom">
        <div class="section_5_bottom_left"></div>
        <div class="section_5_bottom_right">
          <div class="section_5_bottom_right_text">
            <div class="group_9"></div>
            CRF表格回填
          </div>
          <div class="section_5_bottom_right_text_1">系统可以根据随访结果自动回填CRF表格，提高数据整理效率。</div>
          <div class="section_5_bottom_right_text_2">
            <div class="group_9"></div>
            自定义随访表单
          </div>
          <div class="section_5_bottom_right_text_3">
            用户可以根据实际需求自定义随访表单，包括表单结构设计、评分标准设计等；系统支持表单中的多选和单选功能，满足用户不同的填写需求。
          </div>
        </div>
      </div>
    </div>
    <div class="section_8">
      <div class="section_5_bottom">
        <div class="section_5_bottom_left">
          <div class="section_5_bottom_right_text">
            <div class="group_9"></div>
            院内HIS对接
          </div>
          <div class="section_5_bottom_right_text_1">系统支持从医疗机构的HIS系统中导入患者信息，避免重复录入。</div>
          <div class="section_5_bottom_right_text_2">
            <div class="group_9"></div>
            科研数据定制和导出
          </div>
          <div class="section_5_bottom_right_text_3">
            用户可以根据科研需求定制数据导出格式，方便进行数据分析和统计。
          </div>
        </div>
        <div class="section_5_bottom_right"></div>
      </div>
    </div>
    <div class="section_9">
      <div class="section_5_bottom">
        <div class="section_5_bottom_left"></div>
        <div class="section_5_bottom_right">
          <div class="section_5_bottom_right_text">
            <div class="group_9"></div>
            内置国际医学标准问卷模板
          </div>
          <div class="section_5_bottom_right_text_1">
            系统内置多种国际医学标准问卷模板，用户可以根据需要进行选择和修改。
          </div>
          <div class="section_5_bottom_right_text_2">
            <div class="group_9"></div>
            支持本地化部署
          </div>
          <div class="section_5_bottom_right_text_3">
            为了满足不同医疗机构的数据安全和隐私保护需求，系统支持本地化部署。
          </div>
        </div>
      </div>
    </div>
    <div class="section_10">
      <div class="section_11_top">
        <div class="text_xi">系统价值</div>
        <div class="text_se"></div>
        <div class="text_zimu">VALUES</div>
      </div>
      <div class="section_11_bootom">
        <div class="xiao_box_1">
          <div class="xiaobox_left">
            <span class="text_44">01</span>
          </div>
          <div class="xiaobox_right">
            <div class="xiaobox_top">全面智能化服务</div>
            <div class="xiaobox_bottom">
              卫软智慧随访系统融合智能提醒、远程监测与数据分析，为用户提供智能化医疗服务，同时为医疗机构创造高效便捷的患者管理流程。
            </div>
          </div>
        </div>
        <div class="xiao_box_2">
          <div class="xiaobox_left">
            <span class="text_44">03</span>
          </div>
          <div class="xiaobox_right">
            <div class="xiaobox_top">个性化健康护航</div>
            <div class="xiaobox_bottom">
              通过数据分析和预测，系统能够为用户提供个性化的健康管理建议，为每位用户量身打造独特的健康护航方案。
            </div>
          </div>
        </div>
        <div class="xiao_box_3">
          <div class="xiaobox_left">
            <span class="text_44">02</span>
          </div>
          <div class="xiaobox_right">
            <div class="xiaobox_top">时效高效的医疗体验</div>
            <div class="xiaobox_bottom">
              智能化的提醒、自动随访简化医疗流程，远程监测与实时数据分析提高了医疗响应的时效性，为用户和医疗机构创造高效的医疗体验。
            </div>
          </div>
        </div>
        <div class="xiao_box_4">
          <div class="xiaobox_left">
            <span class="text_44">04</span>
          </div>
          <div class="xiaobox_right">
            <div class="xiaobox_top">持续关怀与科研支持</div>
            <div class="xiaobox_bottom">
              智慧随访系统实现了持续的医疗关怀和健康管理，同时为医疗机构提供丰富的患者数据，支持科研创新，促进医学进步。
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="section_11">
      <div class="section_11_top">
        <div class="section_11_top_1">应用场景</div>
        <div class="section_11_top_2"></div>
        <div class="section_11_top_3">APPLICATION SCENARIO</div>
      </div>
      <div class="section_11_bottom">
        <div class="section_11_bottom_1">
          <img src="@/assets/images/yiyuan.png" alt="" class="section_11_bottom_1_1" />
          <div class="section_11_bottom_1_2">基层卫生医疗机构</div>
          <div class="section_11_bottom_1_3">医疗机构整合随访，系统统计支持，方便管理</div>
        </div>
        <div class="section_11_bottom_2">
          <img src="@/assets/images/kanbing.png" alt="" class="section_11_bottom_1_1" />
          <div class="section_11_bottom_1_2">智慧随访</div>
          <div class="section_11_bottom_1_3">医院内系统人员进行随访，信息同步，便于统计和人员管理</div>
        </div>
        <div class="section_11_bottom_3">
          <img src="@/assets/images/yanglao.png" alt="" class="section_11_bottom_1_1" />
          <div class="section_11_bottom_1_2">智慧养老</div>
          <div class="section_11_bottom_1_3">针对老年人群进行随访，根据病症有针对性的随访</div>
        </div>
      </div>
    </div>
  </div>
</template>
<style style lang="scss" scoped>
.main_box {
  max-width: 160rem;
  width: 100%;
  position: relative;
  margin: 0 auto;
  margin-top: 3.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow: hidden;
  img {
    width: 100%;
    height: auto;
  }
  .section_1 {
    max-width: 160rem;
    margin: 0 auto;
    width: 100%;
    height: 37.5rem;
    background-image: url('@/assets/images/tingzhen.png');
    background-size: 100% 100%;
    position: relative;
    .section_1box {
      position: absolute;
      top: 11rem;
      right: 10.625rem;
      height: 15.5rem;
      width: 37.5rem;
      .section_1box_top {
        width: 18.75rem;
        font-size: 2.25rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #ffffff;
      }
      .section_1box_bottom {
        width: 37.5rem;
        font-size: 1.25rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #ffffff;
        margin-top: 1.875rem;
      }
    }
  }
  .section_2 {
    margin: 0 auto;
    max-width: 160rem;
    width: 100%;
    height: 19.375rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: white;
    .xian_1 {
      width: 12.5rem;
      height: 0.25rem;
      background: #4865ff;
      border-radius: 0.125rem;
    }
    .section_2_1 {
      font-size: 1.875rem;
      color: #222222;
      height: 3.125rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      margin-top: 4.375rem;
    }
    .section_2_2 {
      width: 3.125rem;
      height: 0.25rem;
      background: #0281e0;
      margin: 0.625rem 0;
    }
    .section_2_3 {
      font-size: 1rem;
      color: #666666;
      height: 1.5625rem;
    }
    .section_2_4 {
      margin-top: 2.5rem;
      display: flex;
      width: 100%;
      height: 6.3125rem;
      justify-content: center;
      .is {
        .section_2_text_1 {
          color: #4865ff !important;
        }
        .section_2_text_2 {
          color: #4865ff !important;
        }
      }
      .section_2_text {
        cursor: pointer;
        display: flex;
        width: 23.125rem;
        flex-direction: column;
        align-items: center;
        .section_2_text_1 {
          font-size: 1.5rem;
          height: 2.0625rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #222222;
          margin-bottom: 0.375rem;
          margin-top: 1.25rem;
        }
        .section_2_text_2 {
          height: 1.375rem;
          font-size: 1rem;
          font-weight: 400;
          font-family: PingFangSC, PingFang SC;
          color: #666666;
          margin-bottom: 1rem;
        }
      }
      .section_3_text {
        cursor: pointer;
        width: 23.125rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        .section_2_text_1 {
          font-size: 1.5rem;
          height: 2.0625rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #222222;
          margin-bottom: 0.375rem;
          margin-top: 1.25rem;
        }
        .section_2_text_2 {
          font-size: 1rem;
          height: 1.375rem;
          font-weight: 400;
          font-family: PingFangSC, PingFang SC;
          color: #666666;
          margin-bottom: 1rem;
        }
      }
      .section_4_text {
        cursor: pointer;
        width: 23.125rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        .section_2_text_1 {
          font-size: 1.5rem;
          height: 2.0625rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #222222;
          margin-top: 1.25rem;
          margin-bottom: 0.375rem;
        }
        .section_2_text_2 {
          font-size: 1rem;
          height: 1.375rem;
          font-weight: 400;
          font-family: PingFangSC, PingFang SC;
          color: #666666;
          margin-bottom: 1rem;
        }
      }
      .section_5_text {
        cursor: pointer;
        width: 23.125rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        .section_2_text_1 {
          font-size: 1.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #222222;
          height: 2.0625rem;
          margin-bottom: 0.375rem;
          margin-top: 1.25rem;
        }
        .section_2_text_2 {
          font-size: 1rem;
          font-weight: 400;
          font-family: PingFangSC, PingFang SC;
          color: #666666;
          margin-bottom: 1rem;
          height: 1.375rem;
        }
      }
    }
  }
  .section_3_1 {
    width: 100%;
    margin: 0 auto;
    max-width: 160rem;
    height: 40.625rem;
    background: #f8f8f8;
    display: flex;
    justify-content: center;
    .section_3_left {
      width: 25rem;
      display: flex;
      flex-direction: column;
      margin: 2.5rem 0 0 0;
      overflow: hidden;
      .ber_1 {
        background: #ffffff;
        .text_1 {
          color: #222222;
        }
        .text_2 {
          color: #666666;
        }
      }
      .ber_2 {
        background: #4865ff;
        .text_1 {
          color: #ffffff !important;
        }
        .text_2 {
          color: #ffffff !important;
        }
      }
      .section_3_left_box {
        height: 10rem;
        display: flex;
        flex-direction: column;
        box-shadow: 0 0 0.4375rem 0 rgba(0, 0, 0, 0.15);
        border-radius: 0.9375rem;
        .text_1 {
          font-size: 1.5rem;
          height: 2.0625rem;
          color: #222222;
          font-weight: 500;
          font-family: PingFangSC, PingFang SC;
          margin: 2.125rem 15rem 0.9375rem 2.5rem;
        }
        .text_2 {
          font-size: 1rem;
          color: #666666;
          width: 20rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          margin: 0 2.5rem 2.125rem 2.5rem;
        }
      }
      .section_4_left_box {
        height: 10rem;
        display: flex;
        flex-direction: column;
        box-shadow: 0 0 0.4375rem 0 rgba(0, 0, 0, 0.15);
        border-radius: 0.9375rem;
        margin: 1.4375rem 0;
        .text_1 {
          font-size: 1.5rem;
          color: #000000;
          margin: 2.125rem 15rem 0.9375rem 2.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          height: 2.0625rem;
        }
        .text_2 {
          font-size: 1rem;
          color: #666666;
          width: 20rem;
          font-weight: 400;
          margin: 0 2.5rem 2.125rem 2.5rem;
          font-family: PingFangSC, PingFang SC;
        }
      }
      .section_5_left_box {
        height: 10rem;
        display: flex;
        flex-direction: column;
        box-shadow: 0 0 0.4375rem 0 rgba(0, 0, 0, 0.15);
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        border-radius: 0.9375rem;
        .text_1 {
          font-size: 1.5rem;
          color: #222222;
          margin: 2.125rem 15rem 0.9375rem 2.5rem;
          font-family: PingFangSC, PingFang SC;
          height: 2.0625rem;
        }
        .text_2 {
          font-size: 1rem;
          color: #666666;
          width: 20rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          margin: 0 2.5rem 2.125rem 2.5rem;
        }
      }
    }
    .section_3_right {
      width: 48.125rem;
      height: 33.75rem;
      margin: 2.5rem 0 0 4.375rem;
      background-size: 100% 100%;
    }
  }
  .section_3_2 {
    width: 100%;
    margin: 0 auto;
    max-width: 160rem;
    height: 40.625rem;
    background: #f8f8f8;
    display: flex;
    justify-content: center;
    .section_3_left {
      width: 25rem;
      display: flex;
      flex-direction: column;
      margin: 2.5rem 0 0 0;
      overflow: hidden;
      .ber_1 {
        background: #ffffff;
        .text_1 {
          color: #222222;
        }
        .text_2 {
          color: #666666;
        }
      }
      .ber_2 {
        background: #4865ff;
        .text_1 {
          color: #ffffff !important;
        }
        .text_2 {
          color: #ffffff !important;
        }
        .text_2_s {
          color: #ffffff !important;
        }
      }
      .section_3_left_box {
        height: 10rem;
        display: flex;
        flex-direction: column;
        box-shadow: 0 0 0.4375rem 0 rgba(0, 0, 0, 0.15);
        border-radius: 0.9375rem;
        .text_1 {
          font-size: 1.5rem;
          color: #222222;
          font-weight: 500;
          font-family: PingFangSC, PingFang SC;
          margin: 2.125rem 15rem 0.9375rem 2.5rem;
          height: 2.0625rem;
        }
        .text_2 {
          font-size: 1rem;
          color: #666666;
          width: 20rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          margin: 0 2.5rem 2.125rem 2.5rem;
        }
      }
      .section_4_left_box {
        height: 10rem;
        display: flex;
        flex-direction: column;
        font-family: PingFangSC, PingFang SC;
        box-shadow: 0 0 0.4375rem 0 rgba(0, 0, 0, 0.15);
        border-radius: 0.9375rem;
        margin: 1.4375rem 0;
        .text_1 {
          font-size: 1.5rem;
          color: #000000;
          margin: 2.125rem 15rem 0.9375rem 2.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          height: 2.0625rem;
        }
        .text_2 {
          font-size: 1rem;
          color: #666666;
          width: 20rem;
          font-weight: 400;
          margin: 0 2.5rem 2.125rem 2.5rem;
          font-family: PingFangSC, PingFang SC;
        }
        .text_2_s {
          height: 4.125rem;
          font-size: 1rem;
          font-weight: 400;
          color: #666666;
          margin: 0 0 2.25rem 2.5rem;
        }
      }
      .section_5_left_box {
        height: 10rem;
        display: flex;
        flex-direction: column;
        box-shadow: 0 0 0.4375rem 0 rgba(0, 0, 0, 0.15);
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        border-radius: 0.9375rem;
        .text_1 {
          font-size: 1.5rem;
          color: #222222;
          margin: 2.125rem 10.5rem 0.9375rem 2.5rem;
          font-family: PingFangSC, PingFang SC;
          height: 2.0625rem;
        }
        .text_2 {
          font-size: 1rem;
          color: #666666;
          width: 20rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          margin: 0 2.5rem 2.125rem 2.5rem;
        }
      }
    }
    .section_3_right {
      width: 48.125rem;
      height: 33.75rem;
      margin: 2.5rem 0 0 4.375rem;
      background-size: 100% 100%;
    }
  }
  .section_3_3 {
    width: 100%;
    margin: 0 auto;
    max-width: 160rem;
    height: 40.625rem;
    background: #f8f8f8;
    display: flex;
    justify-content: center;
    .section_3_left {
      width: 25rem;
      display: flex;
      flex-direction: column;
      margin: 2.5rem 0 0 0;
      overflow: hidden;
      .ber_1 {
        background: #ffffff;
        .text_1 {
          color: #222222;
        }
        .text_2 {
          color: #666666;
        }
      }
      .ber_2 {
        background: #4865ff;
        .text_1 {
          color: #ffffff !important;
        }
        .text_2 {
          color: #ffffff !important;
        }
      }
      .section_3_left_box {
        height: 10rem;
        display: flex;
        flex-direction: column;
        box-shadow: 0 0 0.4375rem 0 rgba(0, 0, 0, 0.15);
        border-radius: 0.9375rem;
        .text_1 {
          font-size: 1.5rem;
          color: #222222;
          font-weight: 500;
          font-family: PingFangSC, PingFang SC;
          margin: 2.125rem 15rem 0.9375rem 2.5rem;
          height: 2.0625rem;
        }
        .text_2 {
          font-size: 1rem;
          color: #666666;
          width: 20rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          margin: 0 2.5rem 2.125rem 2.5rem;
        }
      }
      .section_4_left_box {
        height: 10rem;
        display: flex;
        flex-direction: column;
        box-shadow: 0 0 0.4375rem 0 rgba(0, 0, 0, 0.15);
        border-radius: 0.9375rem;
        margin: 1.4375rem 0;
        .text_1 {
          font-size: 1.5rem;
          color: #222222;
          font-weight: 500;
          font-family: PingFangSC, PingFang SC;
          margin: 2.125rem 15rem 0.9375rem 2.5rem;
          height: 2.0625rem;
        }
        .text_2 {
          font-size: 1rem;
          color: #666666;
          width: 20rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          margin: 0 2.5rem 2.125rem 2.5rem;
        }
      }
    }
    .section_3_right {
      width: 48.125rem;
      height: 33.75rem;
      margin: 2.5rem 0 0 4.375rem;
      background-size: 100% 100%;
    }
  }
  .section_3_4 {
    width: 100%;
    margin: 0 auto;
    max-width: 160rem;
    height: 40.625rem;
    background: #f8f8f8;
    display: flex;
    justify-content: center;
    .section_3_left {
      width: 25rem;
      display: flex;
      flex-direction: column;
      margin: 2.5rem 0 0 0;
      overflow: hidden;
      .ber_1 {
        background: #ffffff;
        .text_1 {
          color: #222222;
        }
        .text_2 {
          color: #666666;
        }
      }
      .ber_2 {
        background: #4865ff;
        .text_1 {
          color: #ffffff !important;
        }
        .text_2 {
          color: #ffffff !important;
        }
      }
      .section_3_left_box {
        height: 10rem;
        display: flex;
        flex-direction: column;
        box-shadow: 0 0 0.4375rem 0 rgba(0, 0, 0, 0.15);
        border-radius: 0.9375rem;
        .text_1 {
          font-size: 1.5rem;
          color: #222222;
          font-weight: 500;
          font-family: PingFangSC, PingFang SC;
          margin: 2.125rem 13.5rem 0.9375rem 2.5rem;
          height: 2.0625rem;
        }
        .text_2 {
          font-size: 1rem;
          color: #666666;
          width: 20rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          margin: 0 2.5rem 2.125rem 2.5rem;
        }
      }
      .section_4_left_box {
        height: 10rem;
        display: flex;
        flex-direction: column;
        box-shadow: 0 0 0.4375rem 0 rgba(0, 0, 0, 0.15);
        border-radius: 0.9375rem;
        margin: 1.4375rem 0;
        .text_1 {
          font-size: 1.5rem;
          color: #222222;
          font-weight: 500;
          font-family: PingFangSC, PingFang SC;
          margin: 2.125rem 10.5rem 0.9375rem 2.5rem;
          height: 2.0625rem;
        }
        .text_2 {
          font-size: 1rem;
          color: #666666;
          width: 20rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          margin: 0 2.5rem 2.125rem 2.5rem;
        }
      }
    }
    .section_3_right {
      width: 48.125rem;
      height: 33.75rem;
      margin: 2.5rem 0 0 4.375rem;
      background-size: 100% 100%;
    }
  }
  .section_4 {
    width: 100%;
    margin: 0 auto;
    max-width: 160rem;
    height: 48.6875rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    background-image: url('@/assets/images/xitebgr.png');
    background-size: 100% 100%;
    .section_4_top {
      display: flex;
      flex-direction: column;
      align-items: center;
      .section_4_top_1 {
        color: #222222;
        font-size: 1.875rem;
        margin-top: 4.375rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
      }
      .section_4_top_2 {
        width: 3.125rem;
        height: 0.25rem;
        background: #0281e0;
        margin: 0.625rem 0;
      }
      .section_4_top_3 {
        color: #666666;
        font-size: 1.125rem;
        font-family: PingFangSC, PingFang SC;
      }
    }
    .section_4_bottom {
      width: 80rem;
      height: 31.25rem;
      display: flex;
      flex-wrap: wrap;
      margin-top: 2.5rem;
      overflow: hidden;
      .section_4_bottom_box {
        width: 25rem;
        height: 14.375rem;
        background: #ffffff;
        border-radius: 0.9375rem;
        display: flex;
        flex-direction: column;
        .section_4_bottom_box_img {
          width: 3.125rem;
          height: 3.125rem;
          margin: 2.125rem 0 0 1.875rem;
        }
        .section_4_bottom_box_zhi {
          color: #222222;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 1.5rem;
          margin: 1.25rem 0 0.9375rem 1.875rem;
        }
        .section_4_bottom_box_text {
          font-size: 1rem;
          color: #666666;
          width: 21.25rem;
          margin-left: 1.875rem;
        }
      }
      .section_5_bottom_box {
        width: 25rem;
        height: 14.375rem;
        background: #ffffff;
        border-radius: 0.9375rem;
        display: flex;
        flex-direction: column;
        margin: 0 2.5rem 2.5rem 2.5rem;
        .section_4_bottom_box_img {
          width: 3.125rem;
          height: 3.125rem;
          margin: 2.125rem 0 0 1.875rem;
        }
        .section_4_bottom_box_zhi {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #222222;
          font-size: 1.5rem;
          margin: 1.25rem 0 0.9375rem 1.875rem;
        }
        .section_4_bottom_box_text {
          font-size: 1rem;
          color: #666666;
          width: 21.25rem;
          margin-left: 1.875rem;
        }
      }
      .section_6_bottom_box {
        width: 25rem;
        height: 14.375rem;
        background: #ffffff;
        border-radius: 0.9375rem;
        display: flex;
        flex-direction: column;
        .section_4_bottom_box_img {
          width: 3.125rem;
          height: 3.125rem;
          margin: 2.125rem 0 0 1.875rem;
        }
        .section_4_bottom_box_zhi {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #222222;
          font-size: 1.5rem;
          margin: 1.25rem 0 0.9375rem 1.875rem;
        }
        .section_4_bottom_box_text {
          font-size: 1rem;
          color: #666666;
          width: 21.25rem;
          margin-left: 1.875rem;
        }
      }
      .section_7_bottom_box {
        width: 25rem;
        height: 14.375rem;
        background: #ffffff;
        border-radius: 0.9375rem;
        display: flex;
        flex-direction: column;
        .section_4_bottom_box_img {
          width: 3.125rem;
          height: 3.125rem;
          margin: 2.125rem 0 0 1.875rem;
        }
        .section_4_bottom_box_zhi {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #222222;
          font-size: 1.5rem;
          margin: 1.25rem 0 0.9375rem 1.875rem;
        }
        .section_4_bottom_box_text {
          font-size: 1rem;
          color: #666666;
          width: 21.25rem;
          margin-left: 1.875rem;
        }
      }
      .section_8_bottom_box {
        width: 25rem;
        height: 14.375rem;
        background: #ffffff;
        border-radius: 0.9375rem;
        display: flex;
        flex-direction: column;
        margin: 0 2.5rem;
        .section_4_bottom_box_img {
          width: 3.125rem;
          height: 3.125rem;
          margin: 2.125rem 0 0 1.875rem;
        }
        .section_4_bottom_box_zhi {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #222222;
          font-size: 1.5rem;
          margin: 1.25rem 0 0.9375rem 1.875rem;
        }
        .section_4_bottom_box_text {
          font-size: 1rem;
          color: #666666;
          width: 21.25rem;
          margin-left: 1.875rem;
        }
      }
      .section_9_bottom_box {
        width: 25rem;
        height: 14.375rem;
        background: #ffffff;
        border-radius: 0.9375rem;
        display: flex;
        flex-direction: column;
        .section_4_bottom_box_img {
          width: 3.125rem;
          height: 3.125rem;
          margin: 2.125rem 0 0 1.875rem;
        }
        .section_4_bottom_box_zhi {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #222222;
          font-size: 1.5rem;
          margin: 1.25rem 0 0.9375rem 1.875rem;
        }
        .section_4_bottom_box_text {
          font-size: 1rem;
          color: #666666;
          width: 21.25rem;
          margin-left: 1.875rem;
        }
      }
    }
  }
  .section_5 {
    margin: 0 auto;
    max-width: 160rem;
    width: 100%;
    height: 42.4375rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    background: #ffffff;
    .section_5_top {
      display: flex;
      flex-direction: column;
      align-items: center;
      .section_5_top_gong {
        color: #222222;
        font-size: 1.875rem;
        margin-top: 4.375rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
      }
      .section_5_top_xian {
        width: 3.125rem;
        height: 0.25rem;
        background: #0281e0;
        margin: 0.625rem 0;
      }
      .section_5_top_zimu {
        color: #666666;
        font-size: 1rem;
        font-family: PingFangSC, PingFang SC;
      }
    }
    .section_5_bottom {
      width: 91.875rem;
      height: 21.25rem;
      display: flex;
      margin-top: 2.5rem;
      justify-content: center;
      .section_5_bottom_left {
        width: 37.5rem;
        height: 25rem;
        background-image: url('@/assets/images/xiangjiphone.png');
        background-size: 100% 100%;
      }
      .section_5_bottom_right {
        .section_5_bottom_right_text {
          display: flex;
          align-items: center;
          margin: 5.875rem 0 0 4.375rem;
          font-size: 1.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #222222;
          .group_9 {
            background-color: rgba(2, 129, 224, 1);
            border-radius: 0.125rem;
            width: 0.25rem;
            height: 1.25rem;
            margin-right: 0.9375rem;
          }
        }
        .section_5_bottom_right_text_1 {
          width: 37.5rem;
          color: #666666;
          font-size: 1rem;
          margin: 1.25rem 0 0 5.5625rem;
          font-family: PingFangSC, PingFang SC;
        }
        .section_5_bottom_right_text_2 {
          display: flex;
          align-items: center;
          margin: 2.5rem 0 0 4.375rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 1.5rem;
          color: #222222;
          .group_9 {
            background-color: rgba(2, 129, 224, 1);
            border-radius: 0.125rem;
            width: 0.25rem;
            height: 1.25rem;
            margin-right: 0.9375rem;
          }
        }
        .section_5_bottom_right_text_3 {
          width: 37.5rem;
          color: #666666;
          font-size: 1rem;
          margin: 1.25rem 0 0 5.5625rem;
          font-family: PingFangSC, PingFang SC;
        }
      }
    }
  }
  .section_6 {
    margin: 0 auto;
    max-width: 160rem;
    width: 100%;
    height: 33.75rem;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #f3f7ff;
    .section_6_left {
      width: 43.0625rem;
      height: 25rem;
      display: flex;
      flex-direction: column;
      .jiji {
        display: flex;
        flex-direction: column;
        justify-content: center;
        height: 100%;
        .section_6_left_left {
          color: #222222;
          font-size: 1.5rem;
          display: flex;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          align-items: center;
          .group_10 {
            background-color: rgba(2, 129, 224, 1);
            border-radius: 0.125rem;
            width: 0.25rem;
            height: 1.25rem;
            margin-right: 0.9375rem;
          }
        }
        .section_6_left_right {
          font-size: 1rem;
          color: #666666;
          margin-top: 1.25rem;
          font-family: PingFangSC, PingFang SC;
          margin-left: 1.1875rem;
        }
      }
    }
    .section_6_right {
      width: 37.5rem;
      height: 25rem;
      background-image: url('@/assets/images/luyin.png');
      background-size: 100% 100%;
    }
  }
  .section_7 {
    margin: 0 auto;
    max-width: 160rem;
    width: 100%;
    height: 33.75rem;
    display: flex;
    justify-content: center;
    background: #ffffff;
    align-items: center;
    .section_5_bottom {
      width: 80.5625rem;
      height: 25rem;
      display: flex;
      .section_5_bottom_left {
        width: 37.5rem;
        height: 25rem;
        background-image: url('@/assets/images/crf.png');
        background-size: 100% 100%;
      }
      .section_5_bottom_right {
        .section_5_bottom_right_text {
          display: flex;
          align-items: center;
          margin: 6.0625rem 0 0 5rem;
          font-size: 1.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #222222;
          .group_9 {
            background-color: rgba(2, 129, 224, 1);
            border-radius: 0.125rem;
            width: 0.25rem;
            height: 1.25rem;
            margin-right: 0.9375rem;
          }
        }
        .section_5_bottom_right_text_1 {
          width: 37.5rem;
          color: #666666;
          font-size: 1rem;
          margin: 0.9375rem 0 0 6.1875rem;
          font-family: PingFangSC, PingFang SC;
        }
        .section_5_bottom_right_text_2 {
          display: flex;
          align-items: center;
          margin: 2.5rem 0 0 5rem;
          font-size: 1.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #222222;
          .group_9 {
            background-color: rgba(2, 129, 224, 1);
            border-radius: 0.125rem;
            width: 0.25rem;
            height: 1.25rem;
            margin-right: 0.9375rem;
          }
        }
        .section_5_bottom_right_text_3 {
          width: 37.5rem;
          color: #666666;
          font-size: 1rem;
          margin: 1.25rem 0 0 6.1875rem;
          font-family: PingFangSC, PingFang SC;
        }
      }
    }
  }
  .section_8 {
    margin: 0 auto;
    max-width: 160rem;
    width: 100%;
    height: 33.75rem;
    display: flex;
    justify-content: center;
    align-items: center;
    .section_5_bottom {
      width: 80.5625rem;
      height: 25rem;
      display: flex;
      .section_5_bottom_left {
        flex: 1;
        .section_5_bottom_right_text {
          display: flex;
          align-items: center;
          margin: 6.0625rem 0 0 0;
          font-size: 1.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #222222;
          .group_9 {
            background-color: rgba(2, 129, 224, 1);
            border-radius: 0.125rem;
            width: 0.25rem;
            height: 1.25rem;
            margin-right: 0.9375rem;
          }
        }
        .section_5_bottom_right_text_1 {
          width: 37.5rem;
          color: #666666;
          font-size: 1rem;
          margin: 0.9375rem 0 0 1.1875rem;
          font-family: PingFangSC, PingFang SC;
        }
        .section_5_bottom_right_text_2 {
          display: flex;
          align-items: center;
          margin: 2.5rem 0 0 0;
          font-size: 1.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #222222;
          .group_9 {
            background-color: rgba(2, 129, 224, 1);
            border-radius: 0.125rem;
            width: 0.25rem;
            height: 1.25rem;
            margin-right: 0.9375rem;
          }
        }
        .section_5_bottom_right_text_3 {
          width: 37.5rem;
          color: #666666;
          font-size: 1rem;
          margin: 1.25rem 0 0 1.1875rem;
          font-family: PingFangSC, PingFang SC;
        }
      }
      .section_5_bottom_right {
        width: 37.5rem;
        height: 25rem;
        background-image: url('@/assets/images/his.png');
        background-size: 100% 100%;
      }
    }
  }
  .section_9 {
    margin: 0 auto;
    max-width: 160rem;
    width: 100%;
    height: 33.75rem;
    display: flex;
    justify-content: center;
    background-color: #ffffff;
    align-items: center;
    .section_5_bottom {
      width: 80.5625rem;
      height: 25rem;
      display: flex;
      .section_5_bottom_left {
        width: 37.5rem;
        height: 25rem;
        background-image: url('@/assets/images/tabtu.png');
        background-size: 100% 100%;
      }
      .section_5_bottom_right {
        .section_5_bottom_right_text {
          display: flex;
          align-items: center;
          margin: 6.0625rem 0 0 5rem;
          font-size: 1.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #222222;
          .group_9 {
            background-color: rgba(2, 129, 224, 1);
            border-radius: 0.125rem;
            width: 0.25rem;
            height: 1.25rem;
            margin-right: 0.9375rem;
          }
        }
        .section_5_bottom_right_text_1 {
          width: 37.5rem;
          color: #666666;
          font-size: 1rem;
          margin: 0.9375rem 0 0 6.1875rem;
          font-family: PingFangSC, PingFang SC;
        }
        .section_5_bottom_right_text_2 {
          display: flex;
          align-items: center;
          margin: 2.5rem 0 0 5rem;
          font-size: 1.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #222222;
          .group_9 {
            background-color: rgba(2, 129, 224, 1);
            border-radius: 0.125rem;
            width: 0.25rem;
            height: 1.25rem;
            margin-right: 0.9375rem;
          }
        }
        .section_5_bottom_right_text_3 {
          width: 37.5rem;
          color: #666666;
          font-size: 1rem;
          margin: 1.25rem 0 0 6.1875rem;
          font-family: PingFangSC, PingFang SC;
        }
      }
    }
  }
  .section_10 {
    margin: 0 auto;
    max-width: 160rem;
    width: 100%;
    height: 30.8125rem;
    background-image: url('@/assets/images/bg1.png');
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    .section_11_top {
      height: 6.5rem;
      margin-top: 4.375rem;
      display: flex;
      flex-direction: column;
      align-items: center;
      .text_xi {
        color: #222222;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 1.875rem;
      }
      .text_se {
        width: 3.125rem;
        height: 0.25rem;
        background-color: #0281e0;
        margin: 0.625rem 0;
      }
      .text_zimu {
        font-size: 1rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #666666;
      }
    }
    .section_11_bootom {
      width: 78.125rem;
      height: 13.375rem;
      margin-top: 2.5rem;
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      .xiao_box_1 {
        display: flex;
        width: 36.875rem;
        height: 5.4375rem;
        margin-right: 4.375rem;
        margin-bottom: 2.5rem;
        .xiaobox_left {
          height: 4.375rem;
          background: url('@/assets/images/fangbian.png');
          background-size: 100% 100%;
          width: 4.375rem;
          display: flex;
          flex-direction: column;
          .text_44 {
            width: 2.5625rem;
            height: 3.5rem;
            color: #ffffff;
            font-size: 2.5rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            line-height: 4.375rem;
            margin-left: 0.75rem;
          }
        }
        .xiaobox_right {
          width: 31.25rem;
          display: flex;
          flex-direction: column;
          margin-left: 1.25rem;
          .xiaobox_top {
            margin-bottom: 0.625rem;
            color: #222222;
            font-size: 1.5rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
          }
          .xiaobox_bottom {
            width: 31.25rem;
            color: #222222;
            font-size: 1rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
          }
        }
      }
      .xiao_box_2 {
        display: flex;
        width: 36.875rem;
        height: 5.4375rem;
        margin-bottom: 2.5rem;
        .xiaobox_left {
          height: 4.375rem;
          background: url('@/assets/images/fangbian.png');
          background-size: 100% 100%;
          width: 4.375rem;
          display: flex;
          flex-direction: column;
          .text_44 {
            width: 2.5625rem;
            height: 3.5rem;
            color: #ffffff;
            font-size: 2.5rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            line-height: 4.375rem;
            margin-left: 0.75rem;
          }
        }
        .xiaobox_right {
          width: 31.25rem;
          display: flex;
          flex-direction: column;
          margin-left: 1.25rem;
          .xiaobox_top {
            margin-bottom: 0.625rem;
            color: #222222;
            font-size: 1.5rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
          }
          .xiaobox_bottom {
            width: 31.25rem;
            color: #222222;
            font-size: 1rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
          }
        }
      }
      .xiao_box_3 {
        display: flex;
        width: 36.875rem;
        height: 5.4375rem;
        margin-right: 4.375rem;
        .xiaobox_left {
          height: 4.375rem;
          background: url('@/assets/images/fangbian.png');
          background-size: 100% 100%;
          width: 4.375rem;
          display: flex;
          flex-direction: column;
          .text_44 {
            width: 2.5625rem;
            height: 3.5rem;
            color: #ffffff;
            font-size: 2.5rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            line-height: 4.375rem;
            margin-left: 0.75rem;
          }
        }
        .xiaobox_right {
          width: 31.25rem;
          display: flex;
          flex-direction: column;
          margin-left: 1.25rem;
          .xiaobox_top {
            margin-bottom: 0.625rem;
            color: #222222;
            font-size: 1.5rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
          }
          .xiaobox_bottom {
            width: 31.25rem;
            color: #222222;
            font-size: 1rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
          }
        }
      }
      .xiao_box_4 {
        display: flex;
        width: 36.875rem;
        height: 5.4375rem;
        .xiaobox_left {
          height: 4.375rem;
          background: url('@/assets/images/fangbian.png');
          background-size: 100% 100%;
          width: 4.375rem;
          display: flex;
          flex-direction: column;
          .text_44 {
            width: 2.5625rem;
            height: 3.5rem;
            color: #ffffff;
            font-size: 2.5rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            line-height: 4.375rem;
            margin-left: 0.75rem;
          }
        }
        .xiaobox_right {
          width: 31.25rem;
          display: flex;
          flex-direction: column;
          margin-left: 1.25rem;
          .xiaobox_top {
            margin-bottom: 0.625rem;
            color: #222222;
            font-size: 1.5rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
          }
          .xiaobox_bottom {
            width: 31.25rem;
            color: #222222;
            font-size: 1rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
          }
        }
      }
    }
  }
  .section_11 {
    margin: 0 auto;
    max-width: 160rem;
    width: 100%;
    height: 46.8125rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: #f2f8ff;
    .section_11_top {
      display: flex;
      flex-direction: column;
      align-items: center;
      .section_11_top_1 {
        color: #222222;
        font-size: 1.875rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        margin-top: 4.375rem;
      }
      .section_11_top_2 {
        width: 3.125rem;
        height: 0.25rem;
        background: #0281e0;
        margin: 0.625rem 0;
      }
      .section_11_top_3 {
        color: #666666;
        font-size: 1rem;
      }
    }
    .section_11_bottom {
      width: 104.375rem;
      height: 29.375rem;
      display: flex;
      margin-top: 2.5rem;
      img {
        width: 33.125rem;
        height: 21.875rem;
      }
      .section_11_bottom_1 {
        width: 33.125rem;
        height: 29.375rem;
        display: flex;
        flex-direction: column;
        background-color: white;
        border-radius: 0.9375rem;
        .section_11_bottom_1_1 {
          width: 33.125rem;
          height: 21.875rem;
          border-top-left-radius: 0.9375rem;
          border-top-right-radius: 0.9375rem;
        }
        .section_11_bottom_1_2 {
          color: #222222;
          font-size: 1.5rem;
          height: 2.0625rem;
          margin: 1.25rem 0 0.9375rem 1.875rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
        }
        .section_11_bottom_1_3 {
          width: 28.75rem;
          height: 1.375rem;
          color: #666666;
          font-size: 1rem;
          margin-left: 1.875rem;
          margin-bottom: 1.875rem;
        }
      }
      .section_11_bottom_2 {
        width: 33.125rem;
        height: 29.375rem;
        display: flex;
        flex-direction: column;
        background-color: white;
        margin: 0 2.5rem;
        border-radius: 0.9375rem;
        .section_11_bottom_1_1 {
          width: 33.125rem;
          height: 21.875rem;
          border-top-left-radius: 0.9375rem;
          border-top-right-radius: 0.9375rem;
        }
        .section_11_bottom_1_2 {
          color: #222222;
          font-size: 1.5rem;
          margin: 1.25rem 0 0.9375rem 1.875rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          height: 2.0625rem;
        }
        .section_11_bottom_1_3 {
          width: 28.75rem;
          color: #666666;
          font-size: 1rem;
          margin-left: 1.875rem;
          margin-bottom: 1.875rem;
        }
      }
      .section_11_bottom_3 {
        width: 33.125rem;
        display: flex;
        flex-direction: column;
        background-color: white;
        border-radius: 0.9375rem;
        .section_11_bottom_1_1 {
          width: 33.125rem;
          height: 21.875rem;
          border-top-left-radius: 0.9375rem;
          border-top-right-radius: 0.9375rem;
        }
        .section_11_bottom_1_2 {
          color: #222222;
          font-size: 1.5rem;
          margin: 1.25rem 0 0.9375rem 1.875rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          height: 2.0625rem;
        }
        .section_11_bottom_1_3 {
          width: 28.75rem;
          color: #666666;
          font-size: 0.9375rem;
          margin-left: 1.875rem;
          margin-bottom: 1.875rem;
        }
      }
    }
  }
}
</style>
