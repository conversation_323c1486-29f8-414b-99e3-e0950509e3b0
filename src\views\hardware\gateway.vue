<script setup>
import { ref } from 'vue'
const isTrue = ref(true)
const isTrues = ref(true)
const active = ref(1)
const clickFun = () => {
  isTrue.value = !isTrue.value
}
const clickFuns = () => {
  isTrues.value = !isTrues.value
}
const ActiveFuns = val => {
  active.value = val
}
const ActiveFun = index => {
  if (index === '加') {
    if (active.value === 5) {
      active.value = 1
    } else {
      active.value += 1
    }
  } else {
    if (active.value === 1) {
      active.value = 5
    } else {
      active.value -= 1
    }
  }
}
</script>

<template>
  <div class="banner">
    <div class="section_1">
      <div class="section_1_box">
        <div class="box_1">边缘计算网关</div>
        <div class="box_2">设备联网 安全可靠</div>
      </div>
    </div>
    <div class="section_2">
      <span class="text_10">智能硬件产品助您超越所能</span>
      <div class="section_6"></div>
      <span class="text_11">INTELLIGENT&nbsp;HARDWARE</span>
      <div v-show="active === 1" class="list_3">
        <div class="text-group_1-0" @click="ActiveFuns(1)">
          <span class="text_12-0">边缘计算网关</span>
          <span class="text_13-0">万物互联 实时监控</span>
          <img
            class="image_4-0"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng3b9a8121db0a1bcc7c1a65f5a79361a0bbbf877f3a659cc948ad087a9b9f03cb"
          />
        </div>
        <div class="text-group_1-1" @click="ActiveFuns(2)">
          <span class="text_12-1">AI基层辅助机器人</span>
          <span class="text_13-1">人脸识别 自助问诊 远程诊断</span>
          <img
            class="image_2-1"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
        <div class="text-group_1-2" @click="ActiveFuns(3)">
          <span class="text_12-2">卫软穿戴设备</span>
          <span class="text_13-2">实时监测 安心保障</span>
          <img
            class="image_2-2"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
          <img
            class="image_3-2"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
        <div class="text-group_1-3" @click="ActiveFuns(4)">
          <span class="text_12-3">健康检测一体机</span>
          <span class="text_13-3">智慧体检 一机搞定</span>
          <img
            class="image_3-3"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
      </div>
      <div v-show="active === 2" class="list_3">
        <div class="text-group_1-0" @click="ActiveFuns(2)">
          <span class="text_12-0">AI基层辅助机器人</span>
          <span class="text_13-0">人脸识别 自助问诊 远程诊断</span>
          <img
            class="image_4-0"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng3b9a8121db0a1bcc7c1a65f5a79361a0bbbf877f3a659cc948ad087a9b9f03cb"
          />
        </div>
        <div class="text-group_1-1" @click="ActiveFuns(3)">
          <span class="text_12-1">卫软穿戴设备</span>
          <span class="text_13-1">实时监测 安心保障</span>
          <img
            class="image_2-1"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
        <div class="text-group_1-2" @click="ActiveFuns(4)">
          <span class="text_12-2">健康检测一体机</span>
          <span class="text_13-2">智慧体检 一机搞定</span>
          <img
            class="image_2-2"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
          <img
            class="image_3-2"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
        <div class="text-group_1-3" @click="ActiveFuns(5)">
          <span class="text_12-3">智慧随访包</span>
          <span class="text_13-3">一机多用 实时上传</span>
          <img
            class="image_3-3"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
      </div>
      <div v-show="active === 3" class="list_3">
        <div class="text-group_1-0" @click="ActiveFuns(3)">
          <span class="text_12-0">卫软穿戴设备</span>
          <span class="text_13-0">实时监测&nbsp;安心保障</span>
          <img
            class="image_4-0"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng3b9a8121db0a1bcc7c1a65f5a79361a0bbbf877f3a659cc948ad087a9b9f03cb"
          />
        </div>
        <div class="text-group_1-1" @click="ActiveFuns(4)">
          <span class="text_12-1">健康检测一体机</span>
          <span class="text_13-1">智慧体检&nbsp;一机搞定</span>
          <img
            class="image_2-1"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
        <div class="text-group_1-2" @click="ActiveFuns(5)">
          <span class="text_12-2">智彗随访包</span>
          <span class="text_13-2">一机多用&nbsp;实时上传</span>
          <img
            class="image_2-2"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
          <img
            class="image_3-2"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
        <div class="text-group_1-3" @click="ActiveFuns(1)">
          <span class="text_12-3">边缘计算网关</span>
          <span class="text_13-3">万能互联&nbsp;实时监控</span>
          <img
            class="image_3-3"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
      </div>
      <div v-show="active === 4" class="list_3">
        <div class="text-group_1-0" @click="ActiveFuns(4)">
          <span class="text_12-0">健康检测一体机</span>
          <span class="text_13-0">智慧体检 一机搞定</span>
          <img
            class="image_4-0"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng3b9a8121db0a1bcc7c1a65f5a79361a0bbbf877f3a659cc948ad087a9b9f03cb"
          />
        </div>
        <div class="text-group_1-1" @click="ActiveFuns(5)">
          <span class="text_12-1">智慧随访包</span>
          <span class="text_13-1">一机多用 实时上传</span>
          <img
            class="image_2-1"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
        <div class="text-group_1-2" @click="ActiveFuns(1)">
          <span class="text_12-2">边缘计算网关</span>
          <span class="text_13-2">万物互联 实时监控</span>
          <img
            class="image_2-2"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
          <img
            class="image_3-2"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
        <div class="text-group_1-3" @click="ActiveFuns(2)">
          <span class="text_12-3">AI基层辅助机器人</span>
          <span class="text_13-3">人脸识别 自助问诊 远程诊断</span>
          <img
            class="image_3-3"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
      </div>
      <div v-show="active === 5" class="list_3">
        <div class="text-group_1-0" @click="ActiveFuns(5)">
          <span class="text_12-0">智慧随访包</span>
          <span class="text_13-0">一机多用 实时上传</span>
          <img
            class="image_4-0"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng3b9a8121db0a1bcc7c1a65f5a79361a0bbbf877f3a659cc948ad087a9b9f03cb"
          />
        </div>
        <div class="text-group_1-1" @click="ActiveFuns(1)">
          <span class="text_12-1">边缘计算网关</span>
          <span class="text_13-1">万物互联 实时监控</span>
          <img
            class="image_2-1"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
        <div class="text-group_1-2" @click="ActiveFuns(2)">
          <span class="text_12-2">AI基层辅助机器人</span>
          <span class="text_13-2">人脸识别 自助问诊 远程诊断</span>
          <img
            class="image_2-2"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
          <img
            class="image_3-2"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
        <div class="text-group_1-3" @click="ActiveFuns(3)">
          <span class="text_12-3">卫软穿戴设备</span>
          <span class="text_13-3">实时监测 安心保障</span>
          <img
            class="image_3-3"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
      </div>
    </div>
    <div v-show="active === 1" class="section_3">
      <img
        class="image_6"
        referrerpolicy="no-referrer"
        src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng5b727b1da8591b50a16f335462b668ed3c9d990d7539f3741af9ebde6c96b421"
        @click="ActiveFun('减')"
      />
      <div class="group_2">
        <img src="@/assets/images/wifihe.png" alt="" />
      </div>
      <div class="box_31">
        <span class="text_14">边缘计算网关</span>
        <div class="section_8"></div>
        <span class="text_15"
          >超低功耗边缘计算网关，支持多种经典协议接入，支持有线、无线接入，支持设备定位、开关机监测、距离监测。</span
        >
      </div>
      <img
        class="image_7"
        referrerpolicy="no-referrer"
        src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPngce59f9644e820397797e92e07d3796333123469c09e7b719e032226453706ed3"
        @click="ActiveFun('加')"
      />
    </div>
    <div v-show="active === 2" class="section_3">
      <img
        class="image_6"
        referrerpolicy="no-referrer"
        src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng5b727b1da8591b50a16f335462b668ed3c9d990d7539f3741af9ebde6c96b421"
        @click="ActiveFun('减')"
      />
      <div class="group_2">
        <img src="@/assets/images/jiqiqiren.png" alt="" />
      </div>
      <div class="box_31">
        <span class="text_14">AI基层辅助机器人</span>
        <div class="section_8"></div>
        <span class="text_15"
          >科技元素赋能基层实用场景，机器人支持无人值守模式，支持动线引导、人脸身份识别、疾病问诊、远程诊断等，培养患者习惯，减少人力成本。</span
        >
      </div>
      <img
        class="image_7"
        referrerpolicy="no-referrer"
        src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPngce59f9644e820397797e92e07d3796333123469c09e7b719e032226453706ed3"
        @click="ActiveFun('加')"
      />
    </div>
    <div v-show="active === 3" class="section_3">
      <img
        class="image_6"
        referrerpolicy="no-referrer"
        src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng5b727b1da8591b50a16f335462b668ed3c9d990d7539f3741af9ebde6c96b421"
        @click="ActiveFun('减')"
      />
      <div class="group_2">
        <img src="@/assets/images/shoubiao1.png" alt="" />
      </div>
      <div class="box_31">
        <span class="text_14">卫软穿戴设备</span>
        <div class="section_8"></div>
        <span class="text_15"
          >卫软手表定制，支持单导心电、心率、PPG血压监测、血氧、睡眠、体温、GPS、4G通信模块等；卫软同时支持华为、OPPO等一线厂家品牌开发接入。</span
        >
      </div>
      <img
        class="image_7"
        referrerpolicy="no-referrer"
        src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPngce59f9644e820397797e92e07d3796333123469c09e7b719e032226453706ed3"
        @click="ActiveFun('加')"
      />
    </div>
    <div v-show="active === 4" class="section_3">
      <img
        class="image_6"
        referrerpolicy="no-referrer"
        src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng5b727b1da8591b50a16f335462b668ed3c9d990d7539f3741af9ebde6c96b421"
        @click="ActiveFun('减')"
      />
      <div class="group_2">
        <img src="@/assets/images/jixiebi.png" alt="" />
      </div>
      <div class="box_31">
        <span class="text_14">健康信息工作站</span>
        <div class="section_8"></div>
        <span class="text_15"
          >精准之道，内外兼修。高端配置高度集成一体化设计，健康宣教和自助体检结合，30多项健康检测指标，集秒级快检、健康指导、风险评估为一体，助力就诊、体检等多个场景，数据实时上传与统计。</span
        >
      </div>
      <img
        class="image_7"
        referrerpolicy="no-referrer"
        src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPngce59f9644e820397797e92e07d3796333123469c09e7b719e032226453706ed3"
        @click="ActiveFun('加')"
      />
    </div>
    <div v-show="active === 5" class="section_3">
      <img
        class="image_6"
        referrerpolicy="no-referrer"
        src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng5b727b1da8591b50a16f335462b668ed3c9d990d7539f3741af9ebde6c96b421"
        @click="ActiveFun('减')"
      />
      <div class="group_2">
        <img src="@/assets/images/yiliaoxiang.png" alt="" />
      </div>
      <div class="box_31">
        <span class="text_14">智慧随访包</span>
        <div class="section_8"></div>
        <span class="text_15"
          >支持多种检测设备，包含尿液、血糖、血压、体温、心电图（六导、十二导）、支持中医体质辨识、身份识别、血氧、脉率等检测，手提箱设计，数据实时同步。</span
        >
      </div>
      <img
        class="image_7"
        referrerpolicy="no-referrer"
        src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPngce59f9644e820397797e92e07d3796333123469c09e7b719e032226453706ed3"
        @click="ActiveFun('加')"
      />
    </div>
    <div class="section_4">
      <div class="section_4_box1">产品优势</div>
      <div class="section_4_box2"></div>
      <div class="section_4_box3">PRODUCT ADVANTAGES</div>
      <div class="section_4_box4">
        <div class="box">
          <img src="@/assets/images/shebei.png" alt="" />
          <div class="box_1">设备感知</div>
          <div class="box_2">感知一定距离内的医疗设备，轻松掌握设备在线状态、使用频率，万物互联</div>
        </div>
        <div class="box-1">
          <img src="@/assets/images/dengpao.png" alt="" />
          <div class="box_1">低功耗</div>
          <div class="box_2">设备采用专为物联网关设计的ARM Cortext-M3处理器，高性能、低功耗，满足多路终端同时并发</div>
        </div>
        <div class="box-1">
          <img src="@/assets/images/shuangduns.png" alt="" />
          <div class="box_1">安全可靠</div>
          <div class="box_2">内嵌防火墙功能和各种加密方案，实现全方位防护，打造稳定的物联网关服务</div>
        </div>
        <div class="box-1">
          <img src="@/assets/images/zishuang.png" alt="" />
          <div class="box_1">接口丰富</div>
          <div class="box_2">设备支持RJ45网口接入、WIFI接入、支持经典蓝牙协议、BLE低功耗蓝牙协议，支持USB-C接口</div>
        </div>
      </div>
    </div>
    <div class="section_5">
      <div class="section_5_box1">核心功能</div>
      <div class="section_5_box2"></div>
      <div class="section_5_box3">CORE FUNCTIONS</div>
      <div class="section_5_box4">
        <div class="section_5_box4_left"></div>
        <div class="section_5_box4_right">
          <div class="box">
            <div class="hao"></div>
            <div class="hao_1">多协议支持</div>
          </div>
          <div class="box_1">设备支持MQTT、COAP、TCP、UDP、HTTP传输协议，且支持协议扩展，满足多种接入需求。</div>
        </div>
      </div>
      <div class="section_5_box5">
        <div class="section_5_box4_left">
          <div class="box">
            <div class="hao"></div>
            <div class="hao_1">安全加密</div>
          </div>
          <div class="box_1">
            协议传输支持RSA签名算法、3DES对称性加密算法，传输时引入准入机制、节点SSL连接、落盘数据加密和权限控制，安全可靠。
          </div>
        </div>
        <div class="section_5_box4_right"></div>
      </div>
      <div class="section_5_box6">
        <div class="section_5_box4_left"></div>
        <div class="section_5_box4_right">
          <div class="box">
            <div class="hao"></div>
            <div class="hao_1">低延迟、高灵敏</div>
          </div>
          <div class="box_1">
            Class1-3动态控制范围达24dB，NZIF动态范围最低支持97dB，接收灵敏度达-96dBm，RSSI分辨率支持1dB。
          </div>
        </div>
      </div>
    </div>
    <div class="section_6">
      <div class="section_6_box1">产品外观</div>
      <div class="section_6_box2"></div>
      <div class="section_6_box3">APPEARANCE</div>
      <div class="section_6_box4">
        <img src="@/assets/images/chakou.png" alt="" class="bor" />
        <img src="@/assets/images/diannaos.png" alt="" style="margin: 0 2.5rem" class="bor" />
        <img src="@/assets/images/xiaohe.png" alt="" class="bor" />
      </div>
    </div>
    <div class="section_7">
      <div class="section_7_box1">基本参数</div>
      <div class="section_7_box2"></div>
      <div class="section_7_box3">PRODUCT ADVANTAGES</div>
      <div class="section_7_box4">
        <div class="box_1">外观参数</div>
        <template v-if="isTrue">
          <img
            src="@/assets/images/shangla.png"
            alt=""
            style="width: 1.375rem; height: 0.75rem; margin-right: 4.375rem"
            @click="clickFun"
          />
        </template>
        <template v-else>
          <img
            src="@/assets/images/xiala.png"
            alt=""
            style="width: 1.375rem; height: 0.75rem; margin-right: 4.375rem"
            @click="clickFun"
          />
        </template>
      </div>
      <div v-show="isTrue" class="section_7_box4_1">
        <div class="box_1">
          <span class="text_1"> 尺寸 </span>
          <span class="text_2">120*120*30mm</span>
        </div>
        <div class="box_1">
          <span class="text_1"> 电源 </span>
          <span class="text_2">USB Type-C 5V/3A</span>
        </div>
        <div class="box_1">
          <span class="text_1"> 指示灯 </span>
          <span class="text_2">电源，无线网络</span>
        </div>
        <div class="box_1">
          <span class="text_1"> 锂电池 </span>
          <span class="text_2">5000mAH</span>
        </div>
      </div>
      <div class="section_7_box4">
        <div class="box_1">功能参数</div>
        <template v-if="isTrues">
          <img
            src="@/assets/images/shangla.png"
            alt=""
            style="width: 1.375rem; height: 0.75rem; margin-right: 4.375rem"
            @click="clickFuns"
          />
        </template>
        <template v-else>
          <img
            src="@/assets/images/xiala.png"
            alt=""
            style="width: 1.375rem; height: 0.75rem; margin-right: 4.375rem"
            @click="clickFuns"
          />
        </template>
      </div>
      <div v-show="isTrues" class="section_7_box4_2">
        <div class="box_1">
          <span class="text_1"> 工作温度 </span>
          <span class="text_2">-10℃~60℃</span>
        </div>
        <div class="box_1">
          <span class="text_1"> 存储温度 </span>
          <span class="text_2">-40℃~85℃</span>
        </div>
        <div class="box_1">
          <span class="text_1"> Wi-Fi 性能 </span>
          <span class="text_2">最大50M覆盖</span>
        </div>
      </div>
    </div>
  </div>
</template>
<style scoped lang="scss">
.banner {
  max-width: 160rem;
  width: 100%;
  position: relative;
  margin: 0 auto;
  margin-top: 3.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow: hidden;
  .section_1 {
    width: 100%;
    height: 37.5rem;
    position: relative;
    background-image: url('@/assets/images/wulian.png');
    background-size: 100% 100%;
    .section_1_box {
      width: 43.75rem;
      height: 8.5625rem;
      display: flex;
      flex-direction: column;
      align-items: center;
      position: absolute;
      top: 14.4375rem;
      right: 18.75rem;
      .box_1 {
        width: 19.0625rem;
        height: 4.375rem;
        font-size: 2.25rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #222222;
        line-height: 4.375rem;
        text-align: center;
      }
      .box_2 {
        font-size: 1.25rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #666666;
        margin-top: 1.25rem;
      }
    }
  }
  .section_2 {
    background-color: rgba(255, 255, 255, 1);
    width: 100%;
    height: 22.6875rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    .text_10 {
      width: 30rem;
      height: 3.5rem;
      overflow-wrap: break-word;
      color: rgba(34, 34, 34, 1);
      font-size: 1.875rem;
      font-family: PingFangSC-Medium;
      font-weight: 500;
      text-align: left;
      white-space: nowrap;
      line-height: 3.5rem;
      margin: 4.375rem 0 0 0;
      text-align: center;
    }
    .section_6 {
      background-color: rgba(2, 129, 224, 1);
      width: 3.125rem;
      height: 0.25rem;
      margin: 0.625rem 0 0 0;
    }
    .text_11 {
      width: 15.4375rem;
      height: 1.75rem;
      overflow-wrap: break-word;
      color: rgba(102, 102, 102, 1);
      font-size: 1rem;
      font-family: PingFangSC-Regular;
      font-weight: normal;
      text-align: left;
      white-space: nowrap;
      line-height: 1.75rem;
      margin: 0.625rem 0 0 0;
      text-align: center;
    }
    .list_3 {
      cursor: pointer;
      width: 92.5rem;
      height: 7.8125rem;
      display: flex;
      justify-content: space-between;
      margin: 2.5rem 0 1.25rem 0;
    }
    .text-group_1-0 {
      border-radius: 0 0.625rem 0.625rem 0;
      position: relative;
      width: 23.125rem;
      height: 7.8125rem;
      display: flex;
      flex-direction: column;
      justify-content: flex-center;
      background: rgba(72, 101, 255, 1);
    }
    .text_12-0 {
      width: 9.375rem;
      height: 2.0625rem;
      overflow-wrap: break-word;
      font-size: 1.5rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      text-align: left;
      white-space: nowrap;
      margin: 1.6875rem 0 0.9375rem 2.5rem;
      color: rgba(255, 255, 255, 1);
    }
    .text_13-0 {
      width: 9.375rem;
      height: 1.375rem;
      overflow-wrap: break-word;
      font-size: 1rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      text-align: left;
      white-space: nowrap;
      margin: 0 0 1.25rem 2.5rem;
      color: rgba(255, 255, 255, 1);
    }
    .image_4-0 {
      position: absolute;
      left: 0;
      top: -2.5625rem;
      width: 0.125rem;
      height: 10.3125rem;
    }
    .text-group_1-1 {
      border-radius: 0 0.625rem 0.625rem 0;
      position: relative;
      width: 23.125rem;
      height: 7.8125rem;
      display: flex;
      flex-direction: column;
      justify-content: flex-center;
      background: rgba(216, 216, 216, 0);
    }
    .text_12-1 {
      width: 12rem;
      height: 2.0625rem;
      overflow-wrap: break-word;
      font-size: 1.5rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      text-align: left;
      white-space: nowrap;
      margin: 1.6875rem 0 0.9375rem 2.5rem;
      color: rgba(34, 34, 34, 1);
    }
    .text_13-1 {
      width: 12.6875rem;
      height: 1.375rem;
      overflow-wrap: break-word;
      font-size: 1rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      text-align: left;
      white-space: nowrap;
      margin: 0 0 1.75rem 2.5rem;
      color: #666666;
    }
    .image_2-1 {
      position: absolute;
      left: 0;
      top: 1.25rem;
      width: 0.0625rem;
      height: 7.8125rem;
    }
    .text-group_1-2 {
      border-radius: 0 0.625rem 0.625rem 0;
      position: relative;
      width: 23.125rem;
      height: 7.8125rem;
      display: flex;
      flex-direction: column;
      justify-content: flex-center;
      background: rgba(216, 216, 216, 0);
    }
    .text_12-2 {
      width: 9rem;
      height: 2.0625rem;
      overflow-wrap: break-word;
      font-size: 1.5rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      text-align: left;
      white-space: nowrap;
      margin: 1.6875rem 0 0.9375rem 2.5rem;
      color: rgba(34, 34, 34, 1);
    }
    .text_13-2 {
      width: 8.375rem;
      height: 1.375rem;
      overflow-wrap: break-word;
      font-size: 1rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      text-align: left;
      white-space: nowrap;
      margin: 0 0 1.75rem 2.5rem;
      color: rgba(102, 102, 102, 1);
    }
    .image_2-2 {
      position: absolute;
      left: 0;
      top: 1.25rem;
      width: 0.0625rem;
      height: 7.8125rem;
    }
    .image_3-2 {
      position: absolute;
      left: 23.0625rem;
      top: 1.25rem;
      width: 0.0625rem;
      height: 7.8125rem;
    }
    .text-group_1-3 {
      border-radius: 0 0.625rem 0.625rem 0;
      position: relative;
      width: 23.125rem;
      height: 7.8125rem;
      display: flex;
      flex-direction: column;
      justify-content: flex-center;
      background: rgba(216, 216, 216, 0);
    }
    .text_12-3 {
      width: 10.5rem;
      height: 2.0625rem;
      overflow-wrap: break-word;
      font-size: 1.5rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      text-align: left;
      white-space: nowrap;
      margin: 1.6875rem 0 0.9375rem 2.5rem;
      color: rgba(34, 34, 34, 1);
    }
    .text_13-3 {
      width: 8.375rem;
      height: 1.375rem;
      overflow-wrap: break-word;
      font-size: 1rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      text-align: left;
      white-space: nowrap;
      margin: 0 0 1.75rem 2.5rem;
      color: rgba(102, 102, 102, 1);
    }
    .image_3-3 {
      position: absolute;
      left: 23.0625rem;
      top: 1.25rem;
      width: 0.0625rem;
      height: 7.8125rem;
    }
  }
  .section_3 {
    background-color: rgba(34, 34, 34, 1);
    width: 100%;
    height: 12.5rem;
    display: flex;
    justify-content: center;
    align-items: center;
    .image_7 {
      width: 1.25rem;
      height: 2.1875rem;
      margin: 0 10.0625rem 0 2.4375rem;
    }
    .group_2 {
      width: 23.125rem;
      height: 12.5rem;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .image_5 {
      width: 1.25rem;
      height: 2.1875rem;
      margin: 6.75rem 0 0 10.0625rem;
    }
    .box_31 {
      width: 65rem;
      height: 9.6875rem;
      margin: 2.5rem 0 0 4.375rem;
    }
    .text_14 {
      width: 11.875rem;
      height: 2.0625rem;
      overflow-wrap: break-word;
      color: rgba(255, 255, 255, 1);
      font-size: 1.5rem;
      font-family: PingFangSC-Medium;
      font-weight: 500;
      text-align: left;
      white-space: nowrap;
    }
    .section_8 {
      background-color: rgba(255, 255, 255, 1);
      width: 5rem;
      height: 0.25rem;
      margin-top: 0.9375rem;
    }
    .text_15 {
      width: 56.25rem;
      height: 3.25rem;
      overflow-wrap: break-word;
      color: rgba(255, 255, 255, 1);
      font-size: 1rem;
      font-family: PingFangSC-Regular;
      font-weight: normal;
      text-align: left;
      display: block;
      margin-top: 1.25rem;
    }
    .image_6 {
      width: 1.25rem;
      height: 2.5rem;
      margin-right: 2.5rem;
      margin-left: 10rem;
    }
  }
  .section_4 {
    width: 100%;
    height: 34.375rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    background: linear-gradient(180deg, #dfe9ff 0%, #f6f9ff 100%);
    .section_4_box1 {
      width: 8rem;
      font-size: 1.875rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      color: #222222;
      margin-top: 5rem;
      text-align: center;
    }
    .section_4_box2 {
      width: 3.125rem;
      height: 0.25rem;
      background: #0281e0;
      margin: 0.8125rem 0 0.875rem 0;
    }
    .section_4_box3 {
      height: 1.75rem;
      font-size: 1rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #666666;
      line-height: 1.75rem;
    }
    .section_4_box4 {
      width: 92.5rem;
      height: 19.3125rem;
      display: flex;
      margin-top: 2.5rem;
      .box {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 23.125rem;
        .box_1 {
          width: 7.5rem;
          font-size: 1.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #222222;
          margin-top: 1.25rem;
          margin-bottom: 0.9375rem;
        }
        .box_2 {
          width: 18.125rem;
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #666666;
          text-align: center;
        }
      }
      .box-1 {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 23.125rem;
        .box_1 {
          width: 7.5rem;
          display: flex;
          justify-content: center;
          font-size: 1.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #222222;
          margin-top: 1.25rem;
          margin-bottom: 0.9375rem;
        }
        .box_2 {
          width: 18.125rem;
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #666666;
          text-align: center;
        }
      }
    }
  }
  .section_5 {
    width: 100%;
    height: 83rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: #ffffff;
    .section_5_box1 {
      width: 8rem;
      height: 2.8125rem;
      font-size: 1.875rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      color: #222222;
      line-height: 2.8125rem;
      margin-top: 4.375rem;
      text-align: center;
    }
    .section_5_box2 {
      width: 3.125rem;
      height: 0.25rem;
      background: #0281e0;
      margin: 0.625rem 0;
    }
    .section_5_box3 {
      height: 1.75rem;
      font-size: 1rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #666666;
      line-height: 1.75rem;
    }
    .section_5_box4 {
      width: 81.1875rem;
      height: 18.75rem;
      margin-top: 2.5rem;
      display: flex;
      .section_5_box4_left {
        width: 37.5rem;
        height: 18.75rem;
        background-image: url('@/assets/images/xinpian.png');
        background-size: 100% 100%;
      }
      .section_5_box4_right {
        display: flex;
        flex-direction: column;
        margin-left: 5rem;
        margin-top: 4.8125rem;
        .box {
          display: flex;
          align-items: center;
          .hao {
            width: 0.25rem;
            height: 1.25rem;
            background: #0281e0;
            border-radius: 0.125rem;
            margin-right: 0.9375rem;
          }
          .hao_1 {
            width: 9rem;
            height: 2.0625rem;
            font-size: 1.5rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #222222;
            line-height: 2.0625rem;
          }
        }
        .box_1 {
          width: 37.5rem;
          height: 4.625rem;
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #666666;
          margin-top: 1.25rem;
          margin-left: 0.9375rem;
        }
      }
    }
    .section_5_box5 {
      width: 81.1875rem;
      height: 18.75rem;
      margin-top: 2.5rem;
      display: flex;
      margin-top: 4.375rem;
      .section_5_box4_left {
        flex: 1;
        display: flex;
        flex-direction: column;
        margin-top: 3.625rem;
        .box {
          display: flex;
          align-items: center;
          .hao {
            width: 0.25rem;
            height: 1.25rem;
            background: #0281e0;
            border-radius: 0.125rem;
            margin-right: 0.9375rem;
          }
          .hao_1 {
            width: 9.375rem;
            height: 2.0625rem;
            font-size: 1.5rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #222222;
          }
        }
        .box_1 {
          width: 37.5rem;
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #666666;
          line-height: 2.3125rem;
          margin-top: 1.25rem;
          margin-left: 0.9375rem;
        }
      }
      .section_5_box4_right {
        width: 37.5rem;
        height: 18.75rem;
        background-image: url('@/assets/images/landun.png');
        background-size: 100% 100%;
      }
    }
    .section_5_box6 {
      width: 81.1875rem;
      height: 18.75rem;
      margin-top: 2.5rem;
      display: flex;
      margin-top: 4.375rem;
      .section_5_box4_left {
        width: 37.5rem;
        height: 18.75rem;
        background-image: url('@/assets/images/xinpian.png');
        background-size: 100% 100%;
      }
      .section_5_box4_right {
        display: flex;
        flex-direction: column;
        margin-left: 5.0625rem;
        margin-top: 3.625rem;
        .box {
          display: flex;
          align-items: center;
          .hao {
            width: 0.25rem;
            height: 1.25rem;
            background: #0281e0;
            border-radius: 0.125rem;
            margin-right: 0.9375rem;
          }
          .hao_1 {
            width: 10.5rem;
            height: 2.0625rem;
            font-size: 1.5rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #222222;
            line-height: 2.0625rem;
          }
        }
        .box_1 {
          width: 37.5rem;
          height: 3.25rem;
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #666666;
          margin-top: 1.25rem;
          margin-left: 0.9375rem;
        }
      }
    }
  }
  .section_6 {
    width: 100%;
    height: 46.125rem;
    background: #f3faff;
    display: flex;
    flex-direction: column;
    align-items: center;
    .section_6_box1 {
      width: 10rem;
      height: 3.5rem;
      font-size: 1.875rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      color: #222222;
      line-height: 3.5rem;
      margin-top: 4.375rem;
      text-align: center;
    }
    .section_6_box2 {
      width: 3.125rem;
      height: 0.25rem;
      background: #0281e0;
      margin: 0.625rem 0;
    }
    .section_6_box3 {
      width: 8.375rem;
      height: 1.75rem;
      font-size: 1rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #666666;
      line-height: 1.75rem;
      text-align: center;
    }
    .section_6_box4 {
      width: 98.75rem;
      height: 28.125rem;
      margin-top: 2.5rem;
      .bor {
        border-radius: 0.9375rem;
      }
    }
  }
  .section_7 {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    padding-bottom: 5rem;
    .section_7_box1 {
      width: 10rem;
      height: 3.5rem;
      font-size: 2.125rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      color: #222222;
      line-height: 3.5rem;
      margin-top: 4.375rem;
      text-align: center;
    }
    .section_7_box2 {
      width: 3.125rem;
      height: 0.25rem;
      background: #0281e0;
      margin: 0.625rem 0;
    }
    .section_7_box3 {
      height: 1.75rem;
      font-size: 1rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #666666;
      line-height: 1.75rem;
      text-align: center;
    }
    .section_7_box4 {
      display: flex;
      width: 92.5rem;
      height: 6.25rem;
      align-items: center;
      justify-content: space-between;
      background-color: #ffffff;
      margin-top: 2.5rem;
      border-top-left-radius: .9375rem;
      border-top-right-radius: .9375rem;
      .box_1 {
        width: 7.5rem;
        margin-left: 4.375rem;
        height: 2.625rem;
        font-size: 1.5rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #222222;
        line-height: 2.625rem;
      }
    }
    .section_7_box4_1 {
      border-top: 0.0625rem solid #c7c7c7;
      height: 7.5rem;
      width: 92.5rem;
      display: flex;
      flex-wrap: wrap;
      background: #ffffff;
      align-items: center;
      border-bottom-left-radius: 0.9375rem;
      border-bottom-right-radius: 0.9375rem;
      .box_1 {
        margin-top: 0.625rem;
        width: 23.125rem;
        height: 3.125rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        .text_1 {
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 1rem;
          color: #666666;
        }
        .text_2 {
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #222222;
          line-height: 1.375rem;
        }
      }
    }
    .section_7_box4_2 {
      border-top: 0.0625rem solid #c7c7c7;
      height: 7.5rem;
      width: 92.5rem;
      display: flex;
      flex-wrap: wrap;
      background: #ffffff;
      align-items: center;
      border-bottom-left-radius: 0.9375rem;
      border-bottom-right-radius: 0.9375rem;
      .box_1 {
        margin-top: 0.625rem;
        width: 23.125rem;
        height: 3.125rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        .text_1 {
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 1rem;
          color: #666666;
        }
        .text_2 {
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #222222;
          line-height: 1.375rem;
        }
      }
    }
  }
}
</style>
