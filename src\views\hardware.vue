<script setup>
import myFooter from '@/components/myFooter.vue'
import myHeader from '@/components/myHeader.vue'
import { ref, reactive } from 'vue'
const num = ref(1)
</script>

<template>
  <div class="banner">
    <img src="@/assets/images/wearabledevice-banner.png" alt="" />
    <div class="info">
      <div class="label">卫软穿戴 安心放心</div>
      <div class="text">将健康戴在手上，您的每一次心跳都被用心守护，关爱自己，守护家人。</div>
    </div>
  </div>
  <div class="image-main">
    <!-- <img src="@/assets/main_images/Hardware-main1.jpg" alt="" />
    <img src="@/assets/main_images/Hardware-main2.jpg" alt="" /> -->
    <div class="section_1">
      <span class="text_10">智能硬件产品助您超越所能</span>
      <div class="section_6"></div>
      <span class="text_11">INTELLIGENT&nbsp;HARDWARE</span>
      <div class="list_3">
        <div class="text-group_1-0">
          <span class="text_12-0">卫软穿戴设备</span>
          <span class="text_13-0">实时监测&nbsp;安心保障</span>
          <img
            class="image_4-0"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng3b9a8121db0a1bcc7c1a65f5a79361a0bbbf877f3a659cc948ad087a9b9f03cb"
          />
        </div>
        <div class="text-group_1-1">
          <span class="text_12-1">健康检测一体机</span>
          <span class="text_13-1">基础检查&nbsp;一机搞定</span>
          <img
            class="image_2-1"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
        <div class="text-group_1-2">
          <span class="text_12-2">智彗随访包</span>
          <span class="text_13-2">一机多用&nbsp;实时上传</span>
          <img
            class="image_2-2"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
          <img
            class="image_3-2"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
        <div class="text-group_1-3">
          <span class="text_12-3">边缘计算网关</span>
          <span class="text_13-3">万能互联&nbsp;实时监控</span>
          <img
            class="image_3-3"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
      </div>
    </div>
    <div class="section_2">
      <div class="group_2"></div>
      <div class="box_31">
        <span class="text_14">卫软穿戴设备</span>
        <div class="section_8"></div>
        <span class="text_15"
          >卫软手表定制，支持单导心电、心率、PPG血压监测、血氧、睡眠、体温、GPS、4G通信模块等；卫软同时支持华为、OPPO等一线厂家品牌开发接入。</span
        >
      </div>
    </div>
    <div class="section_3">
      <div class="section_3_top">
        <div class="section_3_left">
          <span class="section_t">
            <span class="roul">73%</span>
            <span class="fzie">在出院前死亡的1855名术后患者中，有73%的患者在死亡前没有转入重症监护。</span>
          </span>
          <span class="hang"
            >医疗行业当前面临患者安全和工作效率的严峻挑战。应对挑战，卫软坚持守护生命健康，让医护人员与患者共同感受到高品质穿戴监护的便利与舒适。</span
          >
        </div>
        <div class="section_3_right">
          <img src="@/assets/images/zhuanbiao.png" alt="" class="imgss" />
        </div>
      </div>
      <div class="section_3_bottom">
        <div class="section_box">
          <img src="@/assets/images/sos.png" alt="" class="imgs_1" />
          <span class="section_text">SOS紧急呼叫</span>
        </div>
        <div class="section_box">
          <img src="@/assets/images/naoling.png" alt="" class="imgs_1" />
          <span class="section_text">健康提醒</span>
        </div>
        <div class="section_box">
          <img src="@/assets/images/shuidi.png" alt="" class="imgs_1" />
          <span class="section_text">动态血压</span>
        </div>
        <div class="section_box">
          <img src="@/assets/images/yuantu.png" alt="" class="imgs_1" />
          <span class="section_text">血氧监测</span>
        </div>
        <div class="section_box">
          <img src="@/assets/images/aixin.png" alt="" class="imgs_1" />
          <span class="section_text">心率监测</span>
        </div>
        <div class="section_box">
          <img src="@/assets/images/tiwen.png" alt="" class="imgs_1" />
          <span class="section_text">体温测量</span>
        </div>
        <div class="section_box">
          <img src="@/assets/images/shuimian.png" alt="" class="imgs_1" />
          <span class="section_text">睡眠呼吸</span>
        </div>
      </div>
    </div>
    <div class="section_4">
      <div class="section_4_top">
        <div class="text_section_4">内外兼顾 健康无忧</div>
        <div class="text_da">打通院外数据，形成院内外数据结合的整体健康档案</div>
        <div class="box_section_4">
          <div>
            <img src="@/assets/images/shoubiaoshouji.png" alt="" class="phone_img" />
            <span class="phone_1">居家数据实时同步，您的健康我来守护</span>
            <img src="@/assets/images/zhibiao.png" alt="" class="phone_imgs" />
            <span class="phone_2">院内患者实时监测，有我在，您放心</span>
          </div>
          <div class="box_sectionsm">
            <img src="@/assets/images/liangbiao.png" alt="" class="phone_imgss" />
            <img src="@/assets/images/dianjibiao.png" alt="" class="phone_imgsss" />
            <span class="phone_3">无论哪里，您的健康有我守护</span>
          </div>
        </div>
      </div>
      <div class="section_4_bottom">
        <div class="section_4_left">
          <img src="@/assets/images/diannao.png" alt="" class="diannaoimg" />
        </div>
        <div class="section_4_right">
          <div class="box_right">
            <div class="text_1">数据实时同步</div>
            <div class="text_2">院内院外数据兼顾，便于医生实时了解患者情况</div>
            <div class="text_3">
              系统中设计“穿戴日记”模块，实时同步穿戴数据。
              含心率、血氧、体温、睡眠变化曲线展示，并筛查异常数据进行预警提示。
            </div>
            <div class="text_4">价值</div>
            <div class="text_5">
              <img src="@/assets/images/fangkuai.png" alt="" class="kuai_img" />
              <span class="yuan_text">院内院外 双管齐下</span>
            </div>
            <div class="text_6">打通院外数据，进行居家监测，符合国家的政策要求。</div>
            <div class="text_5">
              <img src="@/assets/images/fangkuai.png" alt="" class="kuai_img" />
              <span class="yuan_text">健康档案 内外结合</span>
            </div>
            <div class="text_6">全方面、全场景健康档案，形成院内外结合的整体档案。</div>
            <div class="text_5">
              <img src="@/assets/images/fangkuai.png" alt="" class="kuai_img" />
              <span class="yuan_text">提高效率 降低成本</span>
            </div>
            <div class="text_6">佩戴即有数据，随时随地检测，大大节省人力、物力，提高效率、降低成本。</div>
          </div>
        </div>
      </div>
    </div>
    <div class="section_5">
      <div class="section_5box">基本参数</div>
      <div class="section_5box1"></div>
      <div class="section_5box2">PRODUCT ADVANTAGES</div>
      <div class="section_5box3">
        <div class="section_5_box3_top">
          <span class="text_s5_1">外观参数</span>
          <span><img src="@/assets/images/cha.png" alt="" class="chaimg" /></span>
        </div>
        <div class="section_5_box3_bottom">
          <div class="secbox">
            <div class="box_text">外壳尺寸</div>
            <div class="box_text1">43.8*37.8*12mm</div>
            <div class="box_text">表镜材质</div>
            <div class="box_text1">二次钢化加硬玻璃镜面</div>
            <div class="box_text">电池材质</div>
            <div class="box_text1">聚合物</div>
            <div class="box_text">整机重量</div>
            <div class="box_text1">36g</div>
          </div>
          <div class="secbox">
            <div class="box_text">屏幕尺寸</div>
            <div class="box_text1">1.54寸</div>
            <div class="box_text">表扣方式</div>
            <div class="box_text1">标准金属表扣</div>
            <div class="box_text">屏幕技术</div>
            <div class="box_text1">全贴合</div>
            <div class="box_text">背光颜色</div>
            <div class="box_text1">LED白光</div>
          </div>
          <div class="secbox">
            <div class="box_text">表壳材质</div>
            <div class="box_text1">PC+ABS</div>
            <div class="box_text">背板材质</div>
            <div class="box_text1">PC+ABS</div>
            <div class="box_text">显示参数</div>
            <div class="box_text1">240*240*RGB</div>
            <div class="box_text">电池容量</div>
            <div class="box_text1">700MAH</div>
          </div>
          <div class="secbox">
            <div class="box_text">表带材质</div>
            <div class="box_text1">硅胶</div>
            <div class="box_text">按键数量</div>
            <div class="box_text1">2</div>
            <div class="box_text">显示颜色</div>
            <div class="box_text1">彩色</div>
            <div class="box_text">背光时长和亮度</div>
            <div class="box_text1">8S</div>
          </div>
        </div>
      </div>
      <div class="section_5box4">
        <div class="box_tops">
          <span class="text_gong">功能参数</span>
          <span class="text_sp"><img src="@/assets/images/cha.png" alt="" class="chaimgs" /></span>
        </div>
        <div class="box_bottoms">
          <div class="bootom_top">
            <div class="box_q1">
              <div class="pu_box">防尘防水</div>
              <div class="Npu_box">IP65</div>
              <div class="pu_box">静电等级</div>
              <div class="Npu_box">10+6~10+12ohm</div>
            </div>
            <div class="box_q1">
              <div class="pu_box">按键功能</div>
              <div class="Npu_box">SOS及功能键</div>
              <div class="pu_box">监测续航（心率连续监测）</div>
              <div class="Npu_box">一小时一次待机15天</div>
            </div>
            <div class="box_q1">
              <div class="pu_box">操作逻辑</div>
              <div class="Npu_box">按键切换/全触屏</div>
              <div class="pu_box">整机功耗</div>
              <div class="Npu_box">30MAh/天</div>
            </div>
            <div class="box_q2">
              <div class="pu_box">待机续航</div>
              <div class="Npu_box">15天</div>
            </div>
          </div>
          <div class="bootom_bottom">
            主要部件功耗说明：
            <div class="text_se1">单导心电（ECG）、心率、血压（PPG）、定位、计步、睡眠、体温、GPS定位、4G卡</div>
          </div>
        </div>
      </div>
    </div>
    <div class="section_6">
      <img src="@/assets/images/shoubiaodi.png" alt="" style="width: 100%; height: 100%" />
    </div>
    <div class="section_7">
      <div class="section_text_1">应用场景</div>
      <div class="section_text_2"></div>
      <div class="section_text_3">PRODUCT ADVANTAGES</div>
      <div class="section_box_1">
        <div class="box_1">
          <img src="@/assets/images/pepol.png" alt="" class="peoimg" />
          <div class="text_1">智慧养老</div>
          <div class="text_2">异常预警，防止意外事件发生，有针对性给予老年人群健康关怀。</div>
        </div>
        <div class="box_2">
          <img src="@/assets/images/jiaonang.png" alt="" class="peoimg" />
          <div class="text_1">药店、中西医诊断</div>
          <div class="text_2">实时数据有助于医生诊断、开处方、院外随访。</div>
        </div>
        <div class="box_1">
          <img src="@/assets/images/lanxin.png" alt="" class="peoimg" />
          <div class="text_1">院内外健康监测</div>
          <div class="text_2">多条数据统计、支持出院回访、病情观察患者。</div>
        </div>
      </div>
      <div class="section_box_2">联系合作</div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.banner {
  max-width: 2560px;
  width: 100%;
  position: relative;
  margin: 0 auto;
  margin-top: 80px;
  display: flex;
  justify-content: center;
  img {
    width: 100%;
    height: auto;
  }
  .info {
    position: absolute;
    width: 36.4vw;
    left: 45%;
    top: 30%;
    text-align: center;
    .label {
      font-size: 50px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      color: #222;
      line-height: 70px;
      margin-bottom: 30px;
    }
    .text {
      font-size: 24px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #666;
      line-height: 37px;
    }
  }
}
.image-main {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  img {
    width: 100%;
  }
  .section_1 {
    background-color: rgba(255, 255, 255, 1);
    width: 100%;
    height: 363px;
    display: flex;
    flex-direction: column;
    align-items: center;
    .text_10 {
      width: 480px;
      height: 56px;
      overflow-wrap: break-word;
      color: rgba(34, 34, 34, 1);
      font-size: 40px;
      font-family: PingFangSC-Medium;
      font-weight: 500;
      text-align: left;
      white-space: nowrap;
      line-height: 56px;
      margin: 70px 0 0 0;
    }
    .section_6 {
      background-color: rgba(2, 129, 224, 1);
      width: 50px;
      height: 4px;
      margin: 10px 0 0 0;
    }
    .text_11 {
      width: 247px;
      height: 28px;
      overflow-wrap: break-word;
      color: rgba(102, 102, 102, 1);
      font-size: 20px;
      font-family: PingFangSC-Regular;
      font-weight: normal;
      text-align: left;
      white-space: nowrap;
      line-height: 28px;
      margin: 10px 0 0 0;
    }
    .list_3 {
      width: 1480px;
      height: 125px;
      display: flex;
      justify-content: space-between;
      margin: 40px 0 20px 0;
    }
    .text-group_1-0 {
      border-radius: 0px 10px 10px 0px;
      position: relative;
      width: 370px;
      height: 125px;
      display: flex;
      flex-direction: column;
      justify-content: flex-center;
      background: rgba(72, 101, 255, 1);
    }
    .text_12-0 {
      width: 270px;
      height: 42px;
      overflow-wrap: break-word;
      font-size: 30px;
      font-family: PingFangSC-Medium;
      font-weight: 500;
      text-align: left;
      white-space: nowrap;
      line-height: 42px;
      margin: 20px 0 0 40px;
      color: rgba(255, 255, 255, 1);
    }
    .text_13-0 {
      width: 200px;
      height: 33px;
      overflow-wrap: break-word;
      font-size: 24px;
      font-family: PingFangSC-Regular;
      font-weight: normal;
      text-align: left;
      white-space: nowrap;
      line-height: 33px;
      margin: 10px 0 20px 40px;
      color: rgba(255, 255, 255, 1);
    }
    .image_4-0 {
      position: absolute;
      left: 0;
      top: -41px;
      width: 2px;
      height: 165px;
    }
    .text-group_1-1 {
      border-radius: 0px 10px 10px 0px;
      position: relative;
      width: 370px;
      height: 125px;
      display: flex;
      flex-direction: column;
      justify-content: flex-center;
      background: rgba(216, 216, 216, 0);
    }
    .text_12-1 {
      width: 270px;
      height: 42px;
      overflow-wrap: break-word;
      font-size: 30px;
      font-family: PingFangSC-Medium;
      font-weight: 500;
      text-align: left;
      white-space: nowrap;
      line-height: 42px;
      margin: 20px 0 0 40px;
      color: rgba(34, 34, 34, 1);
    }
    .text_13-1 {
      width: 200px;
      height: 33px;
      overflow-wrap: break-word;
      font-size: 24px;
      font-family: PingFangSC-Regular;
      font-weight: normal;
      text-align: left;
      white-space: nowrap;
      line-height: 33px;
      margin: 10px 0 20px 40px;
      color: rgba(102, 102, 102, 1);
    }
    .image_2-1 {
      position: absolute;
      left: 0;
      top: 20px;
      width: 1px;
      height: 125px;
    }
    .text-group_1-2 {
      border-radius: 0px 10px 10px 0px;
      position: relative;
      width: 370px;
      height: 125px;
      display: flex;
      flex-direction: column;
      justify-content: flex-center;
      background: rgba(216, 216, 216, 0);
    }
    .text_12-2 {
      width: 270px;
      height: 42px;
      overflow-wrap: break-word;
      font-size: 30px;
      font-family: PingFangSC-Medium;
      font-weight: 500;
      text-align: left;
      white-space: nowrap;
      line-height: 42px;
      margin: 20px 0 0 40px;
      color: rgba(34, 34, 34, 1);
    }
    .text_13-2 {
      width: 200px;
      height: 33px;
      overflow-wrap: break-word;
      font-size: 24px;
      font-family: PingFangSC-Regular;
      font-weight: normal;
      text-align: left;
      white-space: nowrap;
      line-height: 33px;
      margin: 10px 0 20px 40px;
      color: rgba(102, 102, 102, 1);
    }
    .image_2-2 {
      position: absolute;
      left: 0;
      top: 20px;
      width: 1px;
      height: 125px;
    }
    .image_3-2 {
      position: absolute;
      left: 369px;
      top: 20px;
      width: 1px;
      height: 125px;
    }
    .text-group_1-3 {
      border-radius: 0px 10px 10px 0px;
      position: relative;
      width: 370px;
      height: 125px;
      display: flex;
      flex-direction: column;
      justify-content: flex-center;
      background: rgba(216, 216, 216, 0);
    }
    .text_12-3 {
      width: 270px;
      height: 42px;
      overflow-wrap: break-word;
      font-size: 30px;
      font-family: PingFangSC-Medium;
      font-weight: 500;
      text-align: left;
      white-space: nowrap;
      line-height: 42px;
      margin: 20px 0 0 40px;
      color: rgba(34, 34, 34, 1);
    }
    .text_13-3 {
      width: 200px;
      height: 33px;
      overflow-wrap: break-word;
      font-size: 24px;
      font-family: PingFangSC-Regular;
      font-weight: normal;
      text-align: left;
      white-space: nowrap;
      line-height: 33px;
      margin: 10px 0 20px 40px;
      color: rgba(102, 102, 102, 1);
    }
    .image_3-3 {
      position: absolute;
      left: 369px;
      top: 20px;
      width: 1px;
      height: 125px;
    }
  }
  .section_2 {
    background-color: rgba(34, 34, 34, 1);
    width: 100%;
    height: 250px;
    display: flex;
    justify-content: center;
    .group_2 {
      background-image: url(https://lanhu-dds-backend.oss-cn-beijing.aliyuncs.com/merge_image/imgs/2f7b5dc127184141bfd712fa66aaa459_mergeImage.png);
      width: 370px;
      height: 250px;
    }
    .image_5 {
      width: 20px;
      height: 35px;
      margin: 108px 0 0 161px;
    }
    .box_31 {
      width: 1040px;
      height: 155px;
      margin: 40px 0 0 70px;
    }
    .text_14 {
      width: 240px;
      height: 42px;
      overflow-wrap: break-word;
      color: rgba(255, 255, 255, 1);
      font-size: 30px;
      font-family: PingFangSC-Medium;
      font-weight: 500;
      text-align: left;
      white-space: nowrap;
      line-height: 42px;
    }
    .section_8 {
      background-color: rgba(255, 255, 255, 1);
      width: 80px;
      height: 4px;
      margin-top: 15px;
    }
    .text_15 {
      width: 1040px;
      height: 74px;
      overflow-wrap: break-word;
      color: rgba(255, 255, 255, 1);
      font-size: 24px;
      font-family: PingFangSC-Regular;
      font-weight: normal;
      text-align: left;
      line-height: 37px;
      margin-top: 20px;
    }
    .image_6 {
      width: 20px;
      height: 35px;
      margin: 108px 161px 0 39px;
    }
  }
  .section_3 {
    width: 100%;
    height: 692px;
    background: linear-gradient(180deg, #d9ecfa 0%, #f6fbff 100%);
    display: flex;
    flex-direction: column;
    .section_3_top {
      height: 470px;
      display: flex;
      .section_3_left {
        display: flex;
        flex-direction: column;
        .section_t {
          display: flex;
          align-items: center;
          margin-top: 70px;
          width: 1199px;
          height: 150px;
          background: #ffffff;
          border-radius: 0px 30px 30px 0px;
          .roul {
            color: #da3900;
            font-size: 50px;
            margin-left: 219px;
            margin-right: 40px;
          }
          .fzie {
            width: 792px;
            font-size: 30px;
            color: #666666;
          }
        }
        .hang {
          width: 940px;
          height: 141px;
          font-size: 30px;
          line-height: 47px;
          margin-top: 70px;
          margin-left: 219px;
        }
      }
      .section_3_right {
        .imgss {
          display: block;
          width: 460px;
          height: 400px;
          margin-top: 70px;
          margin-left: 40px;
        }
      }
    }
    .section_3_bottom {
      display: flex;
      justify-content: space-around;
      align-items: center;
      margin-top: 40px;
      .section_box {
        display: flex;
        flex-direction: column;
        align-items: center;
        .section_text {
          font-size: 24px;
          color: #222222;
        }
        .imgs_1 {
          width: 64px;
          height: 64px;
        }
      }
    }
  }
  .section_4 {
    display: flex;
    flex-direction: column;
    align-items: center;

    width: 100%;
    height: 1665px;
    .section_4_top {
      width: 100%;
      display: flex;
      flex-direction: column;
      .text_section_4 {
        display: flex;
        flex-direction: column;
        align-items: center;
        font-size: 40px;
        color: #222222;
      }
      .text_da {
        display: flex;
        flex-direction: column;
        align-items: center;
        font-size: 24px;
        color: #666666;
      }
      .box_section_4 {
        display: flex;
        justify-content: center;
        margin-top: 40px;
        .phone_img {
          width: 700px;
          height: 600px;
          margin-left: 70px;
          margin-right: 20px;
          position: relative;
        }
        .phone_1 {
          width: 700px;
          height: 50px;
          position: absolute;
          background-color: #000000;
          left: 70px;
          bottom: 6px;
          border-radius: 0px 0px 15px 15px;
          opacity: 0.5;
          color: #ffffff;
          display: flex;
          justify-content: center;
          align-items: center;
        }
        .phone_2 {
          width: 470px;
          height: 50px;
          position: absolute;
          background-color: #000000;
          left: 790px;
          bottom: 6px;
          border-radius: 0px 0px 15px 15px;
          opacity: 0.5;
          color: #ffffff;
          display: flex;
          justify-content: center;
          align-items: center;
        }
        .phone_3 {
          width: 570px;
          height: 50px;
          position: absolute;
          background-color: #000000;
          right: 0;
          bottom: 6px;
          border-radius: 0px 0px 15px 15px;
          opacity: 0.5;
          color: #ffffff;
          display: flex;
          justify-content: center;
          align-items: center;
        }
        .phone_imgs {
          width: 470px;
          height: 600px;
          margin-right: 20px;
        }
        .box_sectionsm {
          display: flex;
          flex-direction: column;

          .phone_imgss {
            width: 570px;
            height: 290px;
            margin-bottom: 20px;
          }
          .phone_imgsss {
            width: 570px;
            height: 290px;
            position: relative;
          }
        }
      }
    }
    .section_4_bottom {
      position: relative;
      margin-top: 30px;
      width: 100%;
      display: flex;
      .section_4_left {
        width: 53%;
      }
      .diannaoimg {
        width: 1520px;
        height: 800px;
        z-index: 1;
      }
      .section_4_right {
        position: absolute;
        top: 0;
        right: 0;
        width: 1460px;
        height: 880px;
        background: #e4f3ff;
        .box_right {
          margin-left: 510px;
          display: flex;
          flex-direction: column;
          .text_1 {
            margin-top: 70px;
            color: #222222;
            font-size: 36px;
          }
          .text_2 {
            font-size: 24px;
            color: #666666;
            margin-top: 15px;
          }
          .text_3 {
            margin-top: 40px;
            width: 630px;
            height: 129px;
            font-size: 28px;
            color: #222222;
          }
          .text_4 {
            color: #222222;
            font-size: 30px;
            margin-top: 40px;
            margin-bottom: 15px;
          }
          .text_5 {
            display: flex;
            align-items: center;
            .kuai_img {
              width: 20.14px;
              height: 20.14px;
            }
            .yuan_text {
              margin-left: 10px;
              font-size: 24px;
              color: #4865ff;
            }
          }
          .text_6 {
            width: 660px;
            color: #666666;
            font-size: 24px;
            margin-top: 15px;
            margin-left: 30px;
            margin-bottom: 20px;
          }
        }
      }
    }
  }
  .section_5 {
    width: 100%;
    height: 1397px;
    display: flex;
    flex-direction: column;
    align-items: center;
    background: #f8f8f8;
    .section_5box {
      margin-top: 70px;
      font-size: 40px;
      color: #222222;
      font-weight: 500;
    }
    .section_5box1 {
      width: 50px;
      height: 4px;
      background: #0281e0;
      margin: 10px 0;
    }
    .section_5box2 {
      font-size: 20px;
      color: #666666;
    }
    .section_5box3 {
      width: 1480px;
      height: 614px;
      background: #ffffff;
      border-radius: 15px;
      margin-top: 40px;
      display: flex;
      flex-direction: column;
      .section_5_box3_top {
        height: 110px;
        display: flex;
        justify-content: space-between;
        border-bottom: 1px solid #c7c7c7;
        .text_s5_1 {
          color: #222222;
          font-size: 30px;
          margin-top: 29px;
          margin-left: 69px;
        }
        .chaimg {
          width: 33.94px;
          height: 33.94px;
          margin-top: 36px;
          margin-right: 71px;
        }
      }
      .section_5_box3_bottom {
        display: flex;
        height: 503px;
        justify-content: space-around;
        .secbox {
          display: flex;
          flex-direction: column;
          align-items: center;
          margin-top: 18px;
          .box_text {
            font-size: 24px;
            color: #666666;
            margin-bottom: 10px;
          }
          .box_text1 {
            font-size: 24px;
            color: #222222;
            margin-bottom: 40px;
          }
        }
      }
    }
    .section_5box4 {
      display: flex;
      width: 1480px;
      height: 455px;
      margin-top: 40px;
      flex-direction: column;
      background: #ffffff;
      .box_tops {
        height: 110px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #c7c7c7;
        .text_sp {
          display: block;
          margin-right: 71px;
        }
        .text_gong {
          font-size: 30px;
          color: #222222;
          margin-left: 69px;
          .chaimgs {
            width: 28px;
            height: 28px;
          }
        }
      }
      .box_bottoms {
        display: flex;
        flex-direction: column;
        .bootom_top {
          height: 271px;
          display: flex;
          justify-content: space-around;
          align-items: center;
          .box_q1 {
            display: flex;
            flex-direction: column;
            align-items: center;
            height: 191px;
            .pu_box {
              color: #666666;
              font-size: 24px;
              margin-bottom: 10px;
            }
            .Npu_box {
              color: #222222;
              font-size: 24px;
              margin-bottom: 40px;
            }
          }
          .box_q2 {
            display: flex;
            flex-direction: column;
            height: 191px;
            .pu_box {
              color: #666666;
              font-size: 24px;
              margin-bottom: 10px;
            }
            .Npu_box {
              color: #222222;
              font-size: 24px;
              margin-bottom: 40px;
            }
          }
        }
        .bootom_bottom {
          margin-top: 10px;
          display: flex;
          color: #666666;
          font-size: 24px;
          padding-left: 69px;
          .text_se1 {
            color: #222222;
            font-size: 24px;
          }
        }
      }
    }
  }
  .section_6 {
    width: 100%;
    height: 520px;
  }
  .section_7 {
    margin-top: 70px;
    width: 100%;
    height: 638px;
    background-image: url('@/assets/images/bjtu.png');
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    .section_text_1 {
      color: #ffffff;
      font-size: 40px;
      display: flex;
      justify-content: center;
      margin-top: 70px;
    }
    .section_text_2 {
      width: 50px;
      height: 4px;
      background: #ffffff;
      margin: 10px 0;
    }
    .section_text_3 {
      color: #ffffff;
      font-size: 20px;
      margin-bottom: 50px;
    }
    .section_box_1 {
      width: 1100px;
      height: 266px;
      display: flex;
      .box_1 {
        width: 300px;
        height: 266px;
        .peoimg {
          width: 80px;
          height: 80px;
        }
        .text_1 {
          font-size: 30px;
          color: #ffffff;
          margin-top: 20px;
          margin-bottom: 15px;
        }
        .text_2 {
          font-size: 24px;
          color: #ffffff;
        }
      }
      .box_2 {
        width: 300px;
        height: 266px;
        margin: 0 100px;
        .peoimg {
          width: 80px;
          height: 80px;
        }
        .text_1 {
          font-size: 30px;
          color: #ffffff;
          margin-top: 20px;
          margin-bottom: 15px;
        }
        .text_2 {
          font-size: 24px;
          color: #ffffff;
        }
      }
    }
    .section_box_2 {
      width: 450px;
      height: 54px;
      background: #0281e0;
      margin-top: 50px;
      border-radius: 8px;
      font-size: 24px;
      color: #ffffff;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}
</style>
