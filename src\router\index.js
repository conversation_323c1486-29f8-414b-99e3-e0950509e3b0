import { createRouter, createWebHashHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'

const router = createRouter({
  // history: createWebHistory(import.meta.env.BASE_URL),
  history: createWebHashHistory(),
  scrollBehavior() {
    return { top: 0 }
  },
  routes: [
    {
      path: '/', // 首页
      name: 'home',
      component: HomeView
    },
    {
      path: '/Software', // 软件
      name: 'software',
      component: () => import('../views/Software/index.vue'),
      children: [
        {
          path: '/Software/FollowUp',
          name: 'followUp',
          component: () => import('../views/Software/followUp.vue')
        },
        {
          path: '/Software/Telemedicine',
          name: 'telemedicine',
          component: () => import('../views/Software/telemedicine.vue')
        },
        {
          path: '/Software/HealthCheckup',
          name: 'healthCheckup',
          component: () => import('../views/Software/healthCheckup.vue')
        },
        {
          path: '/Software/Remoteconsultation',
          name: 'Remoteconsultation',
          component: () => import('../views/Software/Remoteconsultation.vue')
        }
      ]
    },
    {
      path: '/hardware',
      name: 'hardware',
      component: () => import('../views/hardware/index.vue'),
      children: [
        {
          path: '/hardware/Followpackage',
          name: 'followpackage',
          component: () => import('../views/hardware/followpackage.vue')
        },
        {
          path: '/hardware/hardwares',
          name: 'hardwares',
          component: () => import('../views/hardware/hardware.vue')
        },
        {
          path: '/hardware/Gateway',
          name: 'gateway',
          component: () => import('../views/hardware/gateway.vue')
        },
        {
          path: '/hardware/Robot',
          name: 'robot',
          component: () => import('../views/hardware/robot.vue')
        },
        {
          path: '/hardware/Allonemachine',
          name: 'allonemachine',
          component: () => import('../views/hardware/allonemachine.vue')
        }
      ]
    },
    {
      path: '/Solution',
      name: 'solution',
      component: () => import('../views/Solution/index.vue'),
      children: [
        {
          path: '/Solution/grassroots',
          name: 'grassroots',
          component: () => import('../views/Solution/solution.vue')
        },
        {
          path: '/Solution/tightplatform',
          name: 'tightplatform',
          component: () => import('../views/Solution/tightplatform.vue')
        }
      ]
    },
    {
      path: '/Case',
      name: 'case',
      component: () => import('../views/case.vue')
    },
    {
      path: '/Research',
      name: 'study',
      component: () => import('../views/study.vue')
    },
    {
      path: '/About',
      name: 'about',
      component: () => import('../views/about.vue')
    },
    {
      path: '/Detail',
      name: 'detail',
      component: () => import('../views/detail.vue')
    },
    {
      path: '/single/tongyishu',
      name: 'tongyishu',
      component: () => import('@/views/singlePage/tongyishu.vue')
    }
  ]
})

export default router
