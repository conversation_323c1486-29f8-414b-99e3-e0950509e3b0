<template>
  <div class="banner">
    <img src="@/assets/images/healthCheckup-banner.png" alt="" />
    <div class="info">
      <div class="label">居民健康体检系统</div>
      <div class="text">
        专为基层医疗机构设计的体检解决方案，适用于大规模、多人多队列并行的体检场景。该系统结合了智能硬件设备、云计算和大数据技术，旨在提高体检效率、准确性和便捷性，为基层医疗机构提供高效、优质的体检服务。
      </div>
    </div>
  </div>
  <div class="main">
    <div class="image-main">
      <div class="header-box">
        <div class="text-xi">系统特色</div>
        <div class="text-xian"></div>
        <div class="text-zi">SYSTEM FEATURES</div>
      </div>
      <div class="wrap-box">
        <div class="list-items-1">
          <div class="box_imgage-1"></div>
          <span class="text_s-1">适用于基层大规模体检</span>
          <span class="text_list-1"
            >通过智能硬件设备和云计算技术，能够快速有效地处理大量数据和患者信息，提高体检效率。</span
          >
        </div>
        <div class="list-items-2">
          <div class="box_image-2"></div>
          <span class="text_s-2">公卫云同步</span>
          <span class="text_list-2"
            >支持与当地公卫云实时同步数据，支持检查结果回填到公卫云系统，完善公卫云的系统数据。</span
          >
        </div>
        <div class="list-items-3">
          <div class="box_image-3"></div>
          <span class="text_s-3">大规模体检管理</span>
          <span class="text_list-3">适用于大规模的体检活动，能够快速有效地处理大量数据和患者信息。</span>
        </div>
        <div class="list-items-4">
          <div class="box_image-4"></div>
          <span class="text_s-4">跨院区管理</span>
          <span class="text_list-4">支持多院区之间的体检数据共享和管理，实现资源的优化配置。</span>
        </div>
        <div class="list-items-5">
          <div class="box_image-5"></div>
          <span class="text_s-5">实时监控与调度</span>
          <span class="text_list-5">能够对体检过程进行实时监控和调度，确保体检活动的顺利进行。</span>
        </div>
        <div class="list-items-6">
          <div class="box_image-6"></div>
          <span class="text_s-6">数据安全保障</span>
          <span class="text_list-6">采用严格的数据加密和权限管理制度，确保居民的个人隐私和数据安全。</span>
        </div>
      </div>
    </div>
    <div class="section_5">
      <div class="text-group_bing">
        <span class="text_s">并行体检&nbsp;高端高效</span>
        <span class="text_op">多人多队列并行体检，适用于基层大规模体检，是医疗机构开展体检业务的得力助手。</span>
      </div>
    </div>
    <div class="section_6">
      <div class="section_6-top">
        <div class="text_gong">功能特点</div>
        <div class="text_lan"></div>
        <div class="text_zimu">SPECIFICATIONS</div>
      </div>
      <div class="section_6-bootom">
        <div class="section_left"></div>
        <div class="section_right">
          <div class="xiao">
            <div class="box_4"></div>
            体检预约
          </div>
          <div class="xia">居民可以通过手机或电脑进行体检预约，选择适合自己的体检项目和时间。</div>
        </div>
      </div>
    </div>
    <div class="section_7">
      <div class="section_7-box">
        <div class="section_7_left">
          <div class="se">
            <div class="box_4"></div>
            智能体检
          </div>
          <div class="box_list_1">
            通过智能硬件设备完成各项体检项目，系统自动采集和分析体检数据；支持多个体检队列同时进行，提高体检效率；同时，适用于大规模的体检活动，能够快速有效地处理大量数据和患者信息。
          </div>
          <div class="se_1">
            <div class="box_4"></div>
            健康管理
          </div>
          <div class="box_list_1">根据居民的体检数据和健康状况，提供个性化的健康管理建议和健康风险评估。</div>
        </div>
        <div class="section_7_right"></div>
      </div>
    </div>
    <div class="section_8">
      <div class="section_7-box">
        <div class="section_7_left"></div>
        <div class="section_7_right">
          <div class="se">
            <div class="box_4"></div>
            数据统计与分析
          </div>
          <div class="box_list_1">对居民的体检数据进行统计和分析，为基层医疗机构提供居民健康状况的数据支持。</div>
        </div>
      </div>
    </div>
    <div class="section_9">
      <div class="section_7-box">
        <div class="section_7_left">
          <div class="se">
            <div class="box_4"></div>
            报告生成与查看
          </div>
          <div class="box_list_1">自动生成居民的体检报告，居民可以通过手机或电脑查看自己的体检报告和健康建议。</div>
        </div>
        <div class="section_7_right"></div>
      </div>
    </div>
    <div class="section_10">
      <div class="section_7-box">
        <div class="section_7_left"></div>
        <div class="section_7_right">
          <div class="se">
            <div class="box_4"></div>
            跨院区管理
          </div>
          <div class="box_list_1">支持多院区之间的体检数据共享和管理，实现资源的优化配置。</div>
          <div class="se_1">
            <div class="box_4"></div>
            数据安全保障
          </div>
          <div class="box_list_1">采用严格的数据加密和权限管理制度，确保居民的个人隐私和数据安全。</div>
        </div>
      </div>
    </div>
    <div class="section_11">
      <div class="section_11_top">
        <div class="text_xi">系统价值</div>
        <div class="text_se"></div>
        <div class="text_zimu">VALUES</div>
      </div>
      <div class="section_11_bootom">
        <div class="xiao_box">
          <div class="xiaobox_left">
            <span class="text_44">01</span>
          </div>
          <div class="xiaobox_right">
            <div class="xiaobox_top">提高体检效率</div>
            <div class="xiaobox_bottom">
              通过智能硬件设备和云计算技术，能够快速有效地处理大量数据和患者信息，提高体检效率。
            </div>
          </div>
        </div>
        <div class="xiao_box">
          <div class="xiaobox_left">
            <span class="text_44">03</span>
          </div>
          <div class="xiaobox_right">
            <div class="xiaobox_top">提高服务质量</div>
            <div class="xiaobox_bottom">
              通过优化资源配置和提高设备使用效率等手段，提高基层医疗机构的服务质量和患者满意度。
            </div>
          </div>
        </div>
        <div class="xiao_box">
          <div class="xiaobox_left">
            <span class="text_44">02</span>
          </div>
          <div class="xiaobox_right">
            <div class="xiaobox_top">降低运营成本</div>
            <div class="xiaobox_bottom">通过自动化和智能化的管理手段，降低基层医疗机构的运营成本和管理成本。</div>
          </div>
        </div>
        <div class="xiao_box">
          <div class="xiaobox_left">
            <span class="text_44">04</span>
          </div>
          <div class="xiaobox_right">
            <div class="xiaobox_top">促进医疗资源共享</div>
            <div class="xiaobox_bottom">
              通过多院区、多层级管理和云端服务支持等手段，促进医疗资源的共享和优化配置，提高医疗资源的利用率。
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="section_12">
      <div class="section_12_top">
        <div class="text-xi">用户之声</div>
        <div class="text-xian"></div>
        <div class="text-zimu">VOCICE OF USERS</div>
      </div>
      <div class="section_12_bottom">
        <div class="box_16">
          <img src="@/assets/images/rentou.png" alt="" style="margin-top: 2.5rem" />
          <div class="text-group_43">
            <span class="text_58">某医院</span>
            <span class="text_59">副院长</span>
          </div>
          <div class="image-wrapper_4">
            <img referrerpolicy="no-referrer" src="@/assets/images/zhixian.png" style="width: 100%; height: 100%" />
            <span class="text_60"
              >“居民健康体检系统的管理方式不仅能帮助医院提高管理的精准度，更能帮助医护人员根据自己的成果总结与自省。带来了一种自发提高的方式”</span
            >
          </div>
        </div>
        <div class="box_17">
          <img src="@/assets/images/black.png" alt="" style="margin-top: 2.5rem" />
          <div class="text-group_44">
            <span class="text_61">某医院</span>
            <span class="text_62">主治医师</span>
          </div>
          <div class="image-wrapper_4">
            <img referrerpolicy="no-referrer" src="@/assets/images/zhixian.png" style="width: 100%; height: 100%" />
          </div>
          <span class="text_63"
            >“合理的架构和有序的运作是系统取得成效的关键因素。正因为有了居民健康体检系统，我们的工作更加高效，给我们带来了极大的便利”</span
          >
        </div>
        <div class="box_18">
          <img src="@/assets/images/black.png" alt="" style="margin-top: 2.5rem" />
          <div class="text-group_44">
            <span class="text_61">某医院</span>
            <span class="text_62">主任</span>
          </div>
          <div class="image-wrapper_4">
            <img referrerpolicy="no-referrer" src="@/assets/images/zhixian.png" style="width: 100%; height: 100%" />
          </div>
          <span class="text_63"
            >“不做没意义的评价，不做没有严肃性的数字，不做没有信服力的结论，居民健康体检系统的精准定位与其开放的系统架构使得健康有据可依”</span
          >
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.banner {
  max-width: 160rem;
  width: 100%;
  position: relative;
  margin: 0 auto;
  margin-top: 3.5rem;
  img {
    width: 100%;
    height: auto;
  }
  .info {
    position: absolute;
    width: 37.5rem;
    height: 15.5rem;
    top: 11rem;
    right: 12.5rem;
    .label {
      font-size: 2.75rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      color: #ffffff;
      line-height: 4.375rem;
      margin-bottom: 1.875rem;
    }
    .text {
      width: 37.5rem;
      font-size: 1.25rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #ffffff;
      line-height: 2.3125rem;
    }
  }
}
.image-main {
  background-image: url('@/assets/images/ruan-background.png');
  background-size: 100% 100%;
  width: 100%;
  height: 48.6875rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 4.375rem;
  max-width: 160rem;
  overflow: hidden;
  .header-box {
    width: 10.75rem;
    height: 6.1875rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    .text-xi {
      color: #222222;
      font-size: 1.875rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
    }
    .text-xian {
      width: 3.125rem;
      height: 0.25rem;
      background-color: #0281e0;
      margin: 0.625rem 0;
      margin: 0.625rem 0;
    }
    .text-zi {
      height: 1.5625rem;
      font-size: 1rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #666666;
    }
  }
  .wrap-box {
    display: flex;
    flex-wrap: wrap;
    width: 80rem;
    margin-top: 2.5rem;
    .list-items-1 {
      background-color: rgba(255, 255, 255, 1);
      border-radius: 0.9375rem;
      width: 25rem;
      height: 14.375rem;
      border: 0.0625rem solid rgba(222, 222, 222, 1);
      margin: 0 2.5rem 2.5rem 0;
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    .box_imgage-1 {
      border-radius: 0.9375rem;
      margin: 2.125rem 0 0 0;
      width: 3.125rem;
      height: 3.125rem;
      background: url('@/assets/images/duanxin.png');
      background-size: 100% 100%;
    }
    .text_s-1 {
      height: 2.0625rem;
      font-size: 1.5rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      color: #222222;
      line-height: 2.0625rem;
      margin-top: 1.25rem;
    }
    .text_list-1 {
      width: 21.25rem;
      height: 2.75rem;
      font-size: 1rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #666666;
      line-height: 1.375rem;
      margin-top: 0.9375rem;
    }
    .list-items-2 {
      background-color: rgba(255, 255, 255, 1);
      border-radius: 0.9375rem;
      width: 25rem;
      height: 14.375rem;
      border: 0.0625rem solid rgba(222, 222, 222, 1);
      margin: 0 2.5rem 2.5rem 0;
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    .box_image-2 {
      border-radius: 0.9375rem;
      width: 3.125rem;
      height: 3.125rem;
      margin: 2.125rem 0 0 0;
      background: url('@/assets/images/caidan.png');
      background-size: 100% 100%;
    }
    .text_s-2 {
      height: 2.0625rem;
      font-size: 1.5rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      color: #222222;
      line-height: 2.0625rem;
      margin-top: 1.25rem;
    }
    .text_list-2 {
      width: 21.25rem;
      height: 2.75rem;
      font-size: 1rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #666666;
      line-height: 1.375rem;
      margin-top: 0.9375rem;
    }
    .list-items-3 {
      background-color: rgba(255, 255, 255, 1);
      border-radius: 0.9375rem;
      width: 25rem;
      height: 14.375rem;
      border: 0.0625rem solid rgba(222, 222, 222, 1);
      margin: 0 0 2.5rem 0;
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    .box_image-3 {
      border-radius: 0.9375rem;
      width: 3.125rem;
      height: 3.125rem;
      margin: 1.875rem 0 0 0;
      background: url('@/assets/images/shouji.png');
      background-size: 100% 100%;
    }
    .text_s-3 {
      height: 2.0625rem;
      font-size: 1.5rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      color: #222222;
      line-height: 2.0625rem;
      margin-top: 1.25rem;
    }
    .text_list-3 {
      width: 21.25rem;
      height: 2.75rem;
      font-size: 1rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #666666;
      line-height: 1.375rem;
      margin-top: 0.9375rem;
    }
    .list-items-4 {
      background-color: rgba(255, 255, 255, 1);
      border-radius: 0.9375rem;
      width: 25rem;
      height: 14.375rem;
      border: 0.0625rem solid rgba(222, 222, 222, 1);
      margin: 0 2.5rem 2.5rem 0;
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    .box_image-4 {
      border-radius: 0.9375rem;
      width: 3.125rem;
      height: 3.125rem;
      margin: 1.875rem 0 0 0;
      background: url('@/assets/images/zhenguan.png');
      background-size: 100% 100%;
    }
    .text_s-4 {
      height: 2.0625rem;
      font-size: 1.5rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      color: #222222;
      line-height: 2.0625rem;
      margin-top: 1.25rem;
    }
    .text_list-4 {
      width: 21.25rem;
      height: 2.75rem;
      font-size: 1rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #666666;
      line-height: 1.375rem;
      margin-top: 0.9375rem;
    }
    .list-items-5 {
      background-color: rgba(255, 255, 255, 1);
      border-radius: 0.9375rem;
      width: 25rem;
      height: 14.375rem;
      border: 0.0625rem solid rgba(222, 222, 222, 1);
      margin: 0 2.5rem 0 0;
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    .box_image-5 {
      border-radius: 0.9375rem;
      width: 3.125rem;
      height: 3.125rem;
      margin: 1.875rem 0 0 0;
      background: url('@/assets/images/baibaoxiang.png');
      background-size: 100% 100%;
    }
    .text_s-5 {
      height: 2.0625rem;
      font-size: 1.5rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      color: #222222;
      line-height: 2.0625rem;
      margin-top: 1.25rem;
    }
    .text_list-5 {
      width: 21.25rem;
      height: 2.75rem;
      font-size: 1rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #666666;
      line-height: 1.375rem;
      margin-top: 0.9375rem;
    }
    .list-items-6 {
      background-color: rgba(255, 255, 255, 1);
      border-radius: 0.9375rem;
      width: 25rem;
      height: 14.375rem;
      border: 0.0625rem solid rgba(222, 222, 222, 1);
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    .box_image-6 {
      border-radius: 0.9375rem;
      width: 3.125rem;
      height: 3.125rem;
      margin: 1.875rem 0 0 0;
      background: url('@/assets/images/anquan.png');
      background-size: 100% 100%;
    }
    .text_s-6 {
      height: 2.0625rem;
      font-size: 1.5rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      color: #222222;
      line-height: 2.0625rem;
      margin-top: 1.25rem;
    }
    .text_list-6 {
      width: 21.25rem;
      height: 2.75rem;
      font-size: 1rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #666666;
      line-height: 1.375rem;
      margin-top: 0.9375rem;
    }
  }
}
.main {
  max-width: 160rem;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  margin: 0 auto;
  overflow: hidden;
}
.section_5 {
  background-image: url('@/assets/images/bing.png');
  width: 100%;
  height: 25rem;
  background-size: 100% 100%;
  max-width: 160rem;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  .text-group_bing {
    width: 1280px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    .text_s {
      width: 28.1875rem;
      overflow-wrap: break-word;
      color: rgba(34, 34, 34, 1);
      font-size: 2.5rem;
      font-family: LiGothicMed;
      font-weight: 400;
      text-align: left;
      white-space: nowrap;
    }
    .text_op {
      width: 50rem;
      overflow-wrap: break-word;
      color: rgba(102, 102, 102, 1);
      font-size: 1.5rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      text-align: left;
      margin-top: 1.875rem;
    }
  }
}
.section_6 {
  width: 100%;
  max-width: 160rem;
  overflow: hidden;
  height: 43rem;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  align-items: center;
  .section_6-top {
    width: 13.5rem;
    height: 6.75rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 4.375rem 0 2.5rem 0;
    margin: 4.375rem 0 2.5rem 0;
    .text_gong {
      color: #222222;
      font-size: 1.875rem;
      font-size: 1.875rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
    }
    .text_lan {
      background-color: #0281e0;
      width: 3.125rem;
      height: 0.25rem;
      margin: 0.625rem 0;
    }
    .text_zimu {
      font-size: 1rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #666666;
      line-height: 1.75rem;
    }
  }
  .section_6-bootom {
    width: 80.5625rem;
    width: 80.5625rem;
    height: 25rem;
    display: flex;
    .section_left {
      width: 37.5rem;
      height: 25rem;
      background-image: url('@/assets/images/gong-img.png');
      background-size: 100% 100%;
      background-size: 100% 100%;
    }
    .section_right {
      .xiao {
        display: flex;
        margin-top: 10.1875rem;
        margin-top: 10.1875rem;
        color: #222222;
        font-size: 1.5rem;
        line-height: 2.0625rem;
        font-size: 1.5rem;
        line-height: 2.0625rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        margin-left: 4.375rem;
        align-items: center;
        .box_4 {
          background-color: rgba(2, 129, 224, 1);
          border-radius: 0.125rem;
          width: 0.25rem;
          height: 1.25rem;
          margin-right: 0.9375rem;
        }
        margin-left: 4.375rem;
        align-items: center;
        .box_4 {
          background-color: rgba(2, 129, 224, 1);
          border-radius: 0.125rem;
          width: 0.25rem;
          height: 1.25rem;
          margin-right: 0.9375rem;
        }
      }
      .xia {
        margin-top: 1.25rem;
        margin-left: 5.5625rem;
        font-size: 1.125rem;
        font-size: 1.125rem;
        color: #666666;
      }
    }
  }
}
.section_7 {
  max-width: 160rem;
  overflow: hidden;
  width: 100%;
  height: 33.75rem;
  display: flex;
  background-color: #f3f7ff;
  align-items: center;
  justify-content: center;
  .section_7-box {
    width: 80.5625rem;
    width: 80.5625rem;
    height: 25rem;
    display: flex;
    .section_7_left {
      flex: 1;
      .se {
        display: flex;
        align-items: center;
        color: #222222;
        font-size: 1.5rem;
        font-size: 1.5rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        margin-top: 3.4375rem;
        .box_4 {
          background-color: rgba(2, 129, 224, 1);
          border-radius: 0.125rem;
          width: 0.25rem;
          height: 1.25rem;
          margin: 0.1875rem 0.9375rem 0 0;
        }
      }
      .se_1 {
        display: flex;
        align-items: center;
        color: #222222;
        font-size: 1.5rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        margin-top: 2.5rem;
        .box_4 {
          background-color: rgba(2, 129, 224, 1);
          border-radius: 0.125rem;
          width: 0.25rem;
          height: 1.25rem;
          margin: 0.1875rem 0.9375rem 0 0;
        }
      }
      .box_list_1 {
        width: 37.5rem;
        color: #666666;
        font-size: 1rem;
        margin-top: 1.25rem;
        margin-left: 1.1875rem;
      }
    }
    .section_7_right {
      width: 37.5rem;
      height: 25rem;
      background-image: url('@/assets/images/fengxian.png');
      background-size: 100% 100%;
    }
  }
}
.section_8 {
  width: 100%;
  max-width: 160rem;
  overflow: hidden;
  height: 33.75rem;
  display: flex;
  background-color: #ffffff;
  align-items: center;
  justify-content: center;
  .box_4 {
    background-color: rgba(2, 129, 224, 1);
    border-radius: 0.125rem;
    width: 0.25rem;
    height: 1.25rem;
    margin: 0.1875rem 0.9375rem 0 0;
  }
  .section_7-box {
    width: 80.5625rem;
    width: 80.5625rem;
    height: 25rem;
    display: flex;
    .section_7_left {
      width: 37.5rem;
      height: 25rem;
      background-image: url('@/assets/images/yichang.png');
      background-size: 100% 100%;
      margin-right: 4.375rem;
    }
    .section_7_right {
      flex: 1;
      flex: 1;
      display: flex;
      flex-direction: column;
      flex-direction: column;
      .se {
        display: flex;
        align-items: center;
        color: #222222;
        font-size: 1.5rem;
        font-size: 1.5rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        margin-top: 10.1875rem;
        margin-top: 10.1875rem;
      }
      .box_list_1 {
        margin-top: 1.25rem;
        width: 37.5rem;
        color: #666666;
        font-size: 1rem;
        margin-left: 1.1875rem;
        width: 37.5rem;
        color: #666666;
        font-size: 1rem;
        margin-left: 1.1875rem;
      }
    }
  }
}
.section_9 {
  max-width: 160rem;
  overflow: hidden;
  width: 100%;
  height: 33.75rem;
  display: flex;
  background-color: #f3f7ff;
  align-items: center;
  justify-content: center;
  .box_4 {
    background-color: rgba(2, 129, 224, 1);
    border-radius: 0.125rem;
    width: 0.25rem;
    height: 1.25rem;
    margin: 0.1875rem 0.9375rem 0 0;
  }
  .section_7-box {
    width: 80.5625rem;
    width: 80.5625rem;
    height: 25rem;
    display: flex;
    .section_7_left {
      flex: 1;
      flex: 1;
      display: flex;
      flex-direction: column;
      flex-direction: column;
      .se {
        display: flex;
        align-items: center;
        color: #222222;
        font-size: 1.5rem;
        font-size: 1.5rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        margin-top: 10.1875rem;
        margin-top: 10.1875rem;
      }
      .box_list_1 {
        width: 37.5rem;
        width: 37.5rem;
        color: #666666;
        font-size: 1rem;
        font-size: 1rem;
        margin-top: 1.25rem;
        margin-left: 1.1875rem;
        margin-left: 1.1875rem;
      }
    }
    .section_7_right {
      width: 37.5rem;
      height: 25rem;
      background-image: url('@/assets/images/yichangpdf.png');
      background-size: 100% 100%;
      margin-right: 4.375rem;
    }
  }
}
.section_10 {
  max-width: 160rem;
  overflow: hidden;
  width: 100%;
  height: 33.75rem;
  display: flex;
  background-color: #ffffff;
  align-items: center;
  justify-content: center;
  .box_4 {
    background-color: rgba(2, 129, 224, 1);
    border-radius: 0.125rem;
    width: 0.25rem;
    height: 1.25rem;
    margin: 0.1875rem 0.9375rem 0 0;
  }
  .section_7-box {
    width: 80.5625rem;
    width: 80.5625rem;
    height: 25rem;
    display: flex;
    .section_7_left {
      width: 37.5rem;
      height: 25rem;
      background-image: url('@/assets/images/gexinghua.png');
      background-size: 100% 100%;
      margin-right: 4.375rem;
    }
    .section_7_right {
      flex: 1;
      display: flex;
      flex-direction: column;
      flex: 1;
      display: flex;
      flex-direction: column;
      .se {
        display: flex;
        align-items: center;
        width: 9.375rem;
        height: 2.0625rem;
        font-size: 1.5rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #222222;
        line-height: 2.0625rem;
        margin-top: 6.5625rem;
      }
      .se_1 {
        display: flex;
        align-items: center;
        width: 10.625rem;
        height: 2.0625rem;
        font-size: 1.5rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #222222;
        line-height: 2.0625rem;
        margin-top: 2.5rem;
      }
      .box_list_1 {
        width: 37.5rem;
        height: 1.375rem;
        font-size: 1rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #666666;
        line-height: 1.375rem;
        margin-top: 1.25rem;
        margin-left: 1.1875rem;
        margin-left: 1.1875rem;
      }
    }
  }
}
.section_11 {
  max-width: 160rem;
  overflow: hidden;
  width: 100%;
  height: 30.8125rem;
  height: 30.8125rem;
  background-image: url('@/assets/images/xitongbgr.png');
  background-size: 100% 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  .section_11_top {
    width: 13.375rem;
    height: 6.1875rem;
    width: 13.375rem;
    height: 6.1875rem;
    margin-top: 4.375rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    .text_xi {
      width: 9rem;
      height: 3.125rem;
      font-size: 2.25rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      color: #222222;
      line-height: 3.125rem;
    }
    .text_se {
      width: 3.125rem;
      height: 0.25rem;
      background-color: #0281e0;
      margin: 0.625rem 0;
    }
    .text_zimu {
      width: 13.375rem;
      height: 1.5625rem;
      font-size: 1.125rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #666666;
      line-height: 1.5625rem;
      text-align: center;
    }
  }
  .section_11_bootom {
    width: 91.875rem;
    height: 31.25rem;
    margin-top: 2.5rem;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    justify-content: center;
    .xiao_box {
      display: flex;
      width: 36.875rem;
      height: 5.4375rem;
      width: 36.875rem;
      height: 5.4375rem;
      margin-right: 1.25rem;
      align-items: center;
      justify-content: center;
      .xiaobox_left {
        height: 4.375rem;
        background: url('@/assets/images/fangbian.png');
        height: 4.375rem;
        background: url('@/assets/images/fangbian.png');
        background-size: 100% 100%;
        width: 4.375rem;
        width: 4.375rem;
        display: flex;
        flex-direction: column;
        .text_44 {
          width: 2.5625rem;
          height: 3.5rem;
          width: 2.5625rem;
          height: 3.5rem;
          color: rgba(255, 255, 255, 1);
          font-size: 2.5rem;
          font-size: 2.5rem;
          font-family: PingFangSC-Medium;
          font-weight: 500;
          white-space: nowrap;
          margin: 0.125rem 0 0 0.75rem;
          margin: 0.125rem 0 0 0.75rem;
        }
      }
      .xiaobox_right {
        width: 31.25rem;
        width: 31.25rem;
        display: flex;
        flex-direction: column;
        margin-left: 1.25rem;
        margin-left: 1.25rem;
        .xiaobox_top {
          margin-bottom: 0.625rem;
          margin-bottom: 0.625rem;
          color: #222222;
          font-size: 1.5rem;
          font-size: 1.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
        }
        .xiaobox_bottom {
          width: 31.25rem;
          width: 31.25rem;
          color: #222222;
          font-size: 1rem;
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
        }
      }
    }
  }
}
.section_12 {
  max-width: 160rem;
  overflow: hidden;
  background-image: url('@/assets/images/yonghu.png');
  height: 39.875rem;
  height: 39.875rem;
  margin-bottom: 0.0625rem;
  width: 100%;
  background-size: 100% 100%;
  justify-content: flex-center;
  display: flex;
  flex-direction: column;
  align-items: center;
  .section_12_top {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #ffffff;
    font-size: 2.5rem;
    margin-top: 3.125rem;
    .text-xi {
      font-size: 2.125rem;
    }
    .text-xian {
      width: 3.125rem;
      height: 0.25rem;
      background-color: #ffffff;
      margin: 0.625rem 0;
    }
    .text-zimu {
      font-size: 1rem;
    }
  }
  .section_12_bottom {
    display: flex;
    .box_17 {
      border-radius: 0.9375rem;
      width: 29.375rem;
      height: 21.875rem;
      border: 0.0625rem solid rgba(151, 151, 151, 1);
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-left: 2.1875rem;
      margin-top: 2.5rem;
      background-color: #ffffff;
      .image-wrapper_4 {
        width: 24.375rem;
        height: 0.875rem;
        margin: 1.25rem 0 0 0;
      }
      .text-group_44 {
        width: 4.5rem;
        margin: 0.625rem 0 0 0;
        display: flex;
        flex-direction: column;
        width: 4.5rem;
        margin: 0.625rem 0 0 0;
        display: flex;
        flex-direction: column;
      }
      .text_61 {
        width: 4.5rem;
        height: 2.0625rem;
        font-size: 1.5rem;
        font-family: PingFangSC, PingFang SC;
        width: 4.5rem;
        height: 2.0625rem;
        font-size: 1.5rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #222222;
        text-align: center;
        color: #222222;
        text-align: center;
      }
      .text_62 {
        width: 4.5rem;
        height: 1.375rem;
        width: 4.5rem;
        height: 1.375rem;
        color: rgba(102, 102, 102, 1);
        font-size: 1rem;
        font-size: 1rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        text-align: center;
        margin-top: 0.375rem;
        text-align: center;
        margin-top: 0.375rem;
      }
      .text_63 {
        width: 24.375rem;
        overflow-wrap: break-word;
        color: rgba(102, 102, 102, 1);
        font-size: 1rem;
        font-size: 1rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        text-align: left;
        margin: 1.25rem 2.5rem 0 2.5rem;
        margin: 1.25rem 2.5rem 0 2.5rem;
      }
    }
    .box_16 {
      border-radius: 0.9375rem;
      background-color: #ffffff;
      background-color: #ffffff;
      width: 29.375rem;
      height: 21.875rem;
      height: 21.875rem;
      border: 0.0625rem solid rgba(151, 151, 151, 1);
      margin-top: 2.5rem;
      display: flex;
      flex-direction: column;
      align-items: center;
      .text-group_43 {
        width: 4.5rem;
        margin: 0.625rem 0 0 0;
      }
      .text_58 {
        width: 4.5rem;
        height: 2.0625rem;
        width: 4.5rem;
        height: 2.0625rem;
        color: rgba(34, 34, 34, 1);
        font-size: 1.5rem;
        font-family: PingFangSC, PingFang SC;
        font-size: 1.5rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        text-align: left;
      }
      .text_59 {
        width: 3rem;
        height: 1.375rem;
        width: 3rem;
        height: 1.375rem;
        color: rgba(102, 102, 102, 1);
        font-size: 1rem;
        font-size: 1rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        margin: 0.375rem 0 0 0.75rem;
        margin: 0.375rem 0 0 0.75rem;
      }
      .image-wrapper_4 {
        width: 24.375rem;
        height: 0.875rem;
        margin: 1.25rem 0 0 0;
      }
      .text_60 {
        width: 24.375rem;
        height: 4.875rem;
        overflow-wrap: break-word;
        color: rgba(102, 102, 102, 1);
        font-size: 1rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        text-align: left;
      }
    }
    .box_18 {
      border-radius: 0.9375rem;
      width: 29.375rem;
      height: 21.875rem;
      height: 21.875rem;
      border: 0.0625rem solid rgba(151, 151, 151, 1);
      display: flex;
      flex-direction: column;
      align-items: center;
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-left: 2.1875rem;
      margin-top: 2.5rem;
      background-color: #ffffff;
      .image-wrapper_4 {
        width: 24.375rem;
        height: 0.875rem;
        margin: 1.25rem 0 0 0;
      }
      .text-group_44 {
        width: 4.5rem;
        margin: 0.625rem 0 0 0;
        display: flex;
        flex-direction: column;
      }
      .text_61 {
        width: 4.5rem;
        height: 2.0625rem;
        font-size: 1.5rem;
        font-family: PingFangSC, PingFang SC;
        width: 4.5rem;
        height: 2.0625rem;
        font-size: 1.5rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #222222;
        text-align: center;
        color: #222222;
        text-align: center;
      }
      .text_62 {
        width: 4.5rem;
        height: 1.375rem;
        width: 4.5rem;
        height: 1.375rem;
        color: rgba(102, 102, 102, 1);
        font-size: 1rem;
        font-size: 1rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        text-align: center;
        margin-top: 0.375rem;
        text-align: center;
        margin-top: 0.375rem;
      }
      .text_63 {
        width: 24.375rem;
        overflow-wrap: break-word;
        color: rgba(102, 102, 102, 1);
        font-size: 1rem;
        font-size: 1rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        text-align: left;
        margin: 1.25rem 2.5rem 0 2.5rem;
        margin: 1.25rem 2.5rem 0 2.5rem;
      }
    }
  }
}
</style>
