<script setup>
import { ref } from 'vue'
const isTrue = ref('0')
const select = ref(true)
const selects = ref(true)
const active = ref(1)
const ClickFun = val => {
  isTrue.value = val
}
const slectFun = () => {
  select.value = !select.value
}
const slectFuns = () => {
  selects.value = !selects.value
}
const ActiveFuns = val => {
  active.value = val
}
const ActiveFunes = index => {
  if (index === '加') {
    if (active.value === 5) {
      active.value = 1
    } else {
      active.value += 1
    }
  } else {
    if (active.value === 1) {
      active.value = 5
    } else {
      active.value -= 1
    }
  }
}
</script>
<template>
  <div class="banner">
    <div class="section_1">
      <div class="section_1_box">
        <div class="box_1">健康检测一体机</div>
        <div class="box_2">智能高端一体化设计，智慧体检，一机搞定。</div>
      </div>
    </div>
    <div class="section_2">
      <span class="text_10">智能硬件产品助您超越所能</span>
      <div class="section_61"></div>
      <span class="text_11">INTELLIGENT&nbsp;HARDWARE</span>
      <div v-show="active === 1" class="list_3">
        <div class="text-group_1-0" @click="ActiveFuns(1)">
          <span class="text_12-0">健康检测一体机</span>
          <span class="text_13-0">智慧体检 一机搞定</span>
          <img
            class="image_4-0"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng3b9a8121db0a1bcc7c1a65f5a79361a0bbbf877f3a659cc948ad087a9b9f03cb"
          />
        </div>
        <div class="text-group_1-1" @click="ActiveFuns(2)">
          <span class="text_12-1">智慧随访包</span>
          <span class="text_13-1">一机多用 实时上传</span>
          <img
            class="image_2-1"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
        <div class="text-group_1-2" @click="ActiveFuns(3)">
          <span class="text_12-2">边缘计算网关</span>
          <span class="text_13-2">万物互联 实时监控</span>
          <img
            class="image_2-2"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
          <img
            class="image_3-2"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
        <div class="text-group_1-3" @click="ActiveFuns(4)">
          <span class="text_12-3">AI基层辅助机器人</span>
          <span class="text_13-3">人脸识别 自助问诊 远程诊断</span>
          <img
            class="image_3-3"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
      </div>
      <div v-show="active === 2" class="list_3">
        <div class="text-group_1-0" @click="ActiveFuns(2)">
          <span class="text_12-0">智慧随访包</span>
          <span class="text_13-0">一机多用 实时上传</span>
          <img
            class="image_4-0"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng3b9a8121db0a1bcc7c1a65f5a79361a0bbbf877f3a659cc948ad087a9b9f03cb"
          />
        </div>
        <div class="text-group_1-1" @click="ActiveFuns(3)">
          <span class="text_12-1">边缘计算网关</span>
          <span class="text_13-1">万物互联 实时监控</span>
          <img
            class="image_2-1"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
        <div class="text-group_1-2" @click="ActiveFuns(4)">
          <span class="text_12-2">AI基层辅助机器人</span>
          <span class="text_13-2">人脸识别 自助问诊 远程诊断</span>
          <img
            class="image_2-2"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
          <img
            class="image_3-2"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
        <div class="text-group_1-3" @click="ActiveFuns(5)">
          <span class="text_12-3">卫软穿戴设备</span>
          <span class="text_13-3">实时监测 安心保障</span>
          <img
            class="image_3-3"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
      </div>
      <div v-show="active === 3" class="list_3">
        <div class="text-group_1-0" @click="ActiveFuns(3)">
          <span class="text_12-0">边缘计算网关</span>
          <span class="text_13-0">万物互联 实时监控</span>
          <img
            class="image_4-0"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng3b9a8121db0a1bcc7c1a65f5a79361a0bbbf877f3a659cc948ad087a9b9f03cb"
          />
        </div>
        <div class="text-group_1-1" @click="ActiveFuns(4)">
          <span class="text_12-1">AI基层辅助机器人</span>
          <span class="text_13-1">人脸识别 自助问诊 远程诊断</span>
          <img
            class="image_2-1"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
        <div class="text-group_1-2" @click="ActiveFuns(5)">
          <span class="text_12-2">卫软穿戴设备</span>
          <span class="text_13-2">实时监测 安心保障</span>
          <img
            class="image_2-2"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
          <img
            class="image_3-2"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
        <div class="text-group_1-3" @click="ActiveFuns(1)">
          <span class="text_12-3">健康检测一体机</span>
          <span class="text_13-3">智慧体检 一机搞定</span>
          <img
            class="image_3-3"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
      </div>
      <div v-show="active === 4" class="list_3">
        <div class="text-group_1-0" @click="ActiveFuns(4)">
          <span class="text_12-0">AI基层辅助机器人</span>
          <span class="text_13-0">人脸识别 自助问诊 远程诊断</span>
          <img
            class="image_4-0"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng3b9a8121db0a1bcc7c1a65f5a79361a0bbbf877f3a659cc948ad087a9b9f03cb"
          />
        </div>
        <div class="text-group_1-1" @click="ActiveFuns(5)">
          <span class="text_12-1">卫软穿戴设备</span>
          <span class="text_13-1">实时监测 安心保障</span>
          <img
            class="image_2-1"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
        <div class="text-group_1-2" @click="ActiveFuns(1)">
          <span class="text_12-2">健康检测一体机</span>
          <span class="text_13-2">智慧体检 一机搞定</span>
          <img
            class="image_2-2"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
          <img
            class="image_3-2"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
        <div class="text-group_1-3" @click="ActiveFuns(2)">
          <span class="text_12-3">智慧随访包</span>
          <span class="text_13-3">一机多用 实时上传</span>
          <img
            class="image_3-3"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
      </div>
      <div v-show="active === 5" class="list_3">
        <div class="text-group_1-0" @click="ActiveFuns(5)">
          <span class="text_12-0">卫软穿戴设备</span>
          <span class="text_13-0">实时监测&nbsp;安心保障</span>
          <img
            class="image_4-0"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng3b9a8121db0a1bcc7c1a65f5a79361a0bbbf877f3a659cc948ad087a9b9f03cb"
          />
        </div>
        <div class="text-group_1-1" @click="ActiveFuns(1)">
          <span class="text_12-1">健康检测一体机</span>
          <span class="text_13-1">智慧体检&nbsp;一机搞定</span>
          <img
            class="image_2-1"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
        <div class="text-group_1-2" @click="ActiveFuns(2)">
          <span class="text_12-2">智彗随访包</span>
          <span class="text_13-2">一机多用&nbsp;实时上传</span>
          <img
            class="image_2-2"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
          <img
            class="image_3-2"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
        <div class="text-group_1-3" @click="ActiveFuns(3)">
          <span class="text_12-3">边缘计算网关</span>
          <span class="text_13-3">万能互联&nbsp;实时监控</span>
          <img
            class="image_3-3"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
      </div>
    </div>
    <div v-show="active === 1" class="section_3">
      <img
        class="image_6"
        referrerpolicy="no-referrer"
        src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng5b727b1da8591b50a16f335462b668ed3c9d990d7539f3741af9ebde6c96b421"
        @click="ActiveFunes('减')"
      />
      <div class="group_2">
        <img src="@/assets/images/jixiebi.png" alt="" />
      </div>
      <div class="box_31">
        <span class="text_14">健康信息工作站</span>
        <div class="section_8s"></div>
        <span class="text_15"
          >精准之道，内外兼修。高端配置高度集成一体化设计，健康宣教和自助体检结合，30多项健康检测指标，集秒级快检、健康指导、风险评估为一体，助力就诊、体检等多个场景，数据实时上传与统计。</span
        >
      </div>
      <img
        class="image_7"
        referrerpolicy="no-referrer"
        src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPngce59f9644e820397797e92e07d3796333123469c09e7b719e032226453706ed3"
        @click="ActiveFunes('加')"
      />
    </div>
    <div v-show="active === 2" class="section_3">
      <img
        class="image_6"
        referrerpolicy="no-referrer"
        src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng5b727b1da8591b50a16f335462b668ed3c9d990d7539f3741af9ebde6c96b421"
        @click="ActiveFunes('减')"
      />
      <div class="group_2">
        <img src="@/assets/images/yiliaoxiang.png" alt="" />
      </div>
      <div class="box_31">
        <span class="text_14">智慧随访包</span>
        <div class="section_8s"></div>
        <span class="text_15"
          >支持多种检测设备，包含尿液、血糖、血压、体温、心电图（六导、十二导）、支持中医体质辨识、身份识别、血氧、脉率等检测，手提箱设计，数据实时同步。</span
        >
      </div>
      <img
        class="image_7"
        referrerpolicy="no-referrer"
        src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPngce59f9644e820397797e92e07d3796333123469c09e7b719e032226453706ed3"
        @click="ActiveFunes('加')"
      />
    </div>
    <div v-show="active === 3" class="section_3">
      <img
        class="image_6"
        referrerpolicy="no-referrer"
        src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng5b727b1da8591b50a16f335462b668ed3c9d990d7539f3741af9ebde6c96b421"
        @click="ActiveFunes('减')"
      />
      <div class="group_2">
        <img src="@/assets/images/wifihe.png" alt="" />
      </div>
      <div class="box_31">
        <span class="text_14">边缘计算网关</span>
        <div class="section_8s"></div>
        <span class="text_15"
          >超低功耗边缘计算网关，支持多种经典协议接入，支持有线、无线接入，支持设备定位、开关机监测、距离监测。</span
        >
      </div>
      <img
        class="image_7"
        referrerpolicy="no-referrer"
        src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPngce59f9644e820397797e92e07d3796333123469c09e7b719e032226453706ed3"
        @click="ActiveFunes('加')"
      />
    </div>
    <div v-show="active === 4" class="section_3">
      <img
        class="image_6"
        referrerpolicy="no-referrer"
        src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng5b727b1da8591b50a16f335462b668ed3c9d990d7539f3741af9ebde6c96b421"
        @click="ActiveFunes('减')"
      />
      <div class="group_2">
        <img src="@/assets/images/jiqiqiren.png" alt="" />
      </div>
      <div class="box_31">
        <span class="text_14">AI基层辅助机器人</span>
        <div class="section_8s"></div>
        <span class="text_15"
          >科技元素赋能基层实用场景，机器人支持无人值守模式，支持动线引导、人脸身份识别、疾病问诊、远程诊断等，培养患者习惯，减少人力成本。</span
        >
      </div>
      <img
        class="image_7"
        referrerpolicy="no-referrer"
        src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPngce59f9644e820397797e92e07d3796333123469c09e7b719e032226453706ed3"
        @click="ActiveFunes('加')"
      />
    </div>
    <div v-show="active === 5" class="section_3">
      <img
        class="image_6"
        referrerpolicy="no-referrer"
        src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng5b727b1da8591b50a16f335462b668ed3c9d990d7539f3741af9ebde6c96b421"
        @click="ActiveFunes('减')"
      />
      <div class="group_2">
        <img src="@/assets/images/shoubiao1.png" alt="" />
      </div>
      <div class="box_31">
        <span class="text_14">卫软穿戴设备</span>
        <div class="section_8s"></div>
        <span class="text_15"
          >卫软手表定制，支持单导心电、心率、PPG血压监测、血氧、睡眠、体温、GPS、4G通信模块等；卫软同时支持华为、OPPO等一线厂家品牌开发接入。</span
        >
      </div>
      <img
        class="image_7"
        referrerpolicy="no-referrer"
        src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPngce59f9644e820397797e92e07d3796333123469c09e7b719e032226453706ed3"
        @click="ActiveFunes('加')"
      />
    </div>
    <div class="section_4">
      <div class="section_4_box1">超级性能 超值体验</div>
      <div class="section_4_box2">出色的人机交互，舒适的操作体验，更灵活的信息管理。精准有效的检测结果，立等可取</div>
      <div class="section_4_box3"></div>
      <div class="section_4_box4">
        <div class="box">
          <img src="@/assets/images/laoren.png" alt="" />
          <div class="box_1">一体化设计</div>
          <div class="box_2">安装维护简单，具备移动轮，移动方便</div>
        </div>
        <div class="box2">
          <img src="@/assets/images/shuangping.png" alt="" />
          <div class="box_1">智能双屏</div>
          <div class="box_2">操作+健康宣教双屏设计</div>
        </div>
        <div class="box3">
          <img src="@/assets/images/gaoqing.png" alt="" />
          <div class="box_1">高清摄像头</div>
          <div class="box_2">拍照存档、人脸识别，高清画质</div>
        </div>
      </div>
    </div>
    <div class="section_5">
      <div class="section_5_box1">产品优势</div>
      <div class="section_5_box2"></div>
      <div class="section_5_box3">PRODUCT ADVANTAGES</div>
      <div class="section_5_box4">
        <div class="left">
          <div class="left_1"></div>
          <div class="right_1">
            <div class="top">需求个性化</div>
            <div class="bottom">
              可自选检测项目次序排列；可自选综合版、简易版A4版面健康管理报告单，健康促进建议可编辑。
            </div>
          </div>
        </div>
        <div class="right">
          <div class="left_1"></div>
          <div class="right_1">
            <div class="top">多种身份识别</div>
            <div class="bottom">支持二代身份证（IC卡）、手机、扫码、指纹、人脸等身份识别。</div>
          </div>
        </div>
      </div>
      <div class="section_5_box5">
        <div class="left">
          <div class="left_1"></div>
          <div class="right_1">
            <div class="top">智能交互 轻松增效</div>
            <div class="bottom">电容触控，动画、语音引导，操作简单自助，减轻工作人员的工作量。</div>
          </div>
        </div>
        <div class="right">
          <div class="left_1"></div>
          <div class="right_1">
            <div class="top">第三方对接</div>
            <div class="bottom">支持第三方平台数据对接。</div>
          </div>
        </div>
      </div>
    </div>
    <div class="section_6">
      <div class="div_box">
        <div class="section_6_box1">三端互通 数据同步</div>
        <div class="section_6_box2">一体机、PAD、PC三端互通，智能同步，一键上传</div>
        <div class="section_6_box3">
          PAD端与一体机互通，共同进行测量过程，测量完成后通过PAD端一键上传到PC系统，便于统计及医生后续查看记录。
        </div>
        <div class="section_6_box4"></div>
        <div class="section_6_box5"></div>
        <div class="section_6_box6"></div>
      </div>
    </div>
    <div class="section_7">
      <div class="section_7_box1">适用行业</div>
      <div class="section_7_box2"></div>
      <div class="section_7_box3">PRODUCT ADVANTAGES</div>
      <div class="section_7_box4">
        <div class="box_left">
          <div :class="isTrue === '0' ? 'box1 is' : 'box1'" @click="ClickFun('0')">公卫服务</div>
          <div :class="isTrue === '1' ? 'box2 is' : 'box2'" @click="ClickFun('1')">体检中心</div>
          <div :class="isTrue === '2' ? 'box3 is' : 'box3'" @click="ClickFun('2')">智慧养老</div>
          <div :class="isTrue === '3' ? 'box4 is' : 'box4'" @click="ClickFun('3')">基层卫生医疗机构</div>
        </div>
        <div v-show="isTrue === '0'" class="box_right_1">
          <div class="left"></div>
          <div class="right">
            <div class="right_box1">
              <div class="h"></div>
              <div class="h_1">体检中心</div>
            </div>
            <div class="right_box2">
              <div class="q"></div>
              <div class="q_1">医院通过对设备进行操作从而进行数据测量</div>
            </div>
            <div class="right_box3">
              <div class="w"></div>
              <div class="w_1">设备与平台互通，快速同步测量数据</div>
            </div>
            <div class="right_box4">
              <div class="w"></div>
              <div class="w_1">降低服务运营成本，提升服务效率与服务规模</div>
            </div>
          </div>
        </div>
        <div v-show="isTrue === '1'" class="box_right_2">
          <div class="left"></div>
          <div class="right">
            <div class="right_box1">
              <div class="h"></div>
              <div class="h_1">体检中心</div>
            </div>
            <div class="right_box2">
              <div class="q"></div>
              <div class="q_1">检前预测：检前风险预测，匹配精准体检项目，提升商业转化</div>
            </div>
            <div class="right_box3">
              <div class="w"></div>
              <div class="w_1">检后解读：检后智能解读体检报告，给出疾病风险预测，提升干预服务转化</div>
            </div>
          </div>
        </div>
        <div v-show="isTrue === '2'" class="box_right_3">
          <div class="left"></div>
          <div class="right">
            <div class="right_box1">
              <div class="h"></div>
              <div class="h_1">智慧养老</div>
            </div>
            <div class="right_box2">
              <div class="q"></div>
              <div class="q_1">获取患者画像：帮助获取患者健康数据，针对性提供医疗服务</div>
            </div>
            <div class="right_box3">
              <div class="w"></div>
              <div class="w_1">将患者通过疾病风险预测的方式进行分类管理</div>
            </div>
          </div>
        </div>
        <div v-show="isTrue === '3'" class="box_right_4">
          <div class="left"></div>
          <div class="right">
            <div class="right_box1">
              <div class="h"></div>
              <div class="h_1">基层卫生医疗机构</div>
            </div>
            <div class="right_box2">
              <div class="q"></div>
              <div class="q_1">降本增效：节约人力成本，由机器代替人工</div>
            </div>
            <div class="right_box3">
              <div class="w"></div>
              <div class="w_1">提升体验：赋能产品，增加产品价值，提升用户体验</div>
            </div>
            <div class="right_box4">
              <div class="w"></div>
              <div class="w_1">服务转化：基于患者评测结果，匹配精准服务，提升商业转化</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="section_8">
      <div class="section_7_box1">基本参数</div>
      <div class="section_7_box2"></div>
      <div class="section_7_box3">PRODUCT ADVANTAGES</div>
      <div class="section_7_box4">
        <div class="box_1">外观参数</div>
        <img
          v-if="select"
          @click="slectFun"
          src="@/assets/images/shangla.png"
          alt=""
          style="width: 1.375rem; height: 0.75rem; margin-right: 4.375rem"
        />
        <img
          v-else
          @click="slectFun"
          src="@/assets/images/xiala.png"
          alt=""
          style="width: 1.375rem; height: 0.75rem; margin-right: 4.375rem"
        />
      </div>
      <div v-show="select" class="section_7_box4_2">
        <div class="box_1">
          <span class="text_1"> 尺寸 </span>
          <span class="text_2">1600*600*2400mm</span>
        </div>
        <div class="box_1">
          <span class="text_1"> 智能双屏 </span>
          <span class="text_2">操作+健康宣教</span>
        </div>
        <div class="box_1">
          <span class="text_1"> 一体化设计 </span>
          <span class="text_2">安装简单，部署方便</span>
        </div>
        <div class="box_1">
          <span class="text_1"> 移动轮 </span>
          <span class="text_2">安装移动轮，移动方便</span>
        </div>
      </div>
      <div class="section_7_box4">
        <div class="box_1">功能参数</div>
        <img
          v-if="selects"
          @click="slectFuns"
          src="@/assets/images/shangla.png"
          alt=""
          style="width: 1.375rem; height: 0.75rem; margin-right: 4.375rem"
        />
        <img
          v-else
          @click="slectFuns"
          src="@/assets/images/xiala.png"
          alt=""
          style="width: 1.375rem; height: 0.75rem; margin-right: 4.375rem"
        />
      </div>
      <div v-show="selects" class="section_7_box4_1">
        <div class="section_7_box4_1_0">
          <div class="box_1">
            <span class="text_1"> 通讯网络 </span>
            <span class="text_2">无线wifi和有线网络</span>
          </div>
          <div class="box_1">
            <span class="text_1"> 身份识别 </span>
            <span class="text_2">身份证、指纹、人脸、扫码</span>
          </div>
          <div class="box_1">
            <span class="text_1"> 数据上传 </span>
            <span class="text_2">支持第三方平台对接</span>
          </div>
        </div>
        <div class="box_2">
          <span class="text_1">检测功能：</span>
          <span class="text_2"
            >身高、体重、BMI、体温、人体脂肪成分、血压、脉率、血氧、血流灌注指数、心电（6-12导）、腰围、臀围、腰臀比、血糖、尿酸、胆固醇、血脂四项、中医体质辨识检测</span
          >
        </div>
        <div class="box_2">
          <span class="text_1">可扩展功能：</span>
          <span class="text_2">骨密度、肺功能、心脑血管、尿液分析、血红蛋白、压力评估、视力表、酒精度...</span>
        </div>
      </div>
    </div>
  </div>
</template>
<style scoped lang="scss">
.banner {
  max-width: 160rem;
  width: 100%;
  position: relative;
  margin: 0 auto;
  margin-top: 3.5rem;
  overflow: hidden;
  .section_1 {
    width: 100%;
    height: 37.4612rem;
    background-image: url('@/assets/images/yitijis.png');
    background-size: 100% 100%;
    position: relative;
    .section_1_box {
      width: 43.75rem;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      position: absolute;
      top: 14.4375rem;
      left: 57.5rem;
      .box_1 {
        width: 21.875rem;
        font-size: 2.25rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #222222;
        text-align: center;
      }
      .box_2 {
        width: 43.75rem;
        font-size: 1.25rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #666666;
        margin-top: 1.25rem;
        text-align: center;
      }
    }
  }
  .section_2 {
    background-color: rgba(255, 255, 255, 1);
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    .text_10 {
      width: 30rem;
      overflow-wrap: break-word;
      color: rgba(34, 34, 34, 1);
      font-size: 1.875rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      margin: 4.375rem 0 0 0;
      text-align: center;
    }
    .section_61 {
      background-color: rgba(2, 129, 224, 1);
      width: 3.125rem;
      height: 0.25rem;
      margin: 0.625rem 0 0 0;
    }
    .text_11 {
      width: 15.4375rem;
      color: rgba(102, 102, 102, 1);
      font-size: 1rem;
      font-family: PingFangSC, PingFang SC;
      margin: 0.625rem 0 0 0;
      text-align: center;
    }
    .list_3 {
      cursor: pointer;
      width: 92.5rem;
      height: 7.8125rem;
      display: flex;
      justify-content: space-between;
      margin: 2.5rem 0 1.25rem 0;
    }
    .text-group_1-0 {
      border-radius: 0 0.625rem 0.625rem 0;
      position: relative;
      width: 23.125rem;
      height: 7.8125rem;
      display: flex;
      flex-direction: column;
      justify-content: flex-center;
      background: rgba(72, 101, 255, 1);
    }
    .text_12-0 {
      width: 16.875rem;
      font-size: 1.5rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      text-align: left;
      margin: 1.25rem 0 0 2.5rem;
      color: rgba(255, 255, 255, 1);
    }
    .text_13-0 {
      font-size: 1rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      text-align: left;
      margin: 0.625rem 0 1.25rem 2.5rem;
      color: rgba(255, 255, 255, 1);
    }
    .image_4-0 {
      position: absolute;
      left: 0;
      top: -2.5625rem;
      width: 0.125rem;
      height: 10.3125rem;
    }
    .text-group_1-1 {
      border-radius: 0 0.625rem 0.625rem 0;
      position: relative;
      width: 23.125rem;
      height: 7.8125rem;
      display: flex;
      flex-direction: column;
      justify-content: flex-center;
      background: rgba(216, 216, 216, 0);
    }
    .text_12-1 {
      width: 16.875rem;
      font-size: 1.5rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      text-align: left;
      margin: 1.25rem 0 0 2.5rem;
      color: rgba(34, 34, 34, 1);
    }
    .text_13-1 {
      width: 12.5rem;
      font-size: 1rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      text-align: left;
      margin: 0.625rem 0 1.25rem 2.5rem;
      color: rgba(102, 102, 102, 1);
    }
    .image_2-1 {
      position: absolute;
      left: 0;
      top: 1.25rem;
      width: 0.0625rem;
      height: 7.8125rem;
    }
    .text-group_1-2 {
      border-radius: 0 0.625rem 0.625rem 0;
      position: relative;
      width: 23.125rem;
      height: 7.8125rem;
      display: flex;
      flex-direction: column;
      justify-content: flex-center;
      background: rgba(216, 216, 216, 0);
    }
    .text_12-2 {
      width: 16.875rem;
      font-size: 1.5rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      margin: 1.25rem 0 0 2.5rem;
      color: rgba(34, 34, 34, 1);
    }
    .text_13-2 {
      width: 12.5rem;
      font-size: 1rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      margin: 0.625rem 0 1.25rem 2.5rem;
      color: rgba(102, 102, 102, 1);
    }
    .image_2-2 {
      position: absolute;
      left: 0;
      top: 1.25rem;
      width: 0.0625rem;
      height: 7.8125rem;
    }
    .image_3-2 {
      position: absolute;
      left: 23.0625rem;
      top: 1.25rem;
      width: 0.0625rem;
      height: 7.8125rem;
    }
    .text-group_1-3 {
      border-radius: 0 0.625rem 0.625rem 0;
      position: relative;
      width: 23.125rem;
      height: 7.8125rem;
      display: flex;
      flex-direction: column;
      justify-content: flex-center;
      background: rgba(216, 216, 216, 0);
    }
    .text_12-3 {
      width: 16.875rem;
      font-size: 1.5rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      margin: 1.25rem 0 0 2.5rem;
      color: rgba(34, 34, 34, 1);
    }
    .text_13-3 {
      font-size: 1rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      margin: 0.625rem 0 1.25rem 2.5rem;
      color: rgba(102, 102, 102, 1);
    }
    .image_3-3 {
      position: absolute;
      left: 23.0625rem;
      top: 1.25rem;
      width: 0.0625rem;
      height: 7.8125rem;
    }
  }
  .section_3 {
    background-color: rgba(34, 34, 34, 1);
    width: 100%;
    display: flex;
    height: 12.5rem;
    justify-content: center;
    align-items: center;
    .image_7 {
      width: 1.25rem;
      height: 2.1875rem;
      margin: 0 10.0625rem 0 2.4375rem;
    }
    .group_2 {
      width: 23.125rem;
      height: 12.5rem;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .image_5 {
      width: 1.25rem;
      height: 2.1875rem;
      margin: 6.75rem 0 0 10.0625rem;
    }
    .box_31 {
      width: 65rem;
      height: 9.6875rem;
      margin: 2.5rem 0 0 4.375rem;
    }
    .text_14 {
      width: 15rem;
      height: 2.625rem;
      color: rgba(255, 255, 255, 1);
      font-size: 1.5rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      line-height: 2.625rem;
    }
    .section_8s {
      background-color: rgba(255, 255, 255, 1);
      width: 5rem;
      height: 0.25rem;
      margin-top: 0.9375rem;
    }
    .text_15 {
      width: 65rem;
      color: rgba(255, 255, 255, 1);
      font-size: 1rem;
      font-family: PingFangSC, PingFang SC;
      display: block;
      margin-top: 1.25rem;
    }
    .image_6 {
      width: 1.25rem;
      height: 2.5rem;
      margin-right: 2.5rem;
      margin-left: 10rem;
    }
  }
  .section_4 {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    background: linear-gradient(180deg, #dfe9ff 0%, #f6f9ff 100%);
    padding-bottom: 6.25rem;
    .section_4_box1 {
      font-size: 1.875rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      color: #222222;
      margin-top: 4.375rem;
    }
    .section_4_box2 {
      font-size: 1rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #666666;
      margin-bottom: 2.5rem;
      display: flex;
      justify-content: center;
      margin-top: 0.9375rem;
    }
    .section_4_box3 {
      width: 92.5rem;
      height: 46.875rem;
      background-image: url('@/assets/images/dajiqi.png');
      background-size: 100% 100%;
      margin-bottom: 2.5rem;
    }
    .section_4_box4 {
      width: 92.5rem;
      display: flex;
      img {
        border-top-left-radius: 0.9375rem;
        border-top-right-radius: 0.9375rem;
      }
      .box {
        width: 29.375rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        background-color: #ffffff;
        padding-bottom: 1.5625rem;
        border-radius: 0.9375rem;
        .box_1 {
          font-size: 1.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #000000;
          margin-top: 1.875rem;
          margin-bottom: 0.9375rem;
        }
        .box_2 {
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #666666;
        }
      }
      .box2 {
        width: 29.375rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        background-color: #ffffff;
        margin: 0 2.1875rem;
        padding-bottom: 1.5625rem;
        border-radius: 0.9375rem;
        .box_1 {
          font-size: 1.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #000000;
          margin-top: 1.875rem;
          margin-bottom: 0.9375rem;
        }
        .box_2 {
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #666666;
        }
      }
      .box3 {
        width: 29.375rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        background-color: #ffffff;
        padding-bottom: 1.5625rem;
        border-radius: 0.9375rem;
        .box_1 {
          font-size: 1.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #000000;
          margin-top: 1.875rem;
          margin-bottom: 0.9375rem;
        }
        .box_2 {
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #666666;
        }
      }
    }
  }
  .section_5 {
    width: 100%;
    height: 39.25rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    background-image: url('@/assets/images/lanbgr.png');
    background-size: 100% 100%;
    .section_5_box1 {
      font-size: 1.875rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      color: #ffffff;
      margin-top: 4.375rem;
    }
    .section_5_box2 {
      width: 3.125rem;
      height: 0.25rem;
      background: #ffffff;
      margin: 0.625rem 0;
    }
    .section_5_box3 {
      font-size: 1rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #ffffff;
      margin-bottom: 2.5rem;
    }
    .section_5_box4 {
      width: 92.5rem;
      display: flex;
      .left {
        width: 45rem;
        margin-right: 2.5rem;
        display: flex;
        align-items: center;
        background: #ffffff;
        border-radius: 0.9375rem;
        padding: 1.25rem 0;
        .left_1 {
          width: 3.75rem;
          height: 3.75rem;
          background-image: url('@/assets/images/lanyifu.png');
          background-size: 100% 100%;
          margin-left: 2.5rem;
        }
        .right_1 {
          display: flex;
          flex-direction: column;
          margin-left: 1.875rem;
          .top {
            font-size: 1.5rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #222222;
          }
          .bottom {
            width: 33.75rem;
            font-size: 1rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #666666;
            margin-top: 0.625rem;
          }
        }
      }
      .right {
        width: 45rem;
        margin-right: 2.5rem;
        display: flex;
        align-items: center;
        background: #ffffff;
        border-radius: 0.9375rem;
        padding: 1.25rem 0;
        .left_1 {
          width: 3.75rem;
          height: 3.75rem;
          background-image: url('@/assets/images/zipao.png');
          background-size: 100% 100%;
          margin-left: 2.5rem;
        }
        .right_1 {
          display: flex;
          flex-direction: column;
          margin-left: 1.875rem;
          .top {
            font-size: 1.5rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #222222;
          }
          .bottom {
            width: 33.75rem;
            font-size: 1rem;
            height: 2.75rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #666666;
            margin-top: 0.625rem;
          }
        }
      }
    }
    .section_5_box5 {
      width: 92.5rem;
      display: flex;
      margin-top: 2.5rem;
      .left {
        width: 45rem;
        margin-right: 2.5rem;
        display: flex;
        align-items: center;
        background: #ffffff;
        border-radius: 0.9375rem;
        padding: 1.25rem 0;
        .left_1 {
          width: 3.75rem;
          height: 3.75rem;
          background-image: url('@/assets/images/shangbiao.png');
          background-size: 100% 100%;
          margin-left: 2.5rem;
        }
        .right_1 {
          display: flex;
          flex-direction: column;
          margin-left: 1.875rem;
          .top {
            font-size: 1.5rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #222222;
          }
          .bottom {
            width: 33.75rem;
            font-size: 1rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #666666;
            margin-top: 0.625rem;
          }
        }
      }
      .right {
        width: 45rem;
        margin-right: 2.5rem;
        display: flex;
        align-items: center;
        background: #ffffff;
        border-radius: 0.9375rem;
        padding: 1.25rem 0;
        .left_1 {
          width: 3.75rem;
          height: 3.75rem;
          background-image: url('@/assets/images/niaodun.png');
          background-size: 100% 100%;
          margin-left: 2.5rem;
        }
        .right_1 {
          display: flex;
          flex-direction: column;
          margin-left: 1.875rem;
          .top {
            font-size: 1.5rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #222222;
          }
          .bottom {
            width: 33.75rem;
            height: 2.75rem;
            font-size: 1rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #666666;
            margin-top: 0.625rem;
          }
        }
      }
    }
  }
  .section_6 {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-bottom: 6.25rem;
    .div_box {
      position: relative;
      width: 92.5rem;
      position: relative;
    }
    .section_6_box1 {
      width: 20.875rem;
      height: 3.5rem;
      font-size: 1.875rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      color: #222222;
      line-height: 3.5rem;
      margin-top: 6.25rem;
    }
    .section_6_box2 {
      width: 33.75rem;
      height: 2.3125rem;
      font-size: 1rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #666666;
      line-height: 2.3125rem;
      margin-top: 0.625rem;
    }
    .section_6_box3 {
      width: 40rem;
      height: 8.8125rem;
      font-size: 1.5rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #222222;
      line-height: 2.9375rem;
      margin-top: 500px;
    }
    .section_6_box4 {
      width: 46.875rem;
      height: 25rem;
      background-image: url('@/assets/images/lvdui.png');
      background-size: 100% 100%;
      position: absolute;
      top: 15.9375rem;
      left: 0;
      z-index: 2;
    }
    .section_6_box5 {
      width: 46.875rem;
      height: 25rem;
      background-image: url('@/assets/images/biaoge.png');
      background-size: 100% 100%;
      z-index: 1;
      position: absolute;
      top: 3.125rem;
      left: 43rem;
    }
    .section_6_box6 {
      width: 46.875rem;
      height: 25rem;
      background-image: url('@/assets/images/jiancha.png');
      background-size: 100% 100%;
      position: absolute;
      bottom: -3.125rem;
      left: 43rem;
    }
  }
  .section_7 {
    width: 100%;
    height: 46.125rem;
    background-image: url('@/assets/images/lanse.png');
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    .section_7_box1 {
      font-size: 1.875rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      color: #222222;
      margin-top: 4.375rem;
    }
    .section_7_box2 {
      width: 3.125rem;
      height: 0.25rem;
      background: #0281e0;
      margin: 0.625rem 0;
    }
    .section_7_box3 {
      height: 1.75rem;
      font-size: 1rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #666666;
      line-height: 1.75rem;
      margin-bottom: 2.5rem;
      text-align: center;
    }
    .section_7_box4 {
      display: flex;
      width: 88.75rem;
      height: 28.125rem;
      .box_left {
        width: 13.75rem;
        display: flex;
        flex-direction: column;
        .is {
          background: #4865ff !important;
          box-shadow: 0 0.125rem 0.8125rem 0 rgba(72, 101, 255, 0.5) !important;
          color: #ffffff !important;
          font-weight: 400 !important;
        }
        .box1 {
          cursor: pointer;
          width: 12.5rem;
          height: 3.125rem;
          border-radius: 0.5rem;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 2.5rem;
          background: #ffffff;
          margin-top: 2.1875rem;
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #666666;
        }
        .box2 {
          cursor: pointer;
          width: 12.5rem;
          height: 3.125rem;
          background: #ffffff;
          border-radius: 0.5rem;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 2.5rem;
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #666666;
        }
        .box3 {
          cursor: pointer;
          width: 12.5rem;
          height: 3.125rem;
          background: #ffffff;
          border-radius: 0.5rem;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 2.5rem;
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #666666;
        }
        .box4 {
          cursor: pointer;
          width: 12.5rem;
          height: 3.125rem;
          background: #ffffff;
          border-radius: 0.5rem;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 2.5rem;
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #666666;
        }
      }
      .box_right_1 {
        width: 71.875rem;
        height: 25rem;
        background: #ffffff;
        box-shadow: 0 0.125rem 0.8125rem 0 rgba(72, 101, 255, 0.15);
        border-radius: 0.9375rem;
        margin-left: 2.5rem;
        display: flex;
        .left {
          width: 34.3944rem;
          height: 25rem;
          background-image: url('@/assets/images/guzhang.png');
          background-size: 100% 100%;
        }
        .right {
          flex: 1;
          display: flex;
          flex-direction: column;
          .right_box1 {
            display: flex;
            align-items: center;
            margin-top: 2.5rem;
            margin-left: 2.5rem;
            .h {
              width: 0.25rem;
              height: 1.25rem;
              background: #0281e0;
              border-radius: 0.125rem;
              margin-right: 0.9375rem;
            }
            .h_1 {
              width: 7.5rem;
              height: 2.625rem;
              font-size: 1.5rem;
              font-family: PingFangSC, PingFang SC;
              font-weight: 500;
              color: #222222;
              line-height: 2.625rem;
            }
          }
          .right_box2 {
            display: flex;
            align-items: center;
            margin-left: 2.5rem;
            margin-top: 1.25rem;
            .q {
              width: 0.375rem;
              height: 0.375rem;
              background: #0281e0;
              border-radius: 0.1875rem;
              margin-right: 0.8125rem;
            }
            .q_1 {
              width: 31.3125rem;
              font-size: 1rem;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #222222;
            }
          }
          .right_box3 {
            display: flex;
            align-items: center;
            margin-top: 0.625rem;
            width: 32.5rem;
            margin-left: 2.5rem;
            .w {
              width: 0.375rem;
              height: 0.375rem;
              background: #0281e0;
              border-radius: 0.1875rem;
              margin-right: 0.8125rem;
            }
            .w_1 {
              width: 31.3125rem;
              height: 2.0625rem;
              font-size: 1rem;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #222222;
              line-height: 2.0625rem;
            }
          }
          .right_box4 {
            display: flex;
            align-items: center;
            margin-top: 0.625rem;
            width: 32.5rem;
            margin-left: 2.5rem;
            .w {
              width: 0.375rem;
              height: 0.375rem;
              background: #0281e0;
              border-radius: 0.1875rem;
              margin-right: 0.8125rem;
            }
            .w_1 {
              width: 31.3125rem;
              height: 2.0625rem;
              font-size: 1rem;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #222222;
              line-height: 2.0625rem;
            }
          }
        }
      }
      .box_right_2 {
        width: 71.875rem;
        height: 25rem;
        background: #ffffff;
        box-shadow: 0 0.125rem 0.8125rem 0 rgba(72, 101, 255, 0.15);
        border-radius: 0.9375rem;
        margin-left: 2.5rem;
        display: flex;
        .left {
          width: 34.3944rem;
          height: 25rem;
          background-image: url('@/assets/images/ting.png');
          background-size: 100% 100%;
        }
        .right {
          flex: 1;
          display: flex;
          flex-direction: column;
          .right_box1 {
            display: flex;
            align-items: center;
            margin-top: 2.5rem;
            margin-left: 2.5rem;
            .h {
              width: 0.25rem;
              height: 1.25rem;
              background: #0281e0;
              border-radius: 0.125rem;
              margin-right: 0.9375rem;
            }
            .h_1 {
              width: 7.5rem;
              height: 2.625rem;
              font-size: 1.5rem;
              font-family: PingFangSC, PingFang SC;
              font-weight: 500;
              color: #222222;
              line-height: 2.625rem;
            }
          }
          .right_box2 {
            display: flex;
            margin-left: 2.5rem;
            margin-top: 0.625rem;
            .q {
              width: 0.375rem;
              height: 0.375rem;
              background: #0281e0;
              border-radius: 0.1875rem;
              margin-right: 0.8125rem;
              margin-top: 0.8125rem;
            }
            .q_1 {
              width: 31.3125rem;
              font-size: 1rem;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #222222;
              line-height: 2.0625rem;
            }
          }
          .right_box3 {
            display: flex;
            margin-top: 0.625rem;
            width: 32.5rem;
            margin-left: 2.5rem;
            .w {
              width: 0.375rem;
              height: 0.375rem;
              background: #0281e0;
              border-radius: 0.1875rem;
              margin-right: 0.8125rem;
              margin-top: 0.8125rem;
            }
            .w_1 {
              width: 31.3125rem;
              height: 4.125rem;
              font-size: 1rem;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #222222;
              line-height: 2.0625rem;
            }
          }
        }
      }
      .box_right_3 {
        width: 71.875rem;
        height: 25rem;
        background: #ffffff;
        box-shadow: 0 0.125rem 0.8125rem 0 rgba(72, 101, 255, 0.15);
        border-radius: 0.9375rem;
        margin-left: 2.5rem;
        display: flex;
        .left {
          width: 34.3944rem;
          height: 25rem;
          background-image: url('@/assets/images/paipian.png');
          background-size: 100% 100%;
        }
        .right {
          flex: 1;
          display: flex;
          flex-direction: column;
          .right_box1 {
            display: flex;
            align-items: center;
            margin-top: 2.5rem;
            margin-left: 2.5rem;
            .h {
              width: 0.25rem;
              height: 1.25rem;
              background: #0281e0;
              border-radius: 0.125rem;
              margin-right: 0.9375rem;
            }
            .h_1 {
              width: 7.5rem;
              height: 2.625rem;
              font-size: 1.5rem;
              font-family: PingFangSC, PingFang SC;
              font-weight: 500;
              color: #222222;
              line-height: 2.625rem;
            }
          }
          .right_box2 {
            display: flex;
            margin-left: 2.5rem;
            margin-top: 1.25rem;
            .q {
              width: 0.375rem;
              height: 0.375rem;
              background: #0281e0;
              border-radius: 0.1875rem;
              margin-right: 0.8125rem;
              margin-top: 0.8125rem;
            }
            .q_1 {
              width: 31.3125rem;
              font-size: 1rem;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #222222;
              line-height: 2.0625rem;
            }
          }
          .right_box3 {
            display: flex;
            align-items: center;
            margin-top: 0.625rem;
            width: 32.5rem;
            margin-left: 2.5rem;
            .w {
              width: 0.375rem;
              height: 0.375rem;
              background: #0281e0;
              border-radius: 0.1875rem;
              margin-right: 0.8125rem;
            }
            .w_1 {
              width: 31.3125rem;
              height: 2.0625rem;
              font-size: 1rem;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #222222;
              line-height: 2.0625rem;
            }
          }
        }
      }
      .box_right_4 {
        width: 71.875rem;
        height: 25rem;
        background: #ffffff;
        box-shadow: 0 0.125rem 0.8125rem 0 rgba(72, 101, 255, 0.15);
        border-radius: 0.9375rem;
        margin-left: 2.5rem;
        display: flex;
        .left {
          width: 34.3944rem;
          height: 25rem;
          background-image: url('@/assets/images/kanbingli.png');
          background-size: 100% 100%;
        }
        .right {
          flex: 1;
          display: flex;
          flex-direction: column;
          .right_box1 {
            display: flex;
            align-items: center;
            margin-top: 2.5rem;
            margin-left: 2.5rem;
            .h {
              width: 0.25rem;
              height: 1.25rem;
              background: #0281e0;
              border-radius: 0.125rem;
              margin-right: 0.9375rem;
            }
            .h_1 {
              width: 15rem;
              height: 2.625rem;
              font-size: 1.5rem;
              font-family: PingFangSC, PingFang SC;
              font-weight: 500;
              color: #222222;
              line-height: 2.625rem;
            }
          }
          .right_box2 {
            display: flex;
            align-items: center;
            margin-left: 2.5rem;
            margin-top: 1.25rem;
            .q {
              width: 0.375rem;
              height: 0.375rem;
              background: #0281e0;
              border-radius: 0.1875rem;
              margin-right: 0.8125rem;
            }
            .q_1 {
              width: 31.3125rem;
              height: 2.0625rem;
              font-size: 1rem;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #222222;
              line-height: 2.0625rem;
            }
          }
          .right_box3 {
            display: flex;
            margin-top: 0.9375rem;
            width: 32.5rem;
            margin-left: 2.5rem;
            .w {
              width: 0.375rem;
              height: 0.375rem;
              background: #0281e0;
              border-radius: 0.1875rem;
              margin-right: 0.8125rem;
              margin-top: 0.8125rem;
            }
            .w_1 {
              width: 31.3125rem;
              font-size: 1rem;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #222222;
              line-height: 2.0625rem;
            }
          }
          .right_box4 {
            display: flex;
            margin-top: 0.9375rem;
            width: 32.5rem;
            margin-left: 2.5rem;
            .w {
              width: 0.375rem;
              height: 0.375rem;
              background: #0281e0;
              border-radius: 0.1875rem;
              margin-right: 0.8125rem;
              margin-top: 0.8125rem;
            }
            .w_1 {
              width: 31.3125rem;
              height: 4.125rem;
              font-size: 1rem;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #222222;
              line-height: 2.0625rem;
            }
          }
        }
      }
    }
  }
  .section_8 {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    padding-bottom: 6.25rem;
    background-color: #f8f8f8;
    .section_7_box1 {
      width: 10rem;
      height: 3.5rem;
      font-size: 2.125rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      color: #222222;
      line-height: 3.5rem;
      margin-top: 4.375rem;
      text-align: center;
    }
    .section_7_box2 {
      width: 3.125rem;
      height: 0.25rem;
      background: #0281e0;
      margin: 0.625rem 0;
    }
    .section_7_box3 {
      height: 1.75rem;
      font-size: 1rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #666666;
      line-height: 1.75rem;
      text-align: center;
    }
    .section_7_box4 {
      display: flex;
      width: 92.5rem;
      height: 6.25rem;
      align-items: center;
      justify-content: space-between;
      background-color: #ffffff;
      margin-top: 2.5rem;
      border-top-left-radius: 0.9375rem;
      border-top-right-radius: 0.9375rem;
      .box_1 {
        width: 7.5rem;
        margin-left: 4.375rem;
        height: 2.625rem;
        font-size: 1.5rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #222222;
        line-height: 2.625rem;
      }
    }
    .section_7_box4_2 {
      border-top: 0.0625rem solid #c7c7c7;
      height: 7.5rem;
      width: 92.5rem;
      display: flex;
      flex-wrap: wrap;
      background: #ffffff;
      align-items: center;
      border-bottom-left-radius: 0.9375rem;
      border-bottom-right-radius: 0.9375rem;
      .box_1 {
        margin-top: 0.625rem;
        width: 23.125rem;
        height: 3.125rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        .text_1 {
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 1rem;
          color: #666666;
        }
        .text_2 {
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #222222;
          line-height: 1.375rem;
        }
      }
    }
    .section_7_box4_1 {
      border-top: 0.0625rem solid #c7c7c7;
      height: 14.375rem;
      width: 92.5rem;
      display: flex;
      flex-direction: column;
      background: #ffffff;
      border-bottom-left-radius: 0.9375rem;
      border-bottom-right-radius: 0.9375rem;
      .section_7_box4_1_0 {
        display: flex;
        flex-wrap: wrap;
      }
      .box_1 {
        margin-top: 0.625rem;
        width: 23.125rem;
        height: 3.125rem;
        padding: 2.5rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        .text_1 {
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 1rem;
          color: #666666;
        }
        .text_2 {
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #222222;
          line-height: 1.375rem;
        }
      }
      .box_2 {
        display: flex;
        align-items: center;
        margin-left: 4.375rem;
        margin-top: 1.875rem;
        .text_1 {
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #666666;
        }
        .text_2 {
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #222222;
        }
      }
    }
  }
}
</style>
