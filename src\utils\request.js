import axios from 'axios'

// create an axios instance
// const BaseURL = "http://jsremote.synology.me:8600"
const service = axios.create({
  // baseURL: '', // url = base url + request url
  baseURL: '/api',
  // withCredentials: true,
  // withCredentials: true, // send cookies when cross-domain requests
  timeout: 10 * 1000 // request timeout
})

// request interceptor
service.interceptors.request.use(
  config => {
    // do something before request is sent
    // console.log('config',config);
    // if (config.url == '/api/backend/cos/uploadFile/private') {
    //   config.headers['Content-Type'] = 'multipart/form-data; boundary=----WebKitFormBoundaryy4cAhXv254gc7E9B'
    //   config.headers['Accept'] = '*/*'
    // }
    // if (store.getters.token) {
    //   config.headers.Authorization = getToken();
    // }
    return config
  },
  error => {
    // do something with request error
    // console.log("error", error) // for debug
    return Promise.reject(error)
  }
)

// response interceptor
service.interceptors.response.use(
  /**
   * If you want to get http information such as headers or status
   * Please return  response => response
   */

  /**
   * Determine the request status by custom code
   * Here is just an example
   * You can also judge the status by HTTP Status Code     //application/json
   */
  response => {
    const res = response.data
    // /api/gateway/loginByPhone
    // console.log('response', response);
    // if (response.config.url == '/api/gateway/loginByPhone') {
    //   if (res.code != 200) {
    //     return Promise.reject(new Error(res.msg || '服务器异常！'))
    //   } else {
    //     return res
    //   }
    // } else {
    //   // console.log('res.msg',res.msg);
    //   if (res.code == '401') {
    //     // token失效  跳转登录页
    //     window.location.href = '/login'
    //     Message.error('登录过期，请重新登录！');
    //     return Promise.reject(new Error('登录过期，请重新登录！'))
    //   }
    //   if (res.code !== 200) {
    //     Message({
    //       message: res.msg || '服务器异常！',
    //       type: 'error',
    //       duration: 5 * 1000
    //     })
    //     return Promise.reject(new Error(res.msg || '服务器异常！'))
    //   } else {

    //     return res
    //   }
    // }
    // if (res.code > 500) {
    //   return Promise.reject(new Error(res.msg))
    // }
    // // if the custom code is not 20000, it is judged as an error.

    // if (res.code !== 200) {
    //   Message({
    //     message: res.msg || 'Error',
    //     type: 'error',
    //     duration: 5 * 1000
    //   })

    //   // 50008: Illegal token; 50012: Other clients logged in; 50014: Token expired;
    //   if (res.code === 50008 || res.code === 50012 || res.code === 50014) {
    //     // to re-login
    //     MessageBox.confirm('You have been logged out, you can cancel to stay on this page, or log in again', 'Confirm logout', {
    //       confirmButtonText: 'Re-Login',
    //       cancelButtonText: 'Cancel',
    //       type: 'warning'
    //     }).then(() => {
    //       store.dispatch('user/resetToken').then(() => {
    //         location.reload()
    //       })
    //     })
    //   }
    //   return Promise.reject(new Error(res.msg || '服务器异常！'))
    // } else {

    return res
    // }
  },
  error => {
    // console.log('errorerrorerrorerror',error);
    // Message({
    //   // message: error.message,
    //   type: 'error',
    //   duration: 5 * 1000
    // })
    return Promise.reject(error)
  }
)

export default service
