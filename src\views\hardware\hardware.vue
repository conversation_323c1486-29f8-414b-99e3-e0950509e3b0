<script setup>
import { ref, reactive } from 'vue'
const isTrue = ref(true)
const isTrues = ref(true)
const active = ref(1)
const clickFun = () => {
  isTrue.value = !isTrue.value
}
const clickFuns = () => {
  isTrues.value = !isTrues.value
}
const ActiveFuns = val => {
  active.value = val
}
const ActiveFunes = index => {
  if (index === '加') {
    if (active.value === 5) {
      active.value = 1
    } else {
      active.value += 1
    }
  } else {
    if (active.value === 1) {
      active.value = 5
    } else {
      active.value -= 1
    }
  }
}
</script>

<template>
  <div class="image-main">
    <!-- <img src="@/assets/main_images/Hardware-main1.jpg" alt="" />
    <img src="@/assets/main_images/Hardware-main2.jpg" alt="" /> -->
    <div class="banner">
      <img src="@/assets/images/wearabledevice-banner.png" alt="" />
      <div class="info">
        <div class="label">卫软穿戴 安心放心</div>
        <div class="text">将健康戴在手上，您的每一次心跳都被用心守护，<br />关爱自己，守护家人。</div>
      </div>
    </div>
    <div class="section_1">
      <span class="text_10">智能硬件产品助您超越所能</span>
      <div class="section_6"></div>
      <span class="text_11">INTELLIGENT&nbsp;HARDWARE</span>
      <div v-show="active === 1" class="list_3">
        <div class="text-group_1-0" @click="ActiveFuns(1)">
          <span class="text_12-0">卫软穿戴设备</span>
          <span class="text_13-0">实时监测&nbsp;安心保障</span>
          <img
            class="image_4-0"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng3b9a8121db0a1bcc7c1a65f5a79361a0bbbf877f3a659cc948ad087a9b9f03cb"
          />
        </div>
        <div class="text-group_1-1" @click="ActiveFuns(2)">
          <span class="text_12-1">健康检测一体机</span>
          <span class="text_13-1">智慧体检&nbsp;一机搞定</span>
          <img
            class="image_2-1"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
        <div class="text-group_1-2" @click="ActiveFuns(3)">
          <span class="text_12-2">智彗随访包</span>
          <span class="text_13-2">一机多用&nbsp;实时上传</span>
          <img
            class="image_2-2"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
          <img
            class="image_3-2"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
        <div class="text-group_1-3" @click="ActiveFuns(4)">
          <span class="text_12-3">边缘计算网关</span>
          <span class="text_13-3">万能互联&nbsp;实时监控</span>
          <img
            class="image_3-3"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
      </div>
      <div v-show="active === 2" class="list_3">
        <div class="text-group_1-0" @click="ActiveFuns(2)">
          <span class="text_12-0">健康检测一体机</span>
          <span class="text_13-0">智慧体检 一机搞定</span>
          <img
            class="image_4-0"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng3b9a8121db0a1bcc7c1a65f5a79361a0bbbf877f3a659cc948ad087a9b9f03cb"
          />
        </div>
        <div class="text-group_1-1" @click="ActiveFuns(3)">
          <span class="text_12-1">智慧随访包</span>
          <span class="text_13-1">一机多用 实时上传</span>
          <img
            class="image_2-1"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
        <div class="text-group_1-2" @click="ActiveFuns(4)">
          <span class="text_12-2">边缘计算网关</span>
          <span class="text_13-2">万物互联 实时监控</span>
          <img
            class="image_2-2"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
          <img
            class="image_3-2"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
        <div class="text-group_1-3" @click="ActiveFuns(5)">
          <span class="text_12-3">AI基层辅助机器人</span>
          <span class="text_13-3">人脸识别 自助问诊 远程诊断</span>
          <img
            class="image_3-3"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
      </div>
      <div v-show="active === 3" class="list_3">
        <div class="text-group_1-0" @click="ActiveFuns(3)">
          <span class="text_12-0">智慧随访包</span>
          <span class="text_13-0">一机多用 实时上传</span>
          <img
            class="image_4-0"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng3b9a8121db0a1bcc7c1a65f5a79361a0bbbf877f3a659cc948ad087a9b9f03cb"
          />
        </div>
        <div class="text-group_1-1" @click="ActiveFuns(4)">
          <span class="text_12-1">边缘计算网关</span>
          <span class="text_13-1">万物互联 实时监控</span>
          <img
            class="image_2-1"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
        <div class="text-group_1-2" @click="ActiveFuns(5)">
          <span class="text_12-2">AI基层辅助机器人</span>
          <span class="text_13-2">人脸识别 自助问诊 远程诊断</span>
          <img
            class="image_2-2"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
          <img
            class="image_3-2"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
        <div class="text-group_1-3" @click="ActiveFuns(1)">
          <span class="text_12-3">卫软穿戴设备</span>
          <span class="text_13-3">实时监测 安心保障</span>
          <img
            class="image_3-3"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
      </div>
      <div v-show="active === 4" class="list_3">
        <div class="text-group_1-0" @click="ActiveFuns(4)">
          <span class="text_12-0">边缘计算网关</span>
          <span class="text_13-0">万物互联 实时监控</span>
          <img
            class="image_4-0"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng3b9a8121db0a1bcc7c1a65f5a79361a0bbbf877f3a659cc948ad087a9b9f03cb"
          />
        </div>
        <div class="text-group_1-1" @click="ActiveFuns(5)">
          <span class="text_12-1">AI基层辅助机器人</span>
          <span class="text_13-1">人脸识别 自助问诊 远程诊断</span>
          <img
            class="image_2-1"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
        <div class="text-group_1-2" @click="ActiveFuns(1)">
          <span class="text_12-2">卫软穿戴设备</span>
          <span class="text_13-2">实时监测 安心保障</span>
          <img
            class="image_2-2"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
          <img
            class="image_3-2"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
        <div class="text-group_1-3" @click="ActiveFuns(2)">
          <span class="text_12-3">健康检测一体机</span>
          <span class="text_13-3">智慧体检 一机搞定</span>
          <img
            class="image_3-3"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
      </div>
      <div v-show="active === 5" class="list_3">
        <div class="text-group_1-0" @click="ActiveFuns(5)">
          <span class="text_12-0">AI基层辅助机器人</span>
          <span class="text_13-0">人脸识别 自助问诊 远程诊断</span>
          <img
            class="image_4-0"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng3b9a8121db0a1bcc7c1a65f5a79361a0bbbf877f3a659cc948ad087a9b9f03cb"
          />
        </div>
        <div class="text-group_1-1" @click="ActiveFuns(1)">
          <span class="text_12-1">卫软穿戴设备</span>
          <span class="text_13-1">实时监测 安心保障</span>
          <img
            class="image_2-1"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
        <div class="text-group_1-2" @click="ActiveFuns(2)">
          <span class="text_12-2">健康检测一体机</span>
          <span class="text_13-2">智慧体检 一机搞定</span>
          <img
            class="image_2-2"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
          <img
            class="image_3-2"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
        <div class="text-group_1-3" @click="ActiveFuns(3)">
          <span class="text_12-3">智慧随访包</span>
          <span class="text_13-3">一机多用 实时上传</span>
          <img
            class="image_3-3"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
      </div>
    </div>
    <div v-show="active === 1" class="section_2">
      <img
        class="image_6"
        referrerpolicy="no-referrer"
        src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng5b727b1da8591b50a16f335462b668ed3c9d990d7539f3741af9ebde6c96b421"
        @click="ActiveFunes('减')"
      />
      <div class="group_2">
        <img src="@/assets/images/shoubiao1.png" alt="" />
      </div>
      <div class="box_31">
        <span class="text_14">卫软穿戴设备</span>
        <div class="section_8"></div>
        <span class="text_15"
          >卫软手表定制，支持单导心电、心率、PPG血压监测、血氧、睡眠、体温、GPS、4G通信模块等；卫软同时支持华为、OPPO等一线厂家品牌开发接入。</span
        >
      </div>
      <img
        class="image_7"
        referrerpolicy="no-referrer"
        src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPngce59f9644e820397797e92e07d3796333123469c09e7b719e032226453706ed3"
        @click="ActiveFunes('加')"
      />
    </div>
    <div v-show="active === 2" class="section_2">
      <img
        class="image_6"
        referrerpolicy="no-referrer"
        src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng5b727b1da8591b50a16f335462b668ed3c9d990d7539f3741af9ebde6c96b421"
        @click="ActiveFunes('减')"
      />
      <div class="group_2">
        <img src="@/assets/images/jixiebi.png" alt="" />
      </div>
      <div class="box_31">
        <span class="text_14">健康信息工作站</span>
        <div class="section_8"></div>
        <span class="text_15"
          >精准之道，内外兼修。高端配置高度集成一体化设计，健康宣教和自助体检结合，30多项健康检测指标，集秒级快检、健康指导、风险评估为一体，助力就诊、体检等多个场景，数据实时上传与统计。</span
        >
      </div>
      <img
        class="image_7"
        referrerpolicy="no-referrer"
        src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPngce59f9644e820397797e92e07d3796333123469c09e7b719e032226453706ed3"
        @click="ActiveFunes('加')"
      />
    </div>
    <div v-show="active === 3" class="section_2">
      <img
        class="image_6"
        referrerpolicy="no-referrer"
        src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng5b727b1da8591b50a16f335462b668ed3c9d990d7539f3741af9ebde6c96b421"
        @click="ActiveFunes('减')"
      />
      <div class="group_2">
        <img src="@/assets/images/yiliaoxiang.png" alt="" />
      </div>
      <div class="box_31">
        <span class="text_14">智慧随访包</span>
        <div class="section_8"></div>
        <span class="text_15"
          >支持多种检测设备，包含尿液、血糖、血压、体温、心电图（六导、十二导）、支持中医体质辨识、身份识别、血氧、脉率等检测，手提箱设计，数据实时同步。</span
        >
      </div>
      <img
        class="image_7"
        referrerpolicy="no-referrer"
        src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPngce59f9644e820397797e92e07d3796333123469c09e7b719e032226453706ed3"
        @click="ActiveFunes('加')"
      />
    </div>
    <div v-show="active === 4" class="section_2">
      <img
        class="image_6"
        referrerpolicy="no-referrer"
        src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng5b727b1da8591b50a16f335462b668ed3c9d990d7539f3741af9ebde6c96b421"
        @click="ActiveFunes('减')"
      />
      <div class="group_2">
        <img src="@/assets/images/wifihe.png" alt="" />
      </div>
      <div class="box_31">
        <span class="text_14">边缘计算网关</span>
        <div class="section_8"></div>
        <span class="text_15"
          >超低功耗边缘计算网关，支持多种经典协议接入，支持有线、无线接入，支持设备定位、开关机监测、距离监测。</span
        >
      </div>
      <img
        class="image_7"
        referrerpolicy="no-referrer"
        src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPngce59f9644e820397797e92e07d3796333123469c09e7b719e032226453706ed3"
        @click="ActiveFunes('加')"
      />
    </div>
    <div v-show="active === 5" class="section_2">
      <img
        class="image_6"
        referrerpolicy="no-referrer"
        src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng5b727b1da8591b50a16f335462b668ed3c9d990d7539f3741af9ebde6c96b421"
        @click="ActiveFunes('减')"
      />
      <div class="group_2">
        <img src="@/assets/images/jiqiqiren.png" alt="" />
      </div>
      <div class="box_31">
        <span class="text_14">AI基层辅助机器人</span>
        <div class="section_8"></div>
        <span class="text_15"
          >科技元素赋能基层实用场景，机器人支持无人值守模式，支持动线引导、人脸身份识别、疾病问诊、远程诊断等，培养患者习惯，减少人力成本。</span
        >
      </div>
      <img
        class="image_7"
        referrerpolicy="no-referrer"
        src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPngce59f9644e820397797e92e07d3796333123469c09e7b719e032226453706ed3"
        @click="ActiveFunes('加')"
      />
    </div>
    <div class="section_3">
      <div class="section_3_top">
        <div class="section_3_left">
          <span class="section_t">
            <span class="roul">73%</span>
            <span class="fzie">在出院前死亡的1855名术后患者中，有73%的患者在死亡前没有转入重症监护。</span>
          </span>
          <span class="hang"
            >医疗行业当前面临患者安全和工作效率的严峻挑战。应对挑战，卫软坚持守护生命健康，让医护人员与患者共同感受到高品质穿戴监护的便利与舒适。</span
          >
        </div>
        <div class="section_3_right">
          <img src="@/assets/images/zhuanbiao.png" alt="" class="imgss" />
        </div>
      </div>
      <div class="section_3_bottom">
        <div class="section_box">
          <img src="@/assets/images/sos.png" alt="" class="imgs_1" />
          <span class="section_text">SOS紧急呼叫</span>
        </div>
        <div class="section_box">
          <img src="@/assets/images/naoling.png" alt="" class="imgs_1" />
          <span class="section_text">健康提醒</span>
        </div>
        <div class="section_box">
          <img src="@/assets/images/shuidi.png" alt="" class="imgs_1" />
          <span class="section_text">动态血压</span>
        </div>
        <div class="section_box">
          <img src="@/assets/images/yuantu.png" alt="" class="imgs_1" />
          <span class="section_text">血氧监测</span>
        </div>
        <div class="section_box">
          <img src="@/assets/images/aixin.png" alt="" class="imgs_1" />
          <span class="section_text">心率监测</span>
        </div>
        <div class="section_box">
          <img src="@/assets/images/tiwen.png" alt="" class="imgs_1" />
          <span class="section_text">体温测量</span>
        </div>
        <div class="section_box">
          <img src="@/assets/images/shuimian.png" alt="" class="imgs_1" />
          <span class="section_text">睡眠呼吸</span>
        </div>
      </div>
    </div>
    <div class="section_4">
      <div class="section_4_top">
        <div class="text_section_4">内外兼顾 健康无忧</div>
        <div class="text_da">打通院外数据，形成院内外数据结合的整体健康档案</div>
        <div class="box_section_4">
          <div style="height: 31.25rem">
            <img src="@/assets/images/shoubiaoshouji.png" alt="" class="phone_img" />
            <span class="phone_1">居家数据实时同步，您的健康我来守护</span>
            <span class="pitr_1">院外监测</span>
            <img src="@/assets/images/zhibiao.png" alt="" class="phone_imgs" />
            <span class="phone_2">院内患者实时监测，有我在，您放心</span>
            <span class="pitr_2">院内监测</span>
          </div>
          <div class="box_sectionsm">
            <img src="@/assets/images/liangbiao.png" alt="" class="phone_imgss" />
            <img src="@/assets/images/dianjibiao.png" alt="" class="phone_imgsss" />
            <span class="phone_3">无论哪里，您的健康有我守护</span>
          </div>
        </div>
      </div>
      <div class="section_4_bottom">
        <div class="box">
          <div class="section_4_left">
            <img src="@/assets/images/diannao.png" alt="" class="diannaoimg" />
          </div>
          <div class="section_4_right">
            <div class="box_right">
              <div class="text_1">数据实时同步</div>
              <div class="text_2">院内院外数据兼顾，便于医生实时了解患者情况</div>
              <div class="text_3">
                系统中设计“穿戴日记”模块，实时同步穿戴数据。
                含心率、血氧、体温、睡眠变化曲线展示，并筛查异常数据进行预警提示。
              </div>
              <div class="text_4">价值</div>
              <div class="text_5">
                <img src="@/assets/images/fangkuai.png" alt="" class="kuai_img" />
                <span class="yuan_text">院内院外 双管齐下</span>
              </div>
              <div class="text_6">打通院外数据，进行居家监测，符合国家的政策要求。</div>
              <div class="text_5">
                <img src="@/assets/images/fangkuai.png" alt="" class="kuai_img" />
                <span class="yuan_text">健康档案 内外结合</span>
              </div>
              <div class="text_6">全方面、全场景健康档案，形成院内外结合的整体档案。</div>
              <div class="text_5">
                <img src="@/assets/images/fangkuai.png" alt="" class="kuai_img" />
                <span class="yuan_text">提高效率 降低成本</span>
              </div>
              <div class="text_6">佩戴即有数据，随时随地检测，大大节省人力、物力，提高效率、降低成本。</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="section_5">
      <div class="section_5box">基本参数</div>
      <div class="section_5box1"></div>
      <div class="section_5box2">PRODUCT ADVANTAGES</div>
      <div class="section_5box3">
        <div class="section_5_box3_top">
          <span class="text_s5_1">外观参数</span>
          <span>
            <img v-if="isTrue" @click="clickFun" src="@/assets/images/shangla.png" alt="" class="chaimg" />
            <img v-else @click="clickFun" src="@/assets/images/xiala.png" alt="" class="chaimg" />
          </span>
        </div>
        <div v-show="isTrue" class="section_5_box3_bottom">
          <div class="secbox">
            <div class="box_text">外壳尺寸</div>
            <div class="box_text1">43.8*37.8*12mm</div>
            <div class="box_text">表镜材质</div>
            <div class="box_text1">二次钢化加硬玻璃镜面</div>
            <div class="box_text">电池材质</div>
            <div class="box_text1">聚合物</div>
            <div class="box_text">整机重量</div>
            <div class="box_text1">36g</div>
          </div>
          <div class="secbox">
            <div class="box_text">屏幕尺寸</div>
            <div class="box_text1">1.54寸</div>
            <div class="box_text">表扣方式</div>
            <div class="box_text1">标准金属表扣</div>
            <div class="box_text">屏幕技术</div>
            <div class="box_text1">全贴合</div>
            <div class="box_text">背光颜色</div>
            <div class="box_text1">LED白光</div>
          </div>
          <div class="secbox">
            <div class="box_text">表壳材质</div>
            <div class="box_text1">PC+ABS</div>
            <div class="box_text">背板材质</div>
            <div class="box_text1">PC+ABS</div>
            <div class="box_text">显示参数</div>
            <div class="box_text1">240*240*RGB</div>
            <div class="box_text">电池容量</div>
            <div class="box_text1">700MAH</div>
          </div>
          <div class="secbox">
            <div class="box_text">表带材质</div>
            <div class="box_text1">硅胶</div>
            <div class="box_text">按键数量</div>
            <div class="box_text1">2</div>
            <div class="box_text">显示颜色</div>
            <div class="box_text1">彩色</div>
            <div class="box_text">背光时长和亮度</div>
            <div class="box_text1">8S</div>
          </div>
        </div>
      </div>
      <div class="section_5box4">
        <div class="box_tops">
          <span class="text_gong">功能参数</span>
          <span class="text_sp">
            <img v-if="isTrues" @click="clickFuns" src="@/assets/images/shangla.png" alt="" class="chaimgs" />
            <img v-else @click="clickFuns" src="@/assets/images/xiala.png" alt="" class="chaimgs" />
          </span>
        </div>
        <div v-show="isTrues" class="box_bottoms">
          <div class="bootom_top">
            <div class="box_q1">
              <div class="pu_box">防尘防水</div>
              <div class="Npu_box">IP65</div>
              <div class="pu_box">静电等级</div>
              <div class="Npu_box">10+6~10+12ohm</div>
            </div>
            <div class="box_q1">
              <div class="pu_box">按键功能</div>
              <div class="Npu_box">SOS及功能键</div>
              <div class="pu_box">监测续航（心率连续监测）</div>
              <div class="Npu_box">一小时一次待机15天</div>
            </div>
            <div class="box_q1">
              <div class="pu_box">操作逻辑</div>
              <div class="Npu_box">按键切换/全触屏</div>
              <div class="pu_box">整机功耗</div>
              <div class="Npu_box">30MAh/天</div>
            </div>
            <div class="box_q2">
              <div class="pu_box">待机续航</div>
              <div class="Npu_box">15天</div>
            </div>
          </div>
          <div class="bootom_bottom">
            主要部件功耗说明：
            <div class="text_se1">单导心电（ECG）、心率、血压（PPG）、定位、计步、睡眠、体温、GPS定位、4G卡</div>
          </div>
        </div>
      </div>
    </div>
    <div class="section_6">
      <div class="imgse">
        <div class="info">支持HUAWEI、小米、OPPO、VIVO等一线品牌</div>
      </div>
    </div>
    <div class="section_7">
      <div class="section_text_1">应用场景</div>
      <div class="section_text_2"></div>
      <div class="section_text_3">PRODUCT ADVANTAGES</div>
      <div class="section_box_1">
        <div class="box_1">
          <img src="@/assets/images/pepol.png" alt="" class="peoimg" />
          <div class="text_1">智慧养老</div>
          <div class="text_2">异常预警，防止意外事件发生，有针对性给予老年人群健康关怀。</div>
        </div>
        <div class="box_2">
          <img src="@/assets/images/jiaonang.png" alt="" class="peoimg" />
          <div class="text_1">药店、中西医诊断</div>
          <div class="text_2">实时数据有助于医生诊断、开处方、院外随访。</div>
        </div>
        <div class="box_1">
          <img src="@/assets/images/lanxin.png" alt="" class="peoimg" />
          <div class="text_1">院内外健康监测</div>
          <div class="text_2">多条数据统计、支持出院回访、病情观察患者。</div>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.image-main {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: 160rem;
  margin: 0 auto;
  overflow: hidden;
  font-family: PingFangSC, PingFang SC;
  img {
    width: 100%;
  }
  .banner {
    width: 100%;
    margin-top: 3.5rem;
    display: flex;
    justify-content: center;
    img {
      width: 100%;
      height: auto;
    }
    .info {
      position: absolute;
      width: 43.75rem;
      height: 11.5rem;
      right: 18.75rem;
      top: 13rem;
      text-align: center;
      .label {
        font-size: 2.25rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #222;
        line-height: 4.375rem;
        margin-bottom: 1.875rem;
      }
      .text {
        font-size: 1.25rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #666666;
      }
    }
  }
  .section_1 {
    background-color: rgba(255, 255, 255, 1);
    width: 100%;
    height: 22.6875rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    .text_10 {
      width: 30rem;
      height: 3.5rem;
      overflow-wrap: break-word;
      color: rgba(34, 34, 34, 1);
      font-size: 1.875rem;
      font-family: PingFangSC-Medium;
      font-weight: 500;
      text-align: left;
      white-space: nowrap;
      line-height: 3.5rem;
      margin: 4.375rem 0 0 0;
      text-align: center;
    }
    .section_6 {
      background-color: rgba(2, 129, 224, 1);
      width: 3.125rem;
      height: 0.25rem;
      margin: 0.625rem 0 0 0;
    }
    .text_11 {
      width: 15.4375rem;
      height: 1.75rem;
      overflow-wrap: break-word;
      color: rgba(102, 102, 102, 1);
      font-size: 1rem;
      font-family: PingFangSC-Regular;
      font-weight: normal;
      text-align: left;
      white-space: nowrap;
      line-height: 1.75rem;
      margin: 0.625rem 0 0 0;
      text-align: center;
    }
    .list_3 {
      cursor: pointer;
      width: 92.5rem;
      height: 7.8125rem;
      display: flex;
      justify-content: space-between;
      margin: 2.5rem 0 1.25rem 0;
    }
    .text-group_1-0 {
      border-radius: 0 0.625rem 0.625rem 0;
      position: relative;
      width: 23.125rem;
      height: 7.8125rem;
      display: flex;
      flex-direction: column;
      justify-content: flex-center;
      background: rgba(72, 101, 255, 1);
    }
    .text_12-0 {
      width: 16.875rem;
      overflow-wrap: break-word;
      font-size: 1.5rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      text-align: left;
      white-space: nowrap;
      margin: 1.25rem 0 0 2.5rem;
      color: rgba(255, 255, 255, 1);
    }
    .text_13-0 {
      width: 12.5rem;
      overflow-wrap: break-word;
      font-size: 1rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      text-align: left;
      white-space: nowrap;
      margin: 0.625rem 0 1.25rem 2.5rem;
      color: rgba(255, 255, 255, 1);
    }
    .image_4-0 {
      position: absolute;
      left: 0;
      top: -2.5625rem;
      width: 0.125rem;
      height: 10.3125rem;
    }
    .text-group_1-1 {
      border-radius: 0 0.625rem 0.625rem 0;
      position: relative;
      width: 23.125rem;
      height: 7.8125rem;
      display: flex;
      flex-direction: column;
      justify-content: flex-center;
      background: rgba(216, 216, 216, 0);
    }
    .text_12-1 {
      width: 16.875rem;
      overflow-wrap: break-word;
      font-size: 1.5rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      text-align: left;
      white-space: nowrap;
      margin: 1.25rem 0 0 2.5rem;
      color: rgba(34, 34, 34, 1);
    }
    .text_13-1 {
      width: 12.5rem;
      overflow-wrap: break-word;
      font-size: 1rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: normal;
      text-align: left;
      white-space: nowrap;
      margin: 0.625rem 0 1.25rem 2.5rem;
      color: rgba(102, 102, 102, 1);
    }
    .image_2-1 {
      position: absolute;
      left: 0;
      top: 1.25rem;
      width: 0.0625rem;
      height: 7.8125rem;
    }
    .text-group_1-2 {
      border-radius: 0 0.625rem 0.625rem 0;
      position: relative;
      width: 23.125rem;
      height: 7.8125rem;
      display: flex;
      flex-direction: column;
      justify-content: flex-center;
      background: rgba(216, 216, 216, 0);
    }
    .text_12-2 {
      width: 16.875rem;
      overflow-wrap: break-word;
      font-size: 1.5rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      text-align: left;
      white-space: nowrap;
      margin: 1.25rem 0 0 2.5rem;
      color: rgba(34, 34, 34, 1);
    }
    .text_13-2 {
      width: 12.5rem;
      overflow-wrap: break-word;
      font-size: 1rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      text-align: left;
      white-space: nowrap;
      margin: 0.625rem 0 1.25rem 2.5rem;
      color: rgba(102, 102, 102, 1);
    }
    .image_2-2 {
      position: absolute;
      left: 0;
      top: 1.25rem;
      width: 0.0625rem;
      height: 7.8125rem;
    }
    .image_3-2 {
      position: absolute;
      left: 23.0625rem;
      top: 1.25rem;
      width: 0.0625rem;
      height: 7.8125rem;
    }
    .text-group_1-3 {
      border-radius: 0 0.625rem 0.625rem 0rem;
      position: relative;
      width: 23.125rem;
      height: 7.8125rem;
      display: flex;
      flex-direction: column;
      justify-content: flex-center;
      background: rgba(216, 216, 216, 0);
    }
    .text_12-3 {
      width: 16.875rem;
      overflow-wrap: break-word;
      font-size: 1.5rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      text-align: left;
      white-space: nowrap;
      margin: 1.25rem 0 0 2.5rem;
      color: rgba(34, 34, 34, 1);
    }
    .text_13-3 {
      width: 12.5rem;
      overflow-wrap: break-word;
      font-size: 1rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      text-align: left;
      white-space: nowrap;
      margin: 0.625rem 0 1.25rem 2.5rem;
      color: rgba(102, 102, 102, 1);
    }
    .image_3-3 {
      position: absolute;
      left: 23.0625rem;
      top: 1.25rem;
      width: 0.0625rem;
      height: 7.8125rem;
    }
  }
  .section_2 {
    background-color: rgba(34, 34, 34, 1);
    width: 100%;
    height: 12.5rem;
    display: flex;
    justify-content: center;
    align-items: center;
    .group_2 {
      width: 23.125rem;
      height: 12.5rem;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .image_5 {
      width: 1.25rem;
      height: 2.1875rem;
      margin: 6.75rem 0 0 10.0625rem;
    }
    .box_31 {
      width: 65rem;
      height: 9.6875rem;
      margin: 2.5rem 0 0 4.375rem;
    }
    .text_14 {
      width: 15rem;
      overflow-wrap: break-word;
      color: rgba(255, 255, 255, 1);
      font-size: 1.5rem;
      font-family: PingFangSC-Medium;
      font-weight: 500;
      text-align: left;
      white-space: nowrap;
    }
    .section_8 {
      background-color: rgba(255, 255, 255, 1);
      width: 5rem;
      height: 0.25rem;
      margin-top: 0.9375rem;
    }
    .text_15 {
      width: 56.25rem;
      height: 3.25rem;
      overflow-wrap: break-word;
      color: rgba(255, 255, 255, 1);
      font-size: 1rem;
      font-family: PingFangSC-Regular;
      font-weight: normal;
      text-align: left;
      display: block;
      margin-top: 1.25rem;
    }
    .image_7 {
      width: 1.25rem;
      height: 2.1875rem;
      margin: 0 10.0625rem 0 2.4375rem;
    }
    .image_6 {
      width: 1.25rem;
      height: 2.5rem;
      margin-right: 2.5rem;
      margin-left: 10rem;
    }
  }
  .section_3 {
    font-family: PingFangSC, PingFang SC;
    width: 100%;
    height: 39.375rem;
    background: linear-gradient(180deg, #d9ecfa 0%, #f6fbff 100%);
    display: flex;
    flex-direction: column;
    .section_3_top {
      height: 29.375rem;
      display: flex;
      .section_3_left {
        display: flex;
        flex-direction: column;
        .section_t {
          display: flex;
          align-items: center;
          margin-top: 4.375rem;
          width: 74.9375rem;
          height: 6.25rem;
          background: #ffffff;
          border-radius: 0 1.875rem 1.875rem 0;
          .roul {
            color: #da3900;
            font-size: 1.5rem;
            margin-left: 13.6875rem;
            margin-right: 2.5rem;
          }
          .fzie {
            width: 49.5rem;
            font-size: 20px;
            color: #666666;
          }
        }
        .hang {
          width: 50rem;
          font-size: 1.25rem;
          color: #666666;
          margin-top: 6.25rem;
          margin-left: 13.6875rem;
        }
      }
      .section_3_right {
        .imgss {
          display: block;
          width: 22.5rem;
          height: 18.75rem;
          margin-top: 4.375rem;
          margin-left: 6.25rem;
        }
      }
    }
    .section_3_bottom {
      display: flex;
      justify-content: space-evenly;
      align-items: center;
      .section_box {
        display: flex;
        flex-direction: column;
        align-items: center;
        .section_text {
          font-size: 1rem;
          color: #222222;
          margin-top: 10px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
        }
        .imgs_1 {
          width: 3.75rem;
          height: 3.75rem;
        }
      }
    }
  }
  .section_4 {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    height: 95rem;
    padding-top: 1.25rem;
    img {
      border-radius: 0.9375rem;
    }
    .section_4_top {
      width: 100%;
      display: flex;
      flex-direction: column;
      .text_section_4 {
        display: flex;
        flex-direction: column;
        align-items: center;
        font-size: 1.875rem;
        color: #222222;
      }
      .text_da {
        display: flex;
        flex-direction: column;
        align-items: center;
        font-size: 1rem;
        color: #666666;
      }
      .box_section_4 {
        display: flex;
        justify-content: center;
        margin-top: 2.5rem;
        .phone_img {
          width: 37.5rem;
          height: 31.25rem;
          margin-left: 4.375rem;
          margin-right: 1.25rem;
          position: relative;
        }
        .phone_1 {
          width: 37.5rem;
          height: 3.125rem;
          position: absolute;
          background-color: #000000;
          left: 4.375rem;
          bottom: 0;
          border-radius: 0 0 0.9375rem 0.9375rem;
          opacity: 0.5;
          color: #ffffff;
          display: flex;
          justify-content: center;
          align-items: center;
        }
        .pitr_1 {
          position: absolute;
          left: 5.625rem;
          top: 0.625rem;
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
        }
        .pitr_2 {
          position: absolute;
          right: 19.375rem;
          top: 0.625rem;
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
        }
        .phone_2 {
          width: 23.125rem;
          height: 3.125rem;
          position: absolute;
          background-color: #000000;
          right: 1.25rem;
          bottom: 0;
          border-radius: 0 0 0.9375rem 0.9375rem;
          opacity: 0.5;
          color: #ffffff;
          display: flex;
          justify-content: center;
          align-items: center;
        }
        .phone_3 {
          width: 29.375rem;
          height: 3.125rem;
          position: absolute;
          background-color: #000000;
          right: 0;
          bottom: 0;
          border-radius: 0 0 0.9375rem 0.9375rem;
          opacity: 0.5;
          color: #ffffff;
          display: flex;
          justify-content: center;
          align-items: center;
        }
        .phone_imgs {
          width: 23.125rem;
          height: 31.25rem;
          margin-right: 1.25rem;
        }
        .box_sectionsm {
          display: flex;
          flex-direction: column;
          .phone_imgss {
            width: 29.375rem;
            height: 15rem;
            margin-bottom: 1.25rem;
          }
          .phone_imgsss {
            width: 29.375rem;
            height: 15rem;
            position: relative;
          }
        }
      }
    }
    .section_4_bottom {
      position: relative;
      margin-top: 1.875rem;
      width: 120rem;
      .section_4_left {
        position: absolute;
        top: 0;
        right: 56.25rem;
        width: 63.75rem;
        height: 50rem;
        img {
          width: 100%;
          height: 100%;
        }
        z-index: 2;
      }
      .section_4_right {
        position: absolute;
        top: 0;
        left: 47.5rem;
        z-index: 1;
        width: 72.5rem;
        height: 55rem;
        background: #e4f3ff;
        .box_right {
          width: 56.25rem;
          margin-left: 16.25rem;
          display: flex;
          flex-direction: column;
          .text_1 {
            margin-top: 4.375rem;
            margin-left: 3.125rem;
            color: #222222;
            font-size: 1.5rem;
          }
          .text_2 {
            font-size: 1rem;
            color: #666666;
            margin-top: 0.9375rem;
            margin-left: 3.125rem;
          }
          .text_3 {
            margin-top: 2.5rem;
            width: 39.375rem;
            height: 8.0625rem;
            font-size: 1.5rem;
            color: #222222;
            margin-left: 3.125rem;
          }
          .text_4 {
            color: #222222;
            font-size: 1.5rem;
            margin-top: 2.5rem;
            margin-bottom: 0.9375rem;
            margin-left: 3.125rem;
          }
          .text_5 {
            display: flex;
            align-items: center;
            margin-left: 3.125rem;
            .kuai_img {
              width: 1.125rem;
              height: 1.125rem;
            }
            .yuan_text {
              margin-left: 0.625rem;
              font-size: 1.25rem;
              color: #4865ff;
            }
          }
          .text_6 {
            width: 41.25rem;
            color: #666666;
            font-size: 1rem;
            margin-top: 0.9375rem;
            margin-left: 5rem;
            margin-bottom: 1.25rem;
          }
        }
      }
    }
  }
  .section_5 {
    width: 100%;
    display: flex;
    padding-bottom: 5rem;
    flex-direction: column;
    align-items: center;
    background: #f8f8f8;
    .section_5box {
      margin-top: 4.375rem;
      font-size: 1.875rem;
      color: #222222;
      font-weight: 500;
    }
    .section_5box1 {
      width: 3.125rem;
      height: 0.25rem;
      background: #0281e0;
      margin: 0.625rem 0;
    }
    .section_5box2 {
      font-size: 1rem;
      color: #666666;
    }
    .section_5box3 {
      width: 92.5rem;
      border-radius: 0.9375rem;
      margin-top: 2.5rem;
      display: flex;
      flex-direction: column;
      .section_5_box3_top {
        height: 6.875rem;
        display: flex;
        justify-content: space-between;
        background: #ffffff;
        border-top-left-radius: 0.9375rem;
        border-top-right-radius: 0.9375rem;
        .text_s5_1 {
          color: #222222;
          font-size: 1.5rem;
          margin-top: 2.1875rem;
          margin-left: 4.3125rem;
        }
        .chaimg {
          width: 1.375rem;
          height: 0.75rem;
          margin-top: 2.75rem;
          margin-right: 4.375rem;
        }
      }
      .section_5_box3_bottom {
        display: flex;
        // height: 31.4375rem;
        justify-content: space-around;
        border-top: 0.0625rem solid #c7c7c7;
        background: #ffffff;
        border-bottom-left-radius: 0.9375rem;
        border-bottom-right-radius: 0.9375rem;
        .secbox {
          display: flex;
          flex-direction: column;
          align-items: center;
          margin-top: 3.125rem;
          .box_text {
            font-size: 1rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #666666;
            margin-bottom: 0.625rem;
          }
          .box_text1 {
            font-size: 1rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #222222;
            margin-bottom: 2.5rem;
          }
        }
      }
    }
    .section_5box4 {
      display: flex;
      width: 92.5rem;
      margin-top: 2.5rem;
      flex-direction: column;
      border-radius: 0.9375rem;
      .box_tops {
        height: 6.875rem;
        display: flex;
        justify-content: space-between;
        background: #ffffff;
        align-items: center;
        border-top-left-radius: 0.9375rem;
        border-top-right-radius: 0.9375rem;
        .text_sp {
          display: block;
          margin-right: 4.4375rem;
        }
        .text_gong {
          font-size: 1.5rem;
          color: #222222;
          margin-left: 4.3125rem;
          .chaimgs {
            width: 1.375rem;
            height: 0.75rem;
            margin-top: 2.75rem;
            margin-right: 4.375rem;
          }
        }
      }
      .box_bottoms {
        display: flex;
        flex-direction: column;
        background: #ffffff;
        border-top: 0.0625rem solid #c7c7c7;
        border-bottom-left-radius: 0.9375rem;
        border-bottom-right-radius: 0.9375rem;
        .bootom_top {
          margin-top: 3.125rem;
          display: flex;
          justify-content: space-around;
          align-items: center;
          .box_q1 {
            display: flex;
            flex-direction: column;
            align-items: center;
            .pu_box {
              color: #666666;
              font-size: 1rem;
              margin-bottom: 0.625rem;
              font-family: PingFangSC, PingFang SC;
            }
            .Npu_box {
              color: #222222;
              font-size: 1rem;
              font-family: PingFangSC, PingFang SC;
              margin-bottom: 2.5rem;
            }
          }
          .box_q2 {
            display: flex;
            flex-direction: column;
            height: 11.9375rem;
            .pu_box {
              color: #666666;
              font-size: 1rem;
              margin-bottom: 0.625rem;
            }
            .Npu_box {
              color: #222222;
              font-size: 1rem;
              margin-bottom: 2.5rem;
              text-align: center;
            }
          }
        }
        .bootom_bottom {
          padding-bottom: 1.875rem;
          display: flex;
          color: #666666;
          font-size: 1rem;
          padding-left: 4.3125rem;
          .text_se1 {
            color: #222222;
            font-size: 1rem;
          }
        }
      }
    }
  }
  .section_6 {
    width: 100%;
    height: 32.5rem;
    display: flex;
    justify-content: center;
    .imgse {
      width: 100rem;
      height: 28.125rem;
      background-image: url('@/assets/images/shoubiaodi.png');
      background-size: 100% 100%;
      position: relative;
      .info {
        position: absolute;
        top: 3.75rem;
        left: 30rem;
        width: 52.9375rem;
        height: 3.5rem;
        font-size: 1.875rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #000000;
        line-height: 3.5rem;
      }
    }
  }
  .section_7 {
    margin-top: 4.375rem;
    width: 100%;
    padding-bottom: 9.375rem;
    background-image: url('@/assets/images/bjtu.png');
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    .section_text_1 {
      color: #ffffff;
      font-size: 1.875rem;
      display: flex;
      font-family: PingFangSC, PingFang SC;
      justify-content: center;
      margin-top: 4.375rem;
    }
    .section_text_2 {
      width: 3.125rem;
      height: 0.25rem;
      background: #ffffff;
      margin: 0.625rem 0;
    }
    .section_text_3 {
      color: #ffffff;
      font-size: 1rem;
      margin-bottom: 3.125rem;
      font-family: PingFangSC, PingFang SC;
    }
    .section_box_1 {
      width: 68.75rem;
      display: flex;
      margin-left: -13.6875rem;
      .box_1 {
        width: 18.75rem;
        .peoimg {
          width: 3.75rem;
          height: 3.75rem;
        }
        .text_1 {
          font-size: 1.5rem;
          color: #ffffff;
          margin-top: 1.25rem;
          margin-bottom: 0.9375rem;
          font-family: PingFangSC, PingFang SC;
        }
        .text_2 {
          font-size: 1rem;
          color: #ffffff;
          font-family: PingFangSC, PingFang SC;
        }
      }
      .box_2 {
        width: 18.75rem;
        margin: 0 6.25rem;
        .peoimg {
          width: 3.75rem;
          height: 3.75rem;
        }
        .text_1 {
          font-size: 1.5rem;
          color: #ffffff;
          margin-top: 1.25rem;
          margin-bottom: 0.9375rem;
          font-family: PingFangSC, PingFang SC;
        }
        .text_2 {
          font-size: 1rem;
          color: #ffffff;
          font-family: PingFangSC, PingFang SC;
        }
      }
    }
  }
}
</style>
