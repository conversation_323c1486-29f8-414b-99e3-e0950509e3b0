<script setup>
import { ref } from 'vue'
const isTrue = ref(true)
const isTrues = ref(true)
const active = ref(1)
const clickFun = () => {
  isTrue.value = !isTrue.value
}
const clickFuns = () => {
  isTrues.value = !isTrues.value
}
const ActiveFuns = val => {
  active.value = val
}
const ActiveFunes = index => {
  if (index === '加') {
    if (active.value === 5) {
      active.value = 1
    } else {
      active.value += 1
    }
  } else {
    if (active.value === 1) {
      active.value = 5
    } else {
      active.value -= 1
    }
  }
}
</script>
<template>
  <div class="banner">
    <div class="info">
      <div class="info_1">村医智慧随访包</div>
      <div class="info_2">一机多用，实时上传。</div>
    </div>
  </div>
  <div class="section_main">
    <div class="section_1">
      <span class="text_10">智能硬件产品助您超越所能</span>
      <div class="section_6_1"></div>
      <span class="text_11">INTELLIGENT&nbsp;HARDWARE</span>
      <div v-show="active === 1" class="list_3">
        <div class="text-group_1-0" @click="ActiveFuns(1)">
          <span class="text_12-0">智慧随访包</span>
          <span class="text_13-0">一机多用 实时上传</span>
          <img
            class="image_4-0"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng3b9a8121db0a1bcc7c1a65f5a79361a0bbbf877f3a659cc948ad087a9b9f03cb"
          />
        </div>
        <div class="text-group_1-1" @click="ActiveFuns(2)">
          <span class="text_12-1">边缘计算网关</span>
          <span class="text_13-1">万物互联 实时监控</span>
          <img
            class="image_2-1"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
        <div class="text-group_1-2" @click="ActiveFuns(3)">
          <span class="text_12-2">AI基层辅助机器人</span>
          <span class="text_13-2">人脸识别 自助问诊 远程诊断</span>
          <img
            class="image_2-2"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
          <img
            class="image_3-2"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
        <div class="text-group_1-3" @click="ActiveFuns(4)">
          <span class="text_12-3">卫软穿戴设备</span>
          <span class="text_13-3">实时监测 安心保障</span>
          <img
            class="image_3-3"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
      </div>
      <div v-show="active === 2" class="list_3">
        <div class="text-group_1-0" @click="ActiveFuns(2)">
          <span class="text_12-0">边缘计算网关</span>
          <span class="text_13-0">万物互联 实时监控</span>
          <img
            class="image_4-0"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng3b9a8121db0a1bcc7c1a65f5a79361a0bbbf877f3a659cc948ad087a9b9f03cb"
          />
        </div>
        <div class="text-group_1-1" @click="ActiveFuns(3)">
          <span class="text_12-1">AI基层辅助机器人</span>
          <span class="text_13-1">人脸识别 自助问诊 远程诊断</span>
          <img
            class="image_2-1"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
        <div class="text-group_1-2" @click="ActiveFuns(4)">
          <span class="text_12-2">卫软穿戴设备</span>
          <span class="text_13-2">实时监测 安心保障</span>
          <img
            class="image_2-2"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
          <img
            class="image_3-2"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
        <div class="text-group_1-3" @click="ActiveFuns(5)">
          <span class="text_12-3">健康检测一体机</span>
          <span class="text_13-3">智慧体检 一机搞定</span>
          <img
            class="image_3-3"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
      </div>
      <div v-show="active === 3" class="list_3">
        <div class="text-group_1-0" @click="ActiveFuns(3)">
          <span class="text_12-0">AI基层辅助机器人</span>
          <span class="text_13-0">人脸识别 自助问诊 远程诊断</span>
          <img
            class="image_4-0"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng3b9a8121db0a1bcc7c1a65f5a79361a0bbbf877f3a659cc948ad087a9b9f03cb"
          />
        </div>
        <div class="text-group_1-1" @click="ActiveFuns(4)">
          <span class="text_12-1">卫软穿戴设备</span>
          <span class="text_13-1">实时监测 安心保障</span>
          <img
            class="image_2-1"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
        <div class="text-group_1-2" @click="ActiveFuns(5)">
          <span class="text_12-2">健康检测一体机</span>
          <span class="text_13-2">智慧体检 一机搞定</span>
          <img
            class="image_2-2"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
          <img
            class="image_3-2"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
        <div class="text-group_1-3" @click="ActiveFuns(1)">
          <span class="text_12-3">智慧随访包</span>
          <span class="text_13-3">一机多用 实时上传</span>
          <img
            class="image_3-3"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
      </div>
      <div v-show="active === 4" class="list_3">
        <div class="text-group_1-0" @click="ActiveFuns(4)">
          <span class="text_12-0">卫软穿戴设备</span>
          <span class="text_13-0">实时监测&nbsp;安心保障</span>
          <img
            class="image_4-0"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng3b9a8121db0a1bcc7c1a65f5a79361a0bbbf877f3a659cc948ad087a9b9f03cb"
          />
        </div>
        <div class="text-group_1-1" @click="ActiveFuns(5)">
          <span class="text_12-1">健康检测一体机</span>
          <span class="text_13-1">智慧体检&nbsp;一机搞定</span>
          <img
            class="image_2-1"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
        <div class="text-group_1-2" @click="ActiveFuns(1)">
          <span class="text_12-2">智彗随访包</span>
          <span class="text_13-2">一机多用&nbsp;实时上传</span>
          <img
            class="image_2-2"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
          <img
            class="image_3-2"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
        <div class="text-group_1-3" @click="ActiveFuns(2)">
          <span class="text_12-3">边缘计算网关</span>
          <span class="text_13-3">万能互联&nbsp;实时监控</span>
          <img
            class="image_3-3"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
      </div>
      <div v-show="active === 5" class="list_3">
        <div class="text-group_1-0" @click="ActiveFuns(5)">
          <span class="text_12-0">健康检测一体机</span>
          <span class="text_13-0">智慧体检 一机搞定</span>
          <img
            class="image_4-0"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng3b9a8121db0a1bcc7c1a65f5a79361a0bbbf877f3a659cc948ad087a9b9f03cb"
          />
        </div>
        <div class="text-group_1-1" @click="ActiveFuns(1)">
          <span class="text_12-1">智慧随访包</span>
          <span class="text_13-1">一机多用 实时上传</span>
          <img
            class="image_2-1"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
        <div class="text-group_1-2" @click="ActiveFuns(2)">
          <span class="text_12-2">边缘计算网关</span>
          <span class="text_13-2">万物互联 实时监控</span>
          <img
            class="image_2-2"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
          <img
            class="image_3-2"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
        <div class="text-group_1-3" @click="ActiveFuns(3)">
          <span class="text_12-3">AI基层辅助机器人</span>
          <span class="text_13-3">人脸识别 自助问诊 远程诊断</span>
          <img
            class="image_3-3"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
      </div>
    </div>
    <div v-show="active === 1" class="section_2">
      <img
        class="image_5"
        referrerpolicy="no-referrer"
        src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng5b727b1da8591b50a16f335462b668ed3c9d990d7539f3741af9ebde6c96b421"
        @click="ActiveFunes('减')"
      />
      <div class="group_2">
        <img src="@/assets/images/yiliaoxiang.png" alt="" />
      </div>
      <div class="box_31">
        <span class="text_14">智慧随访包</span>
        <div class="section_8s2"></div>
        <span class="text_15"
          >支持多种检测设备，包含尿液、血糖、血压、体温、心电图（六导、十二导）、支持中医体质辨识、身份识别、血氧、脉率等检测，手提箱设计，数据实时同步。</span
        >
      </div>
      <img
        class="image_6"
        referrerpolicy="no-referrer"
        src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPngce59f9644e820397797e92e07d3796333123469c09e7b719e032226453706ed3"
        @click="ActiveFunes('加')"
      />
    </div>
    <div v-show="active === 2" class="section_2">
      <img
        class="image_5"
        referrerpolicy="no-referrer"
        src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng5b727b1da8591b50a16f335462b668ed3c9d990d7539f3741af9ebde6c96b421"
        @click="ActiveFunes('减')"
      />
      <div class="group_2">
        <img src="@/assets/images/wifihe.png" alt="" />
      </div>
      <div class="box_31">
        <span class="text_14">边缘计算网关</span>
        <div class="section_8s2"></div>
        <span class="text_15"
          >超低功耗边缘计算网关，支持多种经典协议接入，支持有线、无线接入，支持设备定位、开关机监测、距离监测。</span
        >
      </div>
      <img
        class="image_6"
        referrerpolicy="no-referrer"
        src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPngce59f9644e820397797e92e07d3796333123469c09e7b719e032226453706ed3"
        @click="ActiveFunes('加')"
      />
    </div>
    <div v-show="active === 3" class="section_2">
      <img
        class="image_5"
        referrerpolicy="no-referrer"
        src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng5b727b1da8591b50a16f335462b668ed3c9d990d7539f3741af9ebde6c96b421"
        @click="ActiveFunes('减')"
      />
      <div class="group_2">
        <img src="@/assets/images/jiqiqiren.png" alt="" />
      </div>
      <div class="box_31">
        <span class="text_14">AI基层辅助机器人</span>
        <div class="section_8s2"></div>
        <span class="text_15"
          >科技元素赋能基层实用场景，机器人支持无人值守模式，支持动线引导、人脸身份识别、疾病问诊、远程诊断等，培养患者习惯，减少人力成本。</span
        >
      </div>
      <img
        class="image_6"
        referrerpolicy="no-referrer"
        src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPngce59f9644e820397797e92e07d3796333123469c09e7b719e032226453706ed3"
        @click="ActiveFunes('加')"
      />
    </div>
    <div v-show="active === 4" class="section_2">
      <img
        class="image_5"
        referrerpolicy="no-referrer"
        src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng5b727b1da8591b50a16f335462b668ed3c9d990d7539f3741af9ebde6c96b421"
        @click="ActiveFunes('减')"
      />
      <div class="group_2">
        <img src="@/assets/images/shoubiao1.png" alt="" />
      </div>
      <div class="box_31">
        <span class="text_14">卫软穿戴设备</span>
        <div class="section_8s2"></div>
        <span class="text_15"
          >卫软手表定制，支持单导心电、心率、PPG血压监测、血氧、睡眠、体温、GPS、4G通信模块等；卫软同时支持华为、OPPO等一线厂家品牌开发接入。</span
        >
      </div>
      <img
        class="image_6"
        referrerpolicy="no-referrer"
        src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPngce59f9644e820397797e92e07d3796333123469c09e7b719e032226453706ed3"
        @click="ActiveFunes('加')"
      />
    </div>
    <div v-show="active === 5" class="section_2">
      <img
        class="image_5"
        referrerpolicy="no-referrer"
        src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng5b727b1da8591b50a16f335462b668ed3c9d990d7539f3741af9ebde6c96b421"
        @click="ActiveFunes('减')"
      />
      <div class="group_2">
        <img src="@/assets/images/jixiebi.png" alt="" />
      </div>
      <div class="box_31">
        <span class="text_14">健康信息工作站</span>
        <div class="section_8s2"></div>
        <span class="text_15"
          >精准之道，内外兼修。高端配置高度集成一体化设计，健康宣教和自助体检结合，30多项健康检测指标，集秒级快检、健康指导、风险评估为一体，助力就诊、体检等多个场景，数据实时上传与统计。</span
        >
      </div>
      <img
        class="image_6"
        referrerpolicy="no-referrer"
        src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPngce59f9644e820397797e92e07d3796333123469c09e7b719e032226453706ed3"
        @click="ActiveFunes('加')"
      />
    </div>
    <div class="section_3">
      <div class="section_3_text">产品特色</div>
      <div class="section_3_xian"></div>
      <div class="section_3_zimu">PRODUCT ADVANTAGES</div>
      <div class="fang_bao">
        <div class="box_bao1">
          <img src="@/assets/images/lanquan.png" alt="" class="box_df" />
          <span class="box_de">防爆电容触摸：10.1英寸功能屏</span>
        </div>
        <div class="box_bao2">
          <img src="@/assets/images/lanquan.png" alt="" class="box_df1" />
          <span class="box_de1">数据对接：开放数据接口，支持数据上传第三方平台</span>
        </div>
      </div>
      <div class="section_3_xiang"></div>
      <div class="section_3_box">
        <div class="section_3_box_1">
          <img src="@/assets/images/shouti.png" alt="" />
          <div class="section_3_box_1_text">手提箱式设计 小巧携带方便</div>
          <div class="section_3_box_1_text1">全嵌入便携设计，方便下乡体检、实现轻松随访</div>
        </div>
        <div class="section_3_box_2">
          <img src="@/assets/images/jiaohu.png" alt="" />
          <div class="section_3_box_1_text">高端交互 快速建档</div>
          <div class="section_3_box_1_text1">全程动画、语音引导自助操作，刷身份证、或微信扫码建档测量</div>
        </div>
        <div class="section_3_box_3">
          <img src="@/assets/images/shenfen.png" alt="" />
          <div class="section_3_box_1_text">身份识别 方式多样</div>
          <div class="section_3_box_1_text1">支持多种识别方式：身份证、手机号、微信扫码、人脸识别</div>
        </div>
      </div>
    </div>
    <div class="section_4">
      <div class="section_4_box">
        <div class="section_4_box1">便携功能集成，轻松随访</div>
        <div class="section_4_box2">
          助力家医随访、便携设计、功能集成、项目满足基层公共卫生服务，实时测量、全程语音引导、同步数据上传、支持家医签约、健康管理、健康评估、健康指导与干预及拓展远程诊疗等服务。
        </div>
      </div>
    </div>
    <div class="section_5">
      <div class="section_5_box">
        <div class="section_5_box_text">功能特征</div>
        <div class="section_5_box_xian"></div>
        <div class="section_5_box_zimu">FUNCYIONAL CHATACTERISTICS</div>
      </div>
      <div class="section_5_box_1">
        <div class="section_5_box_1_1">
          <div class="top">
            <div class="section_5"></div>
            登录流程
          </div>
          <div class="bottom">通过刷卡、手机号、微信扫码三种方式任意一种建档、登录检测。</div>
        </div>
        <div class="section_5_box_1_1">
          <div class="top">
            <div class="section_5"></div>
            人脸识别
          </div>
          <div class="bottom">人脸识别快速建档，高端高效。</div>
        </div>
        <div class="section_5_box_1_1">
          <div class="top">
            <div class="section_5"></div>
            测量界面
          </div>
          <div class="bottom">体现检测者性别、年龄、照片信息。检测项目自动逐项检测，亦可手动选择需要测量的项目。</div>
        </div>
      </div>
      <div class="section_5_box_2"></div>
      <div class="section_5_box_3">
        <div class="left">
          <div class="top">
            <div class="section_5"></div>
            报告自动生成 支持打印
          </div>
          <div class="bottom">可通过机器自带打印机打印纸质报告单，同时亦可通过微信下发电子报告单。</div>
        </div>
        <div class="right">
          <div class="top">
            <div class="section_5"></div>
            个性化设置
          </div>
          <div class="bottom">
            通过账号密码进入后台系统进行设置，可根据实际使用情况随时调整每个项目的检测顺序、功能打开或关闭；可随时设置服务器地址、软件界面logo（可设置成客户自己的logo）、登录方式、二维码信息等
          </div>
        </div>
      </div>
    </div>
    <div class="section_6">
      <div class="section_6_top">
        <div class="section_6_top_text">适用行业</div>
        <div class="section_6_top_xian"></div>
        <div class="section_6_top_zimu">PRODUCT ADVANTAGES</div>
      </div>
      <div class="section_6_bottom">
        <div class="section_6_bottom_box_1">
          <div class="section_6_bottom_box_1_zhe">企事业单位健康防控</div>
        </div>
        <div class="section_6_bottom_box_2">
          <div class="section_6_bottom_box_2_zhe">基层卫生医疗机构</div>
        </div>
        <div class="section_6_bottom_box_3">
          <div class="section_6_bottom_box_3_zhe">智慧养老</div>
        </div>
        <div class="section_6_bottom_box_4">
          <div class="section_6_bottom_box_4_zhe">智慧随访</div>
        </div>
        <div class="section_6_bottom_box_5">
          <div class="section_6_bottom_box_5_zhe">学校</div>
        </div>
        <div class="section_6_bottom_box_6">
          <div class="section_6_bottom_box_6_zhe">连锁药房</div>
        </div>
      </div>
    </div>
    <div class="section_7">
      <div class="section_7_box">
        <div class="section_7_box_top">自动传输 数据保障</div>
        <div class="section_7_box_bottom">数据自动上传至平台，数据留存，安心保障</div>
      </div>
    </div>
    <div class="section_8">
      <div class="section_8_text">基本参数</div>
      <div class="section_8_xian"></div>
      <div class="section_8_zimu">PRODUCT ADVANTAGES</div>
      <div class="section_8_box">
        <span class="section_8_box_1">外观参数</span>
        <span class="section_8_box_2">
          <img
            v-if="isTrue"
            src="@/assets/images/shangla.png"
            alt=""
            style="width: 1.375rem; height: 0.75rem; margin-right: 4.375rem"
            @click="clickFun"
          />
          <img
            v-else
            src="@/assets/images/xiala.png"
            alt=""
            style="width: 1.375rem; height: 0.75rem; margin-right: 4.375rem"
            @click="clickFun"
          />
        </span>
      </div>
      <div v-show="isTrue" class="section_7_box4_2">
        <div class="box_1">
          <span class="text_1"> 尺寸 </span>
          <span class="text_2">535*385*200mm</span>
        </div>
        <div class="box_1">
          <span class="text_1"> 功耗 </span>
          <span class="text_2">约100W</span>
        </div>
        <div class="box_1">
          <span class="text_1"> 电源 </span>
          <span class="text_2">DC 12V</span>
        </div>
        <div class="box_1">
          <span class="text_1"> 网络 </span>
          <span class="text_2">有线接口、无线wifi</span>
        </div>
        <div class="box_1">
          <span class="text_1"> 触摸显示屏 </span>
          <span class="text_2">10.1英寸</span>
        </div>
        <div class="box_1">
          <span class="text_1"> 手提箱材质 </span>
          <span class="text_2">铝+泡棉</span>
        </div>
      </div>
      <div class="section_8_box_s">
        <span class="section_8_box_1">功能参数</span>
        <span class="section_8_box_2">
          <img
            v-if="isTrues"
            src="@/assets/images/shangla.png"
            alt=""
            style="width: 1.375rem; height: 0.75rem; margin-right: 4.375rem"
            @click="clickFuns"
          />
          <img
            v-else
            src="@/assets/images/xiala.png"
            alt=""
            style="width: 1.375rem; height: 0.75rem; margin-right: 4.375rem"
            @click="clickFuns"
          />
        </span>
      </div>
      <div v-show="isTrues" class="section_7_box4_1">
        <div class="box_1">
          <span class="text_1"> 血压计 </span>
          <span class="text_2">0-270mmHg、40-180次/分</span>
        </div>
        <div class="box_1">
          <span class="text_1"> 血氧测量仪 </span>
          <span class="text_2">70-100%</span>
        </div>
        <div class="box_1">
          <span class="text_1"> 红外额温计 </span>
          <span class="text_2">32.0℃-42.5℃</span>
        </div>
        <div class="box_1">
          <span class="text_1"> 心电图仪 </span>
          <span class="text_2">六导联、一十二导联</span>
        </div>
        <div class="box_1">
          <span class="text_1"> 多功能分析仪 </span>
          <span class="text_2">测试血糖、尿酸、总胆固醇</span>
        </div>
        <div class="box_1">
          <span class="text_1"> 尿液分析 </span>
          <span class="text_2">尿常规11项</span>
        </div>
        <div class="box_1">
          <span class="text_1"> 身份证读卡器 </span>
          <span class="text_2">非接触式IC卡技术</span>
        </div>
        <div class="box_1">
          <span class="text_1"> 健康体检软件 </span>
          <span class="text_2">支持第三方数据平台对接</span>
        </div>
        <div class="box_2">
          <span class="text_1">检测功能：</span>
          <span class="text_2">血压、血氧、体温、心电、血糖、尿酸、胆固醇、尿常规</span>
        </div>
      </div>
    </div>
  </div>
</template>
<style scoped lang="scss">
.banner {
  max-width: 160rem;
  width: 100%;
  height: 37.5rem;
  position: relative;
  margin: 0 auto;
  margin-top: 3.5rem;
  background-image: url('@/assets/images/suifangbao.png');
  background-size: 100% 100%;
  .info {
    position: absolute;
    top: 14.5rem;
    right: 18.75rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 43.75rem;
    .info_1 {
      width: 21.875rem;
      height: 4.375rem;
      font-size: 2.25rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      color: #222222;
      line-height: 4.375rem;
      text-align: center;
    }
    .info_2 {
      width: 43.75rem;
      height: 2.9375rem;
      font-size: 1.25rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #666666;
      line-height: 2.9375rem;
      margin-top: 1.25rem;
      display: flex;
      justify-content: center;
      text-align: center;
    }
  }
}
.section_main {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 auto;
  overflow: hidden;
  max-width: 160rem;
  .section_1 {
    background-color: rgba(255, 255, 255, 1);
    width: 100%;
    height: 22.6875rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    .text_10 {
      width: 30rem;
      height: 3.5rem;
      overflow-wrap: break-word;
      color: rgba(34, 34, 34, 1);
      font-size: 1.875rem;
      font-family: PingFangSC-Medium;
      font-weight: 500;
      text-align: center;
      white-space: nowrap;
      line-height: 3.5rem;
      margin: 4.375rem 0 0 0;
    }
    .section_6_1 {
      background-color: rgba(2, 129, 224, 1);
      width: 3.125rem;
      height: 0.25rem;
      margin: 0.625rem 0 0 0;
    }
    .text_11 {
      width: 15.4375rem;
      height: 1.75rem;
      overflow-wrap: break-word;
      color: rgba(102, 102, 102, 1);
      font-size: 1rem;
      font-family: PingFangSC-Regular;
      font-weight: normal;
      text-align: center;
      white-space: nowrap;
      line-height: 1.75rem;
      margin: 0.625rem 0 0 0;
    }
    .list_3 {
      cursor: pointer;
      width: 92.5rem;
      height: 7.8125rem;
      display: flex;
      justify-content: space-between;
      margin: 2.5rem 0 1.25rem 0;
    }
    .text-group_1-0 {
      border-radius: 0 0.625rem 0.625rem 0;
      position: relative;
      width: 23.125rem;
      height: 7.8125rem;
      display: flex;
      flex-direction: column;
      justify-content: flex-center;
      background: rgba(72, 101, 255, 1);
    }
    .text_12-0 {
      width: 16.875rem;
      height: 2.625rem;
      overflow-wrap: break-word;
      font-size: 1.5rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      text-align: left;
      white-space: nowrap;
      line-height: 2.625rem;
      margin: 1.25rem 0 0 2.5rem;
      color: rgba(255, 255, 255, 1);
    }
    .text_13-0 {
      width: 12.5rem;
      height: 2.0625rem;
      overflow-wrap: break-word;
      font-size: 1rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: normal;
      text-align: left;
      white-space: nowrap;
      line-height: 2.0625rem;
      margin: 0.625rem 0 1.25rem 2.5rem;
      color: rgba(255, 255, 255, 1);
    }
    .image_4-0 {
      position: absolute;
      left: 0;
      top: -2.5625rem;
      width: 0.125rem;
      height: 10.3125rem;
    }
    .text-group_1-1 {
      border-radius: 0 0.625rem 0.625rem 0;
      position: relative;
      width: 23.125rem;
      height: 7.8125rem;
      display: flex;
      flex-direction: column;
      justify-content: flex-center;
      background: rgba(216, 216, 216, 0);
    }
    .text_12-1 {
      width: 16.875rem;
      height: 2.625rem;
      overflow-wrap: break-word;
      font-size: 1.5rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      text-align: left;
      white-space: nowrap;
      line-height: 2.625rem;
      margin: 1.25rem 0 0 2.5rem;
      color: rgba(34, 34, 34, 1);
    }
    .text_13-1 {
      width: 12.5rem;
      height: 2.0625rem;
      overflow-wrap: break-word;
      font-size: 1rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      text-align: left;
      white-space: nowrap;
      line-height: 2.0625rem;
      margin: 0.625rem 0 1.25rem 2.5rem;
      color: rgba(102, 102, 102, 1);
    }
    .image_2-1 {
      position: absolute;
      left: 0;
      top: 1.25rem;
      width: 0.0625rem;
      height: 7.8125rem;
    }
    .text-group_1-2 {
      border-radius: 0 0.625rem 0.625rem 0;
      position: relative;
      width: 23.125rem;
      height: 7.8125rem;
      display: flex;
      flex-direction: column;
      justify-content: flex-center;
      background: rgba(216, 216, 216, 0);
    }
    .text_12-2 {
      width: 16.875rem;
      height: 2.625rem;
      overflow-wrap: break-word;
      font-size: 1.5rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      text-align: left;
      white-space: nowrap;
      line-height: 2.625rem;
      margin: 1.25rem 0 0 2.5rem;
      color: rgba(34, 34, 34, 1);
    }
    .text_13-2 {
      width: 12.5rem;
      height: 2.0625rem;
      overflow-wrap: break-word;
      font-size: 1rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      text-align: left;
      white-space: nowrap;
      line-height: 2.0625rem;
      margin: 0.625rem 0 1.25rem 2.5rem;
      color: rgba(102, 102, 102, 1);
    }
    .image_2-2 {
      position: absolute;
      left: 0;
      top: 1.25rem;
      width: 0.0625rem;
      height: 7.8125rem;
    }
    .image_3-2 {
      position: absolute;
      left: 23.0625rem;
      top: 1.25rem;
      width: 0.0625rem;
      height: 7.8125rem;
    }
    .text-group_1-3 {
      border-radius: 0 0.625rem 0.625rem 0;
      position: relative;
      width: 23.125rem;
      height: 7.8125rem;
      display: flex;
      flex-direction: column;
      justify-content: flex-center;
      background: rgba(216, 216, 216, 0);
    }
    .text_12-3 {
      width: 16.875rem;
      height: 2.625rem;
      overflow-wrap: break-word;
      font-size: 1.5rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      text-align: left;
      white-space: nowrap;
      line-height: 2.625rem;
      margin: 1.25rem 0 0 2.5rem;
      color: rgba(34, 34, 34, 1);
    }
    .text_13-3 {
      width: 12.5rem;
      height: 2.0625rem;
      overflow-wrap: break-word;
      font-size: 1rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      text-align: left;
      white-space: nowrap;
      line-height: 2.0625rem;
      margin: 0.625rem 0 1.25rem 2.5rem;
      color: rgba(102, 102, 102, 1);
    }
    .image_3-3 {
      position: absolute;
      left: 23.0625rem;
      top: 1.25rem;
      width: 0.0625rem;
      height: 7.8125rem;
    }
  }
  .section_2 {
    background-color: rgba(34, 34, 34, 1);
    width: 100%;
    height: 12.5rem;
    display: flex;
    justify-content: center;
    align-items: center;
    .group_2 {
      width: 23.125rem;
      height: 12.5rem;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .image_5 {
      width: 1.25rem;
      height: 2.1875rem;
      margin: 0 2.4375rem 0 10.0625rem;
    }
    .box_31 {
      width: 65rem;
      height: 9.6875rem;
      margin: 2.5rem 0 0 4.375rem;
    }
    .text_14 {
      width: 15rem;
      height: 2.625rem;
      overflow-wrap: break-word;
      color: rgba(255, 255, 255, 1);
      font-size: 1.5rem;
      font-family: PingFangSC-Medium;
      font-weight: 500;
      text-align: left;
      white-space: nowrap;
    }
    .section_8s2 {
      background-color: rgba(255, 255, 255, 1);
      width: 5rem;
      height: 0.25rem;
      margin-top: 0.9375rem;
    }
    .text_15 {
      width: 65rem;
      height: 4.625rem;
      overflow-wrap: break-word;
      color: rgba(255, 255, 255, 1);
      font-size: 1rem;
      font-family: PingFangSC-Regular;
      font-weight: normal;
      text-align: left;
      display: block;
      margin-top: 1.25rem;
    }
    .image_6 {
      width: 1.25rem;
      height: 2.1875rem;
      margin: 0 10.0625rem 0 2.4375rem;
    }
  }
  .section_3 {
    width: 100%;
    height: 91.5rem;
    background: linear-gradient(180deg, #dfe9ff 0%, #f6f9ff 100%);
    display: flex;
    flex-direction: column;
    align-items: center;
    .section_3_text {
      width: 10rem;
      font-size: 1.875rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      color: #222222;
      margin-top: 4.375rem;
      text-align: center;
    }
    .section_3_xian {
      width: 3.125rem;
      height: 0.25rem;
      background: #0281e0;
      margin: 0.625rem 0;
    }
    .section_3_zimu {
      height: 1.75rem;
      font-size: 1rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #666666;
      line-height: 1.75rem;
      text-align: center;
    }
    .fang_bao {
      height: 2.9375rem;
      margin: 2.5rem 0 4.4375rem 0;
      display: flex;
      justify-content: center;
      .box_bao1 {
        height: 2.9375rem;
        display: flex;
        align-items: center;
        .box_df {
          width: 1.25rem;
          height: 1.25rem;
        }
        .box_de {
          height: 2.9375rem;
          font-size: 1.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #222222;
          line-height: 2.9375rem;
          margin-left: 0.9375rem;
        }
      }
      .box_bao2 {
        margin-left: 2.5rem;
        display: flex;
        align-items: center;
        .box_df1 {
          width: 1.25rem;
          height: 1.25rem;
        }
        .box_de1 {
          margin-left: 0.9375rem;
          height: 2.9375rem;
          font-size: 1.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #222222;
          line-height: 2.9375rem;
        }
      }
    }
    .section_3_xiang {
      width: 92.5rem;
      height: 35.8125rem;
      background-image: url('@/assets/images/diannaoxiang.png');
      background-size: 100% 100%;
    }
    .section_3_box {
      margin-top: 2.5rem;
      display: flex;
      width: 92.5rem;
      img {
        border-top-left-radius: 0.9375rem;
        border-top-right-radius: 0.9375rem;
      }
      .section_3_box_1 {
        width: 29.375rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding-bottom: 1.25rem;
        background-color: #ffffff;
        border-radius: 0.9375rem;
        .section_3_box_1_text {
          width: 23.125rem;
          height: 2.625rem;
          font-size: 1.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #000000;
          line-height: 2.625rem;
          margin: 1.875rem 0 0 1.25rem;
        }
        .section_3_box_1_text1 {
          width: 25.625rem;
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #666666;
          line-height: 2.0625rem;
          text-align: center;
        }
      }
      .section_3_box_2 {
        width: 29.375rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        margin: 0 2.1875rem;
        padding-bottom: 1.25rem;
        background-color: #ffffff;
        border-radius: 0.9375rem;
        .section_3_box_1_text {
          width: 15.625rem;
          height: 2.625rem;
          font-size: 1.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #000000;
          line-height: 2.625rem;
          margin: 1.875rem 0 0 1.25rem;
        }
        .section_3_box_1_text1 {
          width: 25.625rem;
          height: 4.125rem;
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #666666;
          line-height: 2.0625rem;
          text-align: center;
        }
      }
      .section_3_box_3 {
        width: 29.375rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding-bottom: 1.25rem;
        background-color: #ffffff;
        border-radius: 0.9375rem;
        .section_3_box_1_text {
          width: 15.625rem;
          height: 2.625rem;
          font-size: 1.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #000000;
          line-height: 2.625rem;
          margin: 1.875rem 0 0 1.25rem;
        }
        .section_3_box_1_text1 {
          width: 25.625rem;
          height: 4.125rem;
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #666666;
          line-height: 2.0625rem;
          text-align: center;
        }
      }
    }
  }
  .section_4 {
    width: 100%;
    height: 18.75rem;
    background-image: url('@/assets/images/bianxie.png');
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    .section_4_box {
      display: flex;
      flex-direction: column;
      align-items: center;
      .section_4_box1 {
        width: 34.375rem;
        height: 4.375rem;
        font-size: 2.5rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #ffffff;
        line-height: 4.375rem;
        margin-top: 4.25rem;
        margin-bottom: 1.25rem;
      }
      .section_4_box2 {
        width: 92.5rem;
        height: 5.25rem;
        font-size: 1.5rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #ffffff;
        line-height: 2.625rem;
        text-align: center;
      }
    }
  }
  .section_5 {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-bottom: 1.25rem;
    .section_5_box {
      display: flex;
      flex-direction: column;
      align-items: center;
      .section_5_box_text {
        width: 10rem;
        height: 3.5rem;
        font-size: 1.875rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #222222;
        line-height: 3.5rem;
        margin-top: 4.375rem;
        text-align: center;
      }
      .section_5_box_xian {
        width: 3.125rem;
        height: 0.25rem;
        background: #0281e0;
        margin: 0.625rem 0;
      }
      .section_5_box_zimu {
        height: 1.75rem;
        font-size: 1rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #666666;
        line-height: 1.75rem;
        text-align: center;
      }
    }
    .section_5_box_1 {
      width: 93.75rem;
      display: flex;
      margin-top: 2.5rem;
      .section_5_box_1_1 {
        width: 31.25rem;
        display: flex;
        flex-direction: column;
        .top {
          display: flex;
          align-items: center;
          font-size: 1.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #222222;
          line-height: 2.625rem;
          .section_5 {
            background-color: rgba(2, 129, 224, 1);
            border-radius: 0.125rem;
            width: 0.25rem;
            height: 1.25rem;
            margin-right: 0.9375rem;
          }
        }
        .bottom {
          width: 28.125rem;
          height: 4.625rem;
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #666666;
          line-height: 2.3125rem;
          margin-top: 0.9375rem;
          margin-left: 0.9375rem;
        }
      }
    }
    .section_5_box_2 {
      margin-top: 1.25rem;
      width: 93.75rem;
      border: 0.0625rem dashed #c7c7c7;
    }
    .section_5_box_3 {
      width: 93.75rem;
      display: flex;
      margin-top: 1.25rem;
      .left {
        width: 31.25rem;
        height: 8.1875rem;
        display: flex;
        flex-direction: column;
        .top {
          height: 2.625rem;
          font-size: 1.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #222222;
          line-height: 2.625rem;
          display: flex;
          align-items: center;
          .section_5 {
            background-color: rgba(2, 129, 224, 1);
            border-radius: 0.125rem;
            width: 0.25rem;
            height: 1.25rem;
            margin-right: 0.9375rem;
          }
        }
        .bottom {
          width: 28.125rem;
          height: 4.625rem;
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #666666;
          line-height: 2.3125rem;
          margin-left: 1.25rem;
          margin-top: 0.9375rem;
        }
      }
      .right {
        flex: 1;
        display: flex;
        flex-direction: column;
        .top {
          height: 2.625rem;
          font-size: 1.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #222222;
          line-height: 2.625rem;
          display: flex;
          align-items: center;
          .section_5 {
            background-color: rgba(2, 129, 224, 1);
            border-radius: 0.125rem;
            width: 0.25rem;
            height: 1.25rem;
            margin-right: 0.9375rem;
          }
        }
        .bottom {
          width: 59.375rem;
          height: 6.9375rem;
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #666666;
          line-height: 2.3125rem;
          margin-left: 1.25rem;
          margin-top: 0.9375rem;
        }
      }
    }
  }
  .section_6 {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    padding-bottom: 6.25rem;
    background-image: url('@/assets/images/shibg.png');
    background-size: 100% 100%;
    .section_6_top {
      display: flex;
      flex-direction: column;
      align-items: center;
      .section_6_top_text {
        width: 10rem;
        height: 3.5rem;
        font-size: 1.875rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #222222;
        line-height: 3.5rem;
        margin-top: 4.375rem;
        text-align: center;
      }
      .section_6_top_xian {
        width: 3.125rem;
        height: 0.25rem;
        background: #0281e0;
        margin: 0.625rem 0;
      }
      .section_6_top_zimu {
        height: 1.75rem;
        font-size: 1rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #666666;
        line-height: 1.75rem;
        text-align: center;
      }
    }
    .section_6_bottom {
      width: 82.5rem;
      display: flex;
      flex-wrap: wrap;
      margin-top: 2.5rem;
      .section_6_bottom_box_1 {
        width: 25rem;
        height: 15.625rem;
        background-image: url('@/assets/images/qiye.png');
        background-size: 100% 100%;
        position: relative;
        border-radius: 0.9375rem;
        .section_6_bottom_box_1_zhe {
          border-radius: 0.9375rem;
          position: absolute;
          display: flex;
          align-items: center;
          justify-content: center;
          background: rgba(0, 0, 0, 0.5);
          width: 25rem;
          height: 15.625rem;
          font-size: 1.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 2.625rem;
        }
      }
      .section_6_bottom_box_2 {
        width: 25rem;
        margin: 0 2.5rem 2.5rem 2.5rem;
        height: 15.625rem;
        background-image: url('@/assets/images/jicengs.png');
        background-size: 100% 100%;
        position: relative;
        border-radius: 0.9375rem;
        .section_6_bottom_box_2_zhe {
          border-radius: 0.9375rem;
          position: absolute;
          display: flex;
          align-items: center;
          justify-content: center;
          background: rgba(0, 0, 0, 0.5);
          width: 25rem;
          height: 15.625rem;
          font-size: 1.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 2.625rem;
        }
      }
      .section_6_bottom_box_3 {
        width: 25rem;
        height: 15.625rem;
        background-image: url('@/assets/images/zhihui.png');
        background-size: 100% 100%;
        position: relative;
        border-radius: 0.9375rem;
        .section_6_bottom_box_3_zhe {
          border-radius: 0.9375rem;
          position: absolute;
          display: flex;
          align-items: center;
          justify-content: center;
          background: rgba(0, 0, 0, 0.5);
          width: 25rem;
          height: 15.625rem;
          font-size: 1.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 2.625rem;
        }
      }
      .section_6_bottom_box_4 {
        width: 25rem;
        height: 15.625rem;
        background-image: url('@/assets/images/zhihuis.png');
        background-size: 100% 100%;
        position: relative;
        border-radius: 0.9375rem;
        .section_6_bottom_box_4_zhe {
          border-radius: 0.9375rem;
          position: absolute;
          display: flex;
          align-items: center;
          justify-content: center;
          background: rgba(0, 0, 0, 0.5);
          width: 25rem;
          height: 15.625rem;
          font-size: 1.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 2.625rem;
        }
      }
      .section_6_bottom_box_5 {
        width: 25rem;
        height: 15.625rem;
        background-image: url('@/assets/images/xuexiao.png');
        background-size: 100% 100%;
        position: relative;
        margin: 0 2.5rem 0 2.5rem;
        border-radius: 0.9375rem;
        .section_6_bottom_box_5_zhe {
          border-radius: 0.9375rem;
          position: absolute;
          display: flex;
          align-items: center;
          justify-content: center;
          background: rgba(0, 0, 0, 0.5);
          width: 25rem;
          height: 15.625rem;
          font-size: 1.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 2.625rem;
        }
      }
      .section_6_bottom_box_6 {
        width: 25rem;
        height: 15.625rem;
        background-image: url('@/assets/images/liansuo.png');
        background-size: 100% 100%;
        position: relative;
        border-radius: 0.9375rem;
        .section_6_bottom_box_6_zhe {
          border-radius: 0.9375rem;
          position: absolute;
          display: flex;
          align-items: center;
          justify-content: center;
          background: rgba(0, 0, 0, 0.5);
          width: 25rem;
          height: 15.625rem;
          font-size: 1.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 2.625rem;
        }
      }
    }
  }
  .section_7 {
    width: 100%;
    height: 31.25rem;
    background-image: url('@/assets/images/zidong.png');
    background-size: 100% 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    .section_7_box {
      width: 82.5rem;
      display: flex;
      flex-direction: column;
      .section_7_box_top {
        font-size: 2.5rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #ffffff;
      }
      .section_7_box_bottom {
        font-size: 1.5rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #ffffff;
        margin-top: 1.875rem;
      }
    }
  }
  .section_8 {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    background: #f8f8f8;
    padding-bottom: 5rem;
    .section_8_text {
      width: 10rem;
      height: 3.5rem;
      font-size: 1.875rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      color: #222222;
      line-height: 3.5rem;
      margin-top: 4.375rem;
      text-align: center;
    }
    .section_8_xian {
      width: 3.125rem;
      height: 0.25rem;
      background: #0281e0;
      margin: 0.625rem 0;
    }
    .section_8_zimu {
      height: 1.75rem;
      font-size: 1rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #666666;
      line-height: 1.75rem;
      text-align: center;
    }
    .section_8_box_s {
      margin-top: 2.5rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 92.5rem;
      height: 6.25rem;
      background: #ffffff;
      border-top-left-radius: 0.9375rem;
      border-top-right-radius: 0.9375rem;
      .section_8_box_1 {
        font-size: 1.5rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #222222;
        margin-left: 4.375rem;
      }
    }
    .section_8_box {
      margin-top: 2.5rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 92.5rem;
      height: 6.25rem;
      background: #ffffff;
      border-top-left-radius: 0.9375rem;
      border-top-right-radius: 0.9375rem;
      .section_8_box_1 {
        font-size: 1.5rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #222222;
        margin-left: 4.375rem;
      }
    }
    .section_7_box4_2 {
      border-top: 0.0625rem solid #c7c7c7;
      height: 13.75rem;
      width: 92.5rem;
      display: flex;
      flex-wrap: wrap;
      background: #ffffff;
      align-items: center;
      border-bottom-left-radius: 0.9375rem;
      border-bottom-right-radius: 0.9375rem;
      .box_1 {
        margin-top: 0.625rem;
        width: 23.125rem;
        height: 3.125rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        .text_1 {
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 1rem;
          color: #666666;
        }
        .text_2 {
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #222222;
          line-height: 1.375rem;
        }
      }
    }
    .section_7_box4_1 {
      border-top: 0.0625rem solid #c7c7c7;
      height: 14.375rem;
      width: 92.5rem;
      display: flex;
      flex-wrap: wrap;
      background: #ffffff;
      align-items: center;
      border-bottom-left-radius: 0.9375rem;
      border-bottom-right-radius: 0.9375rem;
      .box_1 {
        margin-top: 0.625rem;
        width: 23.125rem;
        height: 3.125rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        .text_1 {
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 1rem;
          color: #666666;
        }
        .text_2 {
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #222222;
          line-height: 1.375rem;
        }
      }
      .box_2 {
        display: flex;
        align-items: center;
        margin-left: 4.375rem;
        .text_1 {
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #666666;
        }
        .text_2 {
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #222222;
        }
      }
    }
  }
}
</style>
