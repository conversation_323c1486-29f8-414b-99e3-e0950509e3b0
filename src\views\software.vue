<script setup>
import myFooter from '@/components/myFooter.vue'
import myHeader from '@/components/myHeader.vue'
</script>

<template>
  <my-header />
  <div class="banner">
    <img src="@/assets/images/healthHeader.png" alt="" />
    <div class="banner-info">
      <div class="w">
        <p>知心，才放心</p>
        <span
          >被信任，是一种能力，更是责任与担当，卫来健康携手国内多家TOP医院，为用户提供安全、可信、可靠的数字健康解决方案。
        </span>
        <button>立即下载 &ensp; <svg-icon name="Right" color="#fff" /></button>
      </div>
    </div>
  </div>
  <div class="w">
    <div class="watch-info">
      <img src="@/assets/images/watchinfo.png" alt="" />
      <div class="info-right">
        <h5>与智者同行，与善者相伴</h5>
        <p>
          国内仅此一家，软硬件均通过NMPA（国家食品药品监督总局）、FDA（美国食品药品监督管理局）认证，更有多项国际顶级医学成果发表，国内多家顶级医疗机构临床验证，证实其测量数据的准确性与可靠性-华为穿戴；
        </p>
        <p>卫来健康携手华为Research，为用户提供可信赖的健康监测方案与卓越的健康管理服务。</p>
      </div>
    </div>
    <div class="watch-trait">
      <div class="trait-box">
        <div>
          <span>全天候体征监测</span>
          <p>
            工作、运动、睡眠一刻不停； <br />
            全天候健康监测，与您风雨与共。
          </p>
        </div>
      </div>
      <div class="trait-box">
        <div>
          <span>风险实时预警</span>
          <p>
            只有发现危险，才能解决风险，在 <br />
            正确的时候改善健康状况
          </p>
        </div>
      </div>
      <div class="trait-box">
        <div>
          <span>亲友健康数据共享</span>
          <p>亲友关怀，不仅有信息和语音，还有健康。</p>
        </div>
      </div>
    </div>
  </div>
  <div class="watch-list">
    <img src="@/assets/images/logo-bannner.png" alt="" />
    <div class="w">
      <div class="row">
        <h5>ECG检测</h5>
        <p>华为watch GT2Pro</p>
        <p>华为watch GT3Pro</p>
        <p>华为watch D</p>
      </div>
      <div class="row">
        <h5>房颤、早搏、睡眠呼吸暂停检测</h5>
        <p>华为穿戴全系列（儿童手表除外）</p>
      </div>
      <div class="row">
        <h5>血压检测</h5>
        <p>华为watch D</p>
      </div>
      <div class="row">
        <h5>跌倒检测</h5>
        <p>华为watch 3</p>
      </div>
    </div>
  </div>
  <my-footer />
</template>
<style lang="scss" scoped>
.banner {
  max-width: 2560px;
  width: 100%;
  position: relative;
  margin: 0 auto;
  margin-bottom: 50px;

  img {
    width: 100%;
    height: auto;
  }

  .banner-info {
    width: 100%;
    height: 100%;
    padding-top: 80px;
    position: absolute;
    left: 0;
    top: 0;
    color: #fff;
    display: flex;
    flex-direction: column;
    justify-content: center;
    font-family: 'Alibaba PuHuiTi 2.0';

    p {
      font-weight: 900;
      font-size: 48px;
      line-height: 67px;
      margin-bottom: 20px;
    }

    span {
      font-weight: 400;
      font-size: 16px;
      line-height: 25px;
    }

    button {
      width: 238px;
      height: 72px;
      background-color: #2f75d3;
      border: 0;
      border-radius: 5px;
      font-weight: 400;
      font-size: 18px;
      line-height: 34px;
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 80px;
      cursor: pointer;
    }
  }

  @media screen and (max-width: 1024px) {
    margin-bottom: 24px;
    overflow: hidden;

    img {
      height: 600px;
      width: auto;
      position: relative;
      left: calc(-594px + 50%);
    }

    .banner-info {
      display: flex;

      .w {
        width: 100vw;
        padding: 0 12%;
      }

      p {
        font-size: 32px;
        line-height: 48px;
      }

      span {
        font-size: 14px;
        line-height: 20px;

        br {
          display: none;
        }
      }

      button {
        width: 94px;
        height: 34px;
        font-size: 14px;
        margin-top: 40px;
      }
    }
  }

  @media screen and (max-width: 800px) {
    margin-bottom: 24px;
    overflow: hidden;

    img {
      height: 600px;
      width: auto;
      position: relative;
      left: calc(-594px + 50%);
    }

    .banner-info {
      display: flex;

      .w {
        width: 100vw;
        padding: 0 12%;
      }

      p {
        font-size: 18px;
        line-height: 24px;
      }

      span {
        font-size: 12px;
        line-height: 16px;

        br {
          display: none;
        }
      }

      button {
        width: 94px;
        height: 34px;
        font-size: 12px;
      }
    }
  }
}

.watch-info {
  display: flex;
  align-items: center;
  justify-content: space-between;

  img {
    width: 630px;
    height: 350px;
  }

  .info-right {
    width: 50%;

    h5 {
      font-family: 'Alibaba PuHuiTi 2.0';
      font-style: normal;
      font-weight: 700;
      font-size: 20px;
      color: #3b3f47;
    }

    p {
      padding-left: 5px;
      margin: 24px 0 20px;
      font-family: 'Alibaba PuHuiTi 2.0';
      font-style: normal;
      font-weight: 400;
      font-size: 16px;
      color: #63676f;

      &::before {
        content: '';
        position: absolute;
        left: -5px;
        top: 12px;
        display: block;
        width: 4px;
        height: 4px;
        border-radius: 50%;
        background-color: #abafb8;
        margin-right: 5px;
      }
    }
  }

  @media screen and (max-width: 1920px) and (min-width: 1024px) {
    img {
      width: 40%;
      height: auto;
    }

    h5 {
      font-size: 18px !important;
    }

    p {
      font-size: 14px !important;
    }
  }

  @media screen and (max-width: 1024px) {
    flex-wrap: wrap;
    justify-content: flex-start;

    img {
      width: 100% !important;
      height: auto !important;
    }

    .info-right {
      width: 100%;
    }

    h5 {
      margin-top: 10px;
      font-size: 16px !important;
    }

    p {
      font-size: 12px !important;
      margin: 8px 0 !important;
    }
  }
}

.watch-trait {
  display: flex;
  justify-content: space-between;
  margin: 80px 0;

  .trait-box {
    width: 440px;
    height: 380px;
    padding-top: 260px;
    text-align: center;

    span {
      font-family: 'Alibaba PuHuiTi 2.0';
      font-style: normal;
      font-weight: 500;
      font-size: 20px;
      color: #3b3f47;
    }

    p {
      font-family: 'Alibaba PuHuiTi 2.0';
      font-style: normal;
      font-weight: 400;
      font-size: 16px;
      line-height: 22px;
      letter-spacing: -0.3px;
      margin-top: 10px;
      color: #63676f;
      line-height: 26px;
    }

    &:nth-child(1) {
      background-image: url('@/assets/images/trait1.png');
      background-size: 100% 100%;
    }

    &:nth-child(2) {
      background-image: url('@/assets/images/trait2.png');
      background-size: 100% 100%;
    }

    &:nth-child(3) {
      background-image: url('@/assets/images/trait3.png');
      background-size: 100% 100%;
    }
  }

  @media screen and (max-width: 1024px) {
    margin: 16px 0 36px;
    flex-wrap: wrap;

    .trait-box {
      width: 100%;
      height: 91vw;
      padding-top: 60%;
      margin-bottom: 8px;
      div {
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
        span {
          font-size: 14px;
        }
        p {
          font-size: 12px;
        }
      }
    }
  }
}

.watch-list {
  width: 100%;
  max-width: 1920px;
  margin: 0 auto;
  position: relative;
  img {
    width: 100%;
    height: auto;
  }

  .w {
    position: absolute;
    // width: 1480px;
    top: 86px;
    // left: calc(50% - 740px);
    left: 0;
    right: 0;
    display: flex;
    color: #fff;
    justify-content: space-between;

    .row {
      // width: 25%;

      h5 {
        font-family: 'Alibaba PuHuiTi 2.0';
        font-style: normal;
        font-weight: 500;
        font-size: 20px;
        line-height: 28px;
        letter-spacing: -0.3px;
        color: #f7f9ff;
        margin-bottom: 24px;
      }

      p {
        font-family: 'Alibaba PuHuiTi 2.0';
        font-style: normal;
        font-weight: 400;
        font-size: 16px;
        line-height: 22px;
        letter-spacing: -0.3px;
        color: #ffffff;
        margin-bottom: 20px;
      }
    }
  }

  @media screen and (max-width: 1920px) and (min-width: 1024px) {
    .w {
      top: 35px;
    }
  }
  @media screen and (max-width: 1024px) {
    img {
      display: none;
    }
    width: 100%;
    height: 75vw;
    background-image: url('../assets/images/logo-bannner.png');
    background-repeat: no-repeat;
    background-position: 100% 100%;
    background-color: #2f75d3;
    background-size: 200% 60%;
    .w {
      // flex-wrap: wrap;
      flex-direction: column;
      justify-content: space-evenly;
      top: 0;
      height: 80%;
      .row {
        width: 100%;

        h5 {
          font-size: 14px;
          margin-bottom: 0;
        }

        p {
          display: inline-block;
          margin-right: 3.2vw;
          font-size: 12px;
          margin-bottom: 0;
        }
      }
    }
  }
}
</style>
