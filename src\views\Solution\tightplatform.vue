<template>
  <div class="main">
    <div class="section_1">
      <div class="section_1_box">
        <div class="section_1_box_top">紧密型医共体平台</div>
        <div class="section_1_box_bottom">整合推进区域医疗资源共享，加快推进医疗卫生信息建设</div>
      </div>
    </div>
    <div class="section_2">
      <div class="sectino_2_box">
        习总书记在十九大报告中，关于健康医疗方面明确指示，要加强基层医疗卫生服务体系和全科医生队伍的建设。这一条指示要求医改中应以强基层为重点，完善基层医疗服务体系。
      </div>
      <div class="sectino_2_box_1">
        <div class="sectino_2_box_1_1"></div>
      </div>
      <div class="sectino_2_box_2">
        <div class="sectino_2_box_2_1">建设方案</div>
      </div>
      <div class="sectino_2_box_3"></div>
    </div>
    <div class="section_3">
      <div class="section_3_box">
        <div class="section_3_box_1"></div>
      </div>
      <div class="section_3_box_1">
        <div class="section_3_box_1_1">信息交换与共享</div>
      </div>
      <div class="section_3_box_2">
        <div class="section_3_box_2_left"></div>
        <div class="section_3_box_2_right">
          <div class="section_3_box_2_right_1">统一标准数据结构</div>
          <div class="section_3_box_2_right_2">
            <img
              class="thumbnail_2"
              referrerpolicy="no-referrer"
              src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng4c0ce207b2017cdafc1752d2e19dfbeb250b0f878d04138cbbeb075d0b981781"
            />
            采用业务引擎接受任务，解释业务规则，做出业务数据交换。
          </div>
          <div class="section_3_box_2_right_3">
            <img
              class="thumbnail_2"
              referrerpolicy="no-referrer"
              src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng4c0ce207b2017cdafc1752d2e19dfbeb250b0f878d04138cbbeb075d0b981781"
            />
            医疗业务引擎分为诊断信息引擎、药品处方引擎、临床检验引擎、医学影像引擎、消毒保障引擎、双向转诊引擎、远程心电引擎等各类交换规则引擎。
          </div>
        </div>
      </div>
    </div>
    <div class="section_4">
      <div class="section_4_box1">
        <div class="section_4_box1_1"></div>
      </div>
      <div class="section_4_box2">
        <div class="section_4_box2_1">卓越的云架构</div>
      </div>
      <div class="section_4_box3">
        <div class="section_4_box3_left">
          <div class="section_4_box3_left_top">
            “互联网+”浪潮下，智慧医疗成为医疗信息化的最终目标。医疗智能化建设涉及内容众多，包括临床业务智能化、管理决策智能化、患者服务智能化、资源管理智能化、物流智能化、监管智能化等，无疑是医疗信息化未来的发展方向
          </div>
          <div class="section_4_box3_left_bottom">
            <div class="section_4_box3_left_bottom_left">
              <div class="section_4_box3_left_bottom_left_1">
                <img
                  class="thumbnail_2"
                  referrerpolicy="no-referrer"
                  src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng4c0ce207b2017cdafc1752d2e19dfbeb250b0f878d04138cbbeb075d0b981781"
                />
                高开发访问量
              </div>
              <div class="section_4_box3_left_bottom_left_1">
                <img
                  class="thumbnail_2"
                  referrerpolicy="no-referrer"
                  src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng4c0ce207b2017cdafc1752d2e19dfbeb250b0f878d04138cbbeb075d0b981781"
                />
                海量数据存储
              </div>
              <div class="section_4_box3_left_bottom_left_1">
                <img
                  class="thumbnail_2"
                  referrerpolicy="no-referrer"
                  src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng4c0ce207b2017cdafc1752d2e19dfbeb250b0f878d04138cbbeb075d0b981781"
                />
                数据一致性
              </div>
            </div>
            <div class="section_4_box3_left_bottom_right">
              <div class="section_4_box3_left_bottom_left_1">
                <img
                  class="thumbnail_2"
                  referrerpolicy="no-referrer"
                  src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng4c0ce207b2017cdafc1752d2e19dfbeb250b0f878d04138cbbeb075d0b981781"
                />
                高开发访问量
              </div>
              <div class="section_4_box3_left_bottom_left_1">
                <img
                  class="thumbnail_2"
                  referrerpolicy="no-referrer"
                  src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng4c0ce207b2017cdafc1752d2e19dfbeb250b0f878d04138cbbeb075d0b981781"
                />
                大型文件存储
              </div>
              <div class="section_4_box3_left_bottom_left_1">
                <img
                  class="thumbnail_2"
                  referrerpolicy="no-referrer"
                  src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng4c0ce207b2017cdafc1752d2e19dfbeb250b0f878d04138cbbeb075d0b981781"
                />
                数据一致性
              </div>
            </div>
          </div>
        </div>
        <div class="section_4_box3_right"></div>
      </div>
    </div>
    <div class="section_5">
      <div class="section_5_box1">
        <div class="section_5_box1_1"></div>
      </div>
      <div class="section_5_box2">
        <div class="section_5_box2_1">显著特点</div>
      </div>
      <div class="section_5_box3">
        <div class="section_5_box3_left"></div>
        <div class="section_5_box3_right">
          <div class="section_5_box3_right_top">
            <div class="box_35"></div>
            <div class="section_5_box3_right_top_1">双向转诊</div>
          </div>
          <div class="section_5_box3_right_bottom">
            <div class="section_5_box3_right_bottom_top">
              <span style="color: #4865ff">病人流转：</span>
              促使医疗资源合理分配，建立“小病在基层、大病进医院，康复回基层”的就医新格局。
            </div>
            <div class="section_5_box3_right_bottom_tbottom">
              <span style="color: #4865ff">资源流转：</span>
              实行临床检验及其他大型医疗设备检查资源共享，病人在基层预约后直接到主体医院检验检查。
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="section_6">
      <div class="section_6_box">
        <div class="section_6_box_left">
          <div class="section_6_box_left_top">
            <div class="box_38"></div>
            处方流转与药品流通
          </div>
          <div class="section_6_box_left_bottom">
            区域内处方电子化流转。
            必要药品直接申请、药企统一配送、主体医院配送慢病处方药，对用药采购情况、医药配送供应服务、物流信息进行整体的监督和管理。
          </div>
        </div>
        <div class="section_6_box_right"></div>
      </div>
    </div>
    <div class="section_7">
      <div class="section_7_box">
        <div class="section_7_box_left"></div>
        <div class="section_7_box_right">
          <div class="section_7_box_right_top">
            <div class="block_9"></div>
            <div class="section_7_box_right_top_box">远程会诊</div>
          </div>
          <div class="section_7_box_right_bottom">
            通过可穿戴设备进行基本信息采集，通过可视化设备进行远程问诊。
            基层患者通过远程会诊，享受到大医院的先进医疗服务，基层医生也可提升医疗水平。
          </div>
        </div>
      </div>
    </div>
    <div class="section_8">
      <div class="section_8_box">
        <div class="section_8_box_left">
          <div class="section_8_box_left_box1">
            <div class="box_1"></div>
            <div class="box_2">诊断协同</div>
          </div>
          <div class="section_8_box_left_box2">
            <div class="box1">
              <span class="sp1">样本协同：</span>基层医院采样，并进行生化检验，上级医院集中诊断并审核。
            </div>
            <div class="box1">
              <span class="sp1">影像协同：</span>基层医院拍片、初诊，上级医院集中协同阅片诊断并审核。
            </div>
            <div class="box1">
              <span class="sp1">超声协同：</span
              >基于互联网的实时视频通讯，实现交互式远程会诊，会诊专家实时指导临床医生超声操作手法，进行远程教育与远程质量控制。
            </div>
            <div class="box1">
              <span class="sp1">心电检查协同：</span
              >乡镇卫生院、社区医院进行心电图检查，县级医院进行心电图诊断，复杂病例进行远程心电的会诊，支持移动端诊断。
            </div>
          </div>
        </div>
        <div class="section_8_box_right"></div>
      </div>
    </div>
    <div class="section_9">
      <div class="section_9_box">
        <div class="section_9_box_left"></div>
        <div class="section_9_box_right">
          <div class="box1">
            <div class="sps"></div>
            <div class="spp">公共卫生平台</div>
          </div>
          <div class="box2">通过搭建或对接公共卫生服务平台，实现人类在不同年龄阶段的健康数据管理。</div>
        </div>
      </div>
    </div>
    <div class="section_10">
      <div class="section_10_box">
        <div class="section_10_box_left">
          <div class="section_10_box_left_box1">
            <div class="box_1"></div>
            <div class="box_2">服务监管</div>
          </div>
          <div class="section_10_box_left_box2">
            <div class="box_1">
              <span class="se">医疗服务监管：</span>
              医疗质量分析、患者负担分析、临床信息对比分析。
            </div>
            <div class="box_1">
              <span class="se">公共卫生监管：</span>
              疾病控制情况统计、区域疾病监测、居民生命周期统计。
            </div>
            <div class="box_1">
              <span class="se">药品采供监管：</span>
              从各医疗机构采集药品的生产、配送、使用机构以及患者的用药信息，统一处理以后进行分析。
            </div>
          </div>
        </div>
        <div class="section_10_box_right"></div>
      </div>
    </div>
    <div class="section_11">
      <div class="section_11_box1">
        <div class="section_11_box1_1"></div>
      </div>
      <div class="section_11_box2">
        <div class="section_11_box2_1">方案价值</div>
      </div>
      <div class="section_11_box3">
        <div class="section_11_box3_left">
          <div class="box_1">01</div>
        </div>
        <div class="section_11_box3_right">
          <div class="box1">加强重大疾病防治</div>
          <div class="box2">
            用数据做支撑，大力推进全民健康促进计划；通过建立监管/评价平台，提升医疗质量和服务水平；科学使用大数据分析，加强重大疾病防治。
          </div>
        </div>
      </div>
      <div class="section_11_box4">
        <div class="section_11_box3_left">
          <div class="box_1">02</div>
        </div>
        <div class="section_11_box3_right">
          <div class="box1">提升卫生服务应急能力</div>
          <div class="box2">
            通过数据精细化分析，科学建立健全综合管理制度；通过科学数据监测分析，加强健康影响因素评价；建立应急指挥平台，提升卫生服务应急能力。
          </div>
        </div>
      </div>
      <div class="section_11_box5">
        <div class="section_11_box3_left">
          <div class="box_1">03</div>
        </div>
        <div class="section_11_box3_right">
          <div class="box1">推进分级诊疗制度落实</div>
          <div class="box2">
            用通过县域云平台监管能力建设，实施药品供应保障监管；通过医联/共体平台建设，推进分级诊疗制度落实。
          </div>
        </div>
      </div>
      <div class="section_11_box6">
        <div class="section_11_box3_left">
          <div class="box_1">04</div>
        </div>
        <div class="section_11_box3_right">
          <div class="box1">加快健康产业发展</div>
          <div class="box2">
            建立健康扶贫信息系统，深入实施健康扶贫工程；建立统一门户/医教协同，加强人才队伍建设；建立统一门户/公众号/APP
            , 加强宣传引导教育；通过云平台信息整合，加快推进健康产业发展；科学精准分析医疗数据，加快推进医疗科技创新。
          </div>
        </div>
      </div>
      <div class="section_11_box7">
        <div class="section_11_box3_left">
          <div class="box_1">05</div>
        </div>
        <div class="section_11_box3_right">
          <div class="box1">传承发展中医药事业</div>
          <div class="box2">
            建立统一医风评价系统，加强卫生计生行风建设；科学建立绩效评价系统
            ，调动医务人员积极性主动性；建立中医治未病系统，传承发展中医药事业。
          </div>
        </div>
      </div>
      <div class="section_11_box8">
        <div class="section_11_box3_left">
          <div class="box_1">06</div>
        </div>
        <div class="section_11_box3_right">
          <div class="box1">实现卫生计生服务均等化</div>
          <div class="box2">
            建立妇幼健康保障系统，全面改善妇幼健康服务能力；建立失独家庭信息档案，监管失独家庭关心关爱工作；通过医共体平台技术支撑，提升基层卫生服务能力；建立流动人口医疗保障卡，实现卫生计生服务均等化。
          </div>
        </div>
      </div>
    </div>
    <div class="section_12">
      <div class="section_9_box1">
        我们的客户评价
        <div class="zhe"></div>
      </div>
      <div class="section_9_box2">
        <div class="section_9_box2_1">
          <img src="@/assets/images/wenwen.png" alt="" class="hujis" />
          <div class="box_1">“平台带来的数字化管理帮我们节省了大量人力”</div>
          <div class="box_2">
            <div class="box_2_left"></div>
            <div class="box_2_right">
              <span class="box1">某医院</span>
              <span class="box2">某院长</span>
            </div>
          </div>
        </div>
        <div class="section_9_box2_3">
          <img src="@/assets/images/tanhua.png" alt="" class="hujis" />
          <div class="box_1">“平台化繁为简，帮助医院释放服务效能”</div>
          <div class="box_2">
            <div class="box_2_left"></div>
            <div class="box_2_right">
              <span class="box1">某医院</span>
              <span class="box2">主任医师</span>
            </div>
          </div>
        </div>
        <div class="section_9_box2_4">
          <img src="@/assets/images/kaihui.png" alt="" class="hujis" />
          <div class="box_1">“平台帮助我们使复杂的工作流程变得简单，对我们帮助很大”</div>
          <div class="box_2">
            <div class="box_2_left"></div>
            <div class="box_2_right">
              <span class="box1">某医院</span>
              <span class="box2">门诊医生</span>
            </div>
          </div>
        </div>
        <div class="section_9_box2_2">
          <img src="@/assets/images/tingzhens.png" alt="" class="hujis" />
          <div class="box_1">“使用该平台将我们的检查时间提升了20%”</div>
          <div class="box_2">
            <div class="box_2_left"></div>
            <div class="box_2_right">
              <span class="box1">某医院</span>
              <span class="box2">体检医生</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.main {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  max-width: 160rem;
  overflow: hidden;
  margin: 0 auto;
  margin-top: 3.5rem;
  .section_1 {
    width: 100%;
    max-width: 160rem;
    height: 37.5rem;
    background-image: url('@/assets/images/jinmi.png');
    background-size: 100% 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    .section_1_box {
      display: flex;
      width: 92.5rem;
      flex-direction: column;
      .section_1_box_top {
        font-size: 2.25rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #222222;
      }
      .section_1_box_bottom {
        margin-top: 1.25rem;
        font-size: 1.25rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #666666;
      }
    }
  }
  .section_2 {
    width: 100%;
    height: 78.0625rem;
    max-width: 160rem;
    background: #ffffff;
    display: flex;
    flex-direction: column;
    align-items: center;
    .sectino_2_box {
      width: 92.5rem;
      font-size: 1.5rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #666666;
      margin-top: 4.25rem;
    }
    .sectino_2_box_1 {
      width: 92.5rem;
      margin-top: 4.375rem;
      .sectino_2_box_1_1 {
        width: 7.5rem;
        height: 0.375rem;
        background: linear-gradient(90deg, #4865ff 0%, rgba(255, 255, 255, 0) 100%);
        border-radius: 0.1875rem;
      }
    }
    .sectino_2_box_2 {
      width: 92.5rem;
      margin-top: 0.625rem;
      .sectino_2_box_2_1 {
        width: 7.5rem;
        height: 3.5rem;
        font-size: 1.875rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #222222;
      }
    }
    .sectino_2_box_3 {
      margin-top: 2.5rem;
      width: 92.5rem;
      height: 52.25rem;
      background-image: url('@/assets/images/jianshe.png');
      background-size: 100% 100%;
    }
  }
  .section_3 {
    width: 100%;
    max-width: 160rem;
    height: 44.5rem;
    background: rgba(255, 255, 255, 0);
    display: flex;
    flex-direction: column;
    align-items: center;
    .section_3_box {
      width: 92.5rem;
      margin-top: 4.375rem;
      .section_3_box_1 {
        width: 13.125rem;
        height: 0.375rem;
        background: linear-gradient(90deg, #4865ff 0%, rgba(255, 255, 255, 0) 100%);
        border-radius: 0.1875rem;
      }
    }
    .section_3_box_1 {
      width: 92.5rem;
      margin-bottom: 2.25rem;
      .section_3_box_1_1 {
        width: 13.125rem;
        font-size: 1.875rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #222222;
      }
    }
    .section_3_box_2 {
      width: 92.5rem;
      display: flex;
      .section_3_box_2_left {
        width: 50rem;
        height: 28.75rem;
        background-image: url('@/assets/images/sanjia.png');
        background-size: 100% 100%;
      }
      .section_3_box_2_right {
        display: flex;
        flex-direction: column;
        margin-left: 2.5rem;
        justify-content: center;
        .section_3_box_2_right_1 {
          font-size: 1.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #222222;
          line-height: 2.3125rem;
        }
        .section_3_box_2_right_2 {
          margin-top: 1.875rem;
          height: 4.625rem;
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #666666;
          line-height: 2.3125rem;
          display: flex;
          align-items: center;
          .thumbnail_2 {
            margin-right: 0.9375rem;
          }
        }
        .section_3_box_2_right_3 {
          display: flex;
          align-items: flex-start;
          margin-top: 1.25rem;
          width: 37.8125rem;
          height: 6.9375rem;
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #666666;
          line-height: 2.3125rem;
          .thumbnail_2 {
            margin-right: 0.9375rem;
            margin-top: 0.625rem;
          }
        }
      }
    }
  }
  .section_4 {
    display: flex;
    max-width: 160rem;
    flex-direction: column;
    align-items: center;
    width: 100%;
    margin-top: 4.375rem;
    padding-bottom: 5rem;
    .section_4_box1 {
      width: 92.5rem;
      margin-top: 4.375rem;
      .section_4_box1_1 {
        width: 11.25rem;
        height: 0.375rem;
        background: linear-gradient(90deg, #4865ff 0%, rgba(255, 255, 255, 0) 100%);
        border-radius: 0.1875rem;
      }
    }
    .section_4_box2 {
      width: 92.5rem;
      margin-top: 0.625rem;
      margin-bottom: 2.5rem;
      .section_4_box2_1 {
        width: 11.25rem;
        height: 3.5rem;
        font-size: 1.875rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #222222;
        line-height: 3.5rem;
      }
    }
    .section_4_box3 {
      width: 92.5rem;
      display: flex;
      .section_4_box3_left {
        width: 50rem;
        display: flex;
        flex-direction: column;
        .section_4_box3_left_top {
          margin-top: 4.125rem;
          width: 40rem;
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #666666;
        }
        .section_4_box3_left_bottom {
          display: flex;
          margin-top: 1.875rem;
          .section_4_box3_left_bottom_left {
            display: flex;
            flex-direction: column;
            .section_4_box3_left_bottom_left_1 {
              display: flex;
              align-items: flex-start;
              font-size: 1.5rem;
              font-family: PingFangSC, PingFang SC;
              font-weight: 500;
              color: #4865ff;
              margin-bottom: 1.25rem;
              .thumbnail_2 {
                margin-top: 0.5rem;
                margin-right: 0.9375rem;
              }
            }
          }
          .section_4_box3_left_bottom_right {
            display: flex;
            flex-direction: column;
            margin-left: 4.375rem;
            .section_4_box3_left_bottom_left_1 {
              display: flex;
              align-items: flex-start;
              font-size: 1.5rem;
              font-family: PingFangSC, PingFang SC;
              font-weight: 500;
              color: #4865ff;
              margin-bottom: 1.25rem;
              .thumbnail_2 {
                margin-top: 0.5rem;
                margin-right: 0.9375rem;
              }
            }
          }
        }
      }
      .section_4_box3_right {
        width: 50rem;
        height: 28.75rem;
        background-image: url('@/assets/images/duoge.png');
        background-size: 100% 100%;
      }
    }
  }
  .section_5 {
    width: 100%;
    max-width: 160rem;
    height: 40.75rem;
    background: #ffffff;
    display: flex;
    flex-direction: column;
    align-items: center;
    .section_5_box1 {
      margin-top: 4.375rem;
      width: 92.5rem;
      .section_5_box1_1 {
        width: 7.5rem;
        height: 0.375rem;
        background: linear-gradient(90deg, #4865ff 0%, rgba(255, 255, 255, 0) 100%);
        border-radius: 0.1875rem;
      }
    }
    .section_5_box2 {
      width: 92.5rem;
      margin-top: 0.625rem;
      margin-bottom: 2.5rem;
      .section_5_box2_1 {
        width: 7.5rem;
        font-size: 1.875rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #222222;
      }
    }
    .section_5_box3 {
      width: 92.5rem;
      height: 25rem;
      display: flex;
      .section_5_box3_left {
        width: 37.5rem;
        height: 25rem;
        background-image: url('@/assets/images/xianzhu.png');
        background-size: 100% 100%;
      }
      .section_5_box3_right {
        flex: 1;
        margin-left: 4.375rem;
        display: flex;
        flex-direction: column;
        margin-top: 5.9375rem;
        .section_5_box3_right_top {
          display: flex;
          align-items: center;
          .section_5_box3_right_top_1 {
            font-size: 1.5rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #222222;
          }
          .box_35 {
            background-color: rgba(2, 129, 224, 1);
            border-radius: 0.125rem;
            width: 0.25rem;
            height: 1.25rem;
            margin-right: 0.9375rem;
          }
        }
        .section_5_box3_right_bottom {
          display: flex;
          flex-direction: column;
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          line-height: 2.3125rem;
          margin-left: 0.9375rem;
          margin-top: 1.25rem;
          .section_5_box3_right_bottom_top {
            color: #666666;
          }
          .section_5_box3_right_bottom_tbottom {
            color: #666666;
          }
        }
      }
    }
  }
  .section_6 {
    width: 100%;
    max-width: 160rem;
    height: 33.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #ffffff;
    .section_6_box {
      width: 92.5rem;
      height: 25rem;
      display: flex;
      .section_6_box_left {
        flex: 1;
        display: flex;
        flex-direction: column;
        .section_6_box_left_top {
          margin-top: 7.125rem;
          height: 2.625rem;
          font-size: 1.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #222222;
          line-height: 2.625rem;
          display: flex;
          align-items: center;
          .box_38 {
            background-color: rgba(2, 129, 224, 1);
            border-radius: 0.125rem;
            width: 0.25rem;
            height: 1.25rem;
            margin-right: 0.9375rem;
          }
        }
        .section_6_box_left_bottom {
          margin-top: 1.25rem;
          height: 6.9375rem;
          font-size: 1remx;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #666666;
          margin-left: 0.9375rem;
          line-height: 2.3125rem;
        }
      }
      .section_6_box_right {
        width: 37.5rem;
        height: 25rem;
        background-image: url('@/assets/images/erweima.png');
        background-size: 100% 100%;
      }
    }
  }
  .section_7 {
    width: 100%;
    max-width: 160rem;
    height: 33.75rem;
    background: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    .section_7_box {
      display: flex;
      width: 92.5rem;
      height: 25rem;
      .section_7_box_left {
        width: 37.5rem;
        height: 25rem;
        background-image: url('@/assets/images/shipinhuiyi.png');
        background-size: 100% 100%;
      }
      .section_7_box_right {
        flex: 1;
        .section_7_box_right_top {
          margin-left: 4.375rem;
          margin-top: 7.125rem;
          display: flex;
          align-items: center;
          .section_7_box_right_top_box {
            width: 7.5rem;
            font-size: 1.5rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #222222;
          }
          .block_9 {
            background-color: rgba(2, 129, 224, 1);
            border-radius: 0.125rem;
            width: 0.25rem;
            height: 1.25rem;
            margin: 0 0.9375rem 0 0;
          }
        }
        .section_7_box_right_bottom {
          margin-left: 5.5625rem;
          margin-top: 1.25rem;
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #666666;
        }
      }
    }
  }
  .section_8 {
    max-width: 160rem;
    width: 100%;
    height: 33.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #ffffff;
    .section_8_box {
      width: 92.5rem;
      height: 25rem;
      display: flex;
      .section_8_box_left {
        flex: 1;
        display: flex;
        flex-direction: column;
        .section_8_box_left_box1 {
          display: flex;
          align-items: center;
          .box_1 {
            width: 0.25rem;
            height: 1.25rem;
            background: #0281e0;
            border-radius: 0.125rem;
            margin-right: 0.9375rem;
          }
          .box_2 {
            width: 7.5rem;
            height: 2.625rem;
            font-size: 1.5rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #222222;
            line-height: 2.625rem;
          }
        }
        .section_8_box_left_box2 {
          margin-top: 1.25rem;
          margin-left: 0.9375rem;
          height: 13.875rem;
          font-size: 1.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          display: flex;
          flex-direction: column;
          .sp1 {
            color: #4865ff;
          }
          .box1 {
            color: #666666;
            font-size: 1rem;
          }
        }
      }
      .section_8_box_right {
        width: 37.5rem;
        height: 25rem;
        background-image: url('@/assets/images/situ.png');
        background-size: 100% 100%;
      }
    }
  }
  .section_9 {
    width: 100%;
    max-width: 160rem;
    height: 33.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background-image: url(https://lanhu-dds-backend.oss-cn-beijing.aliyuncs.com/merge_image/imgs/05db9e40a49e4b2d9370c46d4d2a46f7_mergeImage.png);
    background-size: 100% 100%;
    .section_9_box {
      width: 80.5625rem;
      height: 25rem;
      display: flex;
      .section_9_box_left {
        width: 37.5rem;
        height: 25rem;
        background-image: url('@/assets/images/yaofang.png');
        background-size: 100% 100%;
      }
      .section_9_box_right {
        display: flex;
        flex-direction: column;
        margin-top: 8.25rem;
        .box1 {
          display: flex;
          align-items: center;
          .sps {
            width: 0.25rem;
            height: 1.25rem;
            background: #0281e0;
            border-radius: 0.125rem;
            margin-right: 0.9375rem;
          }
          .spp {
            height: 2.625rem;
            font-size: 1.5rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #222222;
            line-height: 2.625rem;
          }
        }
        .box2 {
          height: 4.625rem;
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #666666;
          line-height: 2.3125rem;
          margin-top: 1.25rem;
          margin-left: 0.9375rem;
        }
      }
    }
  }
  .section_10 {
    width: 100%;
    max-width: 160rem;
    height: 33.75rem;
    background-image: url(https://lanhu-dds-backend.oss-cn-beijing.aliyuncs.com/merge_image/imgs/7b65a7177f0343eaa4af0e23fe9dd667_mergeImage.png);
    background-size: 100% 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    .section_10_box {
      width: 80.5625rem;
      height: 25rem;
      display: flex;
      .section_10_box_left {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        .section_10_box_left_box1 {
          display: flex;
          align-items: center;
          .box_1 {
            width: 0.25rem;
            height: 1.25rem;
            background: #0281e0;
            border-radius: 0.125rem;
            margin-right: 0.9375rem;
          }
          .box_2 {
            width: 7.5rem;
            height: 2.625rem;
            font-size: 1.5rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #222222;
            line-height: 2.625rem;
          }
        }
        .section_10_box_left_box2 {
          margin-top: 1.25rem;
          margin-left: 1.1875rem;
          width: 48.75rem;
          height: 9.25rem;
          display: flex;
          flex-direction: column;
          .box_1 {
            width: 48.75rem;
            height: 9.25rem;
            font-size: 1rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            line-height: 2.3125rem;
            color: #666666;
            .se {
              color: #4865ff;
            }
          }
        }
      }
      .section_10_box_right {
        width: 37.5rem;
        height: 25rem;
        background-image: url('@/assets/images/duidun.png');
        background-size: 100% 100%;
      }
    }
  }
  .section_11 {
    width: 100%;
    background: #ffffff;
    max-width: 160rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 3.125rem;
    padding-bottom: 3.125rem;
    .section_11_box1 {
      margin-top: 4.375rem;
      width: 92.5rem;
      .section_11_box1_1 {
        width: 7.5rem;
        height: 0.375rem;
        background: linear-gradient(90deg, #4865ff 0%, rgba(255, 255, 255, 0) 100%);
        border-radius: 0.1875rem;
      }
    }
    .section_11_box2 {
      width: 92.5rem;
      margin-top: 0.625rem;
      margin-bottom: 1.9375rem;
      .section_11_box2_1 {
        width: 7.5rem;
        height: 3.5rem;
        font-size: 1.875rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #222222;
        line-height: 3.5rem;
      }
    }
    .section_11_box3 {
      width: 92.5rem;
      display: flex;
      .section_11_box3_left {
        display: flex;
        align-items: center;
        justify-content: center;
        background-image: url('@/assets/images/fangbian.png');
        background-size: 100% 100%;
        width: 4.375rem;
        height: 4.375rem;
        .box_1 {
          width: 3.5625rem;
          height: 4.375rem;
          font-size: 2.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          margin: 0.25rem 0 0 0.625rem;
        }
      }
      .section_11_box3_right {
        display: flex;
        flex-direction: column;
        margin-left: 1.25rem;
        .box1 {
          width: 15rem;
          height: 2.625rem;
          font-size: 1.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #222222;
          line-height: 2.625rem;
        }
        .box2 {
          width: 85rem;
          height: 4.125rem;
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #666666;
          line-height: 2.0625rem;
          margin-top: 0.625rem;
        }
      }
    }
    .section_11_box4 {
      width: 92.5rem;
      display: flex;
      .section_11_box3_left {
        display: flex;
        align-items: center;
        justify-content: center;
        background-image: url('@/assets/images/fangbian.png');
        background-size: 100% 100%;
        width: 4.375rem;
        height: 4.375rem;
        .box_1 {
          width: 3.5625rem;
          height: 4.375rem;
          font-size: 2.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          margin: 0.25rem 0 0 0.625rem;
        }
      }
      .section_11_box3_right {
        display: flex;
        flex-direction: column;
        margin-left: 1.25rem;
        .box1 {
          width: 15rem;
          height: 2.625rem;
          font-size: 1.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #222222;
          line-height: 2.625rem;
        }
        .box2 {
          width: 85rem;
          height: 4.125rem;
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #666666;
          line-height: 2.0625rem;
          margin-top: 0.625rem;
        }
      }
    }
    .section_11_box5 {
      width: 92.5rem;
      display: flex;
      .section_11_box3_left {
        display: flex;
        align-items: center;
        justify-content: center;
        background-image: url('@/assets/images/fangbian.png');
        background-size: 100% 100%;
        width: 4.375rem;
        height: 4.375rem;
        .box_1 {
          width: 3.5625rem;
          height: 4.375rem;
          font-size: 2.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          margin: 0.25rem 0 0 0.625rem;
        }
      }
      .section_11_box3_right {
        display: flex;
        flex-direction: column;
        margin-left: 1.25rem;
        .box1 {
          width: 15rem;
          height: 2.625rem;
          font-size: 1.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #222222;
          line-height: 2.625rem;
        }
        .box2 {
          width: 85rem;
          height: 4.125rem;
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #666666;
          line-height: 2.0625rem;
          margin-top: 0.625rem;
        }
      }
    }
    .section_11_box6 {
      width: 92.5rem;
      display: flex;
      margin-bottom: 1.875rem;
      .section_11_box3_left {
        display: flex;
        align-items: center;
        justify-content: center;
        background-image: url('@/assets/images/fangbian.png');
        background-size: 100% 100%;
        width: 4.375rem;
        height: 4.375rem;
        .box_1 {
          width: 3.5625rem;
          height: 4.375rem;
          font-size: 2.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          margin: 0.25rem 0 0 0.625rem;
        }
      }
      .section_11_box3_right {
        display: flex;
        flex-direction: column;
        margin-left: 1.25rem;
        .box1 {
          width: 15rem;
          height: 2.625rem;
          font-size: 1.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #222222;
          line-height: 2.625rem;
        }
        .box2 {
          width: 85rem;
          height: 4.125rem;
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #666666;
          line-height: 2.0625rem;
          margin-top: 0.625rem;
        }
      }
    }
    .section_11_box7 {
      width: 92.5rem;
      display: flex;
      .section_11_box3_left {
        display: flex;
        align-items: center;
        justify-content: center;
        background-image: url('@/assets/images/fangbian.png');
        background-size: 100% 100%;
        width: 4.375rem;
        height: 4.375rem;
        .box_1 {
          width: 3.5625rem;
          height: 4.375rem;
          font-size: 2.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          margin: 0.25rem 0 0 0.625rem;
        }
      }
      .section_11_box3_right {
        display: flex;
        flex-direction: column;
        margin-left: 1.25rem;
        .box1 {
          width: 15rem;
          height: 2.625rem;
          font-size: 1.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #222222;
          line-height: 2.625rem;
        }
        .box2 {
          width: 85rem;
          height: 4.125rem;
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #666666;
          line-height: 2.0625rem;
          margin-top: 0.625rem;
        }
      }
    }
    .section_11_box8 {
      width: 92.5rem;
      display: flex;
      .section_11_box3_left {
        display: flex;
        align-items: center;
        justify-content: center;
        background-image: url('@/assets/images/fangbian.png');
        background-size: 100% 100%;
        width: 4.375rem;
        height: 4.375rem;
        .box_1 {
          width: 3.5625rem;
          height: 4.375rem;
          font-size: 2.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          margin: 0.25rem 0 0 0.625rem;
        }
      }
      .section_11_box3_right {
        display: flex;
        flex-direction: column;
        margin-left: 1.25rem;
        .box1 {
          width: 16.875rem;
          height: 2.625rem;
          font-size: 1.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #222222;
          line-height: 2.625rem;
        }
        .box2 {
          width: 85rem;
          height: 4.125rem;
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #666666;
          line-height: 2.0625rem;
          margin-top: 0.625rem;
        }
      }
    }
  }
  .section_12 {
    width: 100%;
    background-color: #f8f8f8;
    max-width: 160rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-bottom: 3.125rem;
    .section_9_box1 {
      font-size: 1.875rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      color: #222222;
      line-height: 2.8125rem;
      text-align: center;
      position: relative;
      margin-bottom: 3.125rem;
      .zhe {
        position: absolute;
        bottom: 0;
        width: 13.125rem;
        height: 0.75rem;
        background: linear-gradient(90deg, rgba(72, 101, 255, 0.1) 0%, #4865ff 49%, rgba(72, 101, 255, 0.1) 100%);
      }
    }
    .section_9_box2 {
      width: 82.5rem;
      display: flex;
      justify-content: center;
      border-radius: 0.9375rem;
      .hujis {
        border-top-left-radius: 0.9375rem;
        border-top-right-radius: 0.9375rem;
        width: 18.75rem;
        height: 11.25rem;
      }
      .section_9_box2_1 {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-right: 2.5rem;
        width: 18.75rem;
        background: #ffffff;
        box-shadow: inset 0 0 0.4375rem 0 rgba(255, 255, 255, 0.5);
        border-radius: 0.9375rem;
        border: 0.0625rem solid #dedede;
        padding-bottom: 1.25rem;
        .box_1 {
          width: 80%;
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #222222;
          margin-top: 0.9375rem;
        }
        .box_2 {
          display: flex;
          margin-top: 1.25rem;
          .box_2_left {
            width: 3.125rem;
            height: 3.125rem;
            background-image: url('@/assets/images/rentou.png');
            background-size: 100% 100%;
          }
          .box_2_right {
            display: flex;
            flex-direction: column;
            margin-left: 0.9375rem;
            .box1 {
              width: 3rem;
              font-size: 1rem;
              font-family: PingFangSC, PingFang SC;
              font-weight: 500;
              color: #222222;
            }
            .box2 {
              width: 3rem;
              font-size: 1rem;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #666666;
            }
          }
        }
      }
      .section_9_box2_2 {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 18.75rem;
        background: #ffffff;
        box-shadow: inset 0 0 0.4375rem 0 rgba(255, 255, 255, 0.5);
        border-radius: 0.9375rem;
        border: 0.0625rem solid #dedede;
        padding-bottom: 1.25rem;
        .box_1 {
          width: 80%;
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #222222;
          margin-top: 0.9375rem;
        }
        .box_2 {
          width: 8.0625rem;
          display: flex;
          margin-top: 1.25rem;
          .box_2_left {
            width: 3.125rem;
            height: 3.125rem;
            background-image: url('@/assets/images/rentou.png');
            background-size: 100% 100%;
          }
          .box_2_right {
            display: flex;
            flex-direction: column;
            margin-left: 1.25rem;
            .box1 {
              width: 3rem;
              font-size: 1rem;
              font-family: PingFangSC, PingFang SC;
              font-weight: 500;
              color: #222222;
            }
            .box2 {
              width: 4rem;
              font-size: 1rem;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #666666;
            }
          }
        }
      }
      .section_9_box2_3 {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-right: 2.5rem;
        width: 18.75rem;
        background: #ffffff;
        box-shadow: inset 0 0 0.4375rem 0 rgba(255, 255, 255, 0.5);
        border-radius: 0.9375rem;
        border: 0.0625rem solid #dedede;
        padding-bottom: 1.25rem;
        .box_1 {
          width: 80%;
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #222222;
          margin-top: 0.9375rem;
        }
        .box_2 {
          width: 8.0625rem;
          display: flex;
          margin-top: 1.25rem;
          .box_2_left {
            width: 3.125rem;
            height: 3.125rem;
            background-image: url('@/assets/images/rentou.png');
            background-size: 100% 100%;
          }
          .box_2_right {
            display: flex;
            flex-direction: column;
            margin-left: 0.9375rem;
            .box1 {
              width: 3rem;
              font-size: 1rem;
              font-family: PingFangSC, PingFang SC;
              font-weight: 500;
              color: #222222;
            }
            .box2 {
              width: 4rem;
              font-size: 1rem;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #666666;
            }
          }
        }
      }
      .section_9_box2_4 {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-right: 2.5rem;
        width: 18.75rem;
        background: #ffffff;
        box-shadow: inset 0 0 0.4375rem 0 rgba(255, 255, 255, 0.5);
        border-radius: 0.9375rem;
        border: 0.0625rem solid #dedede;
        padding-bottom: 1.25rem;
        .box_1 {
          width: 80%;
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #222222;
          margin-top: 0.9375rem;
        }
        .box_2 {
          width: 8.0625rem;
          display: flex;
          margin-top: 1.25rem;
          .box_2_left {
            width: 3.125rem;
            height: 3.125rem;
            background-image: url('@/assets/images/rentou.png');
            background-size: 100% 100%;
          }
          .box_2_right {
            display: flex;
            flex-direction: column;
            margin-left: 0.9375rem;
            .box1 {
              width: 3rem;
              font-size: 1rem;
              font-family: PingFangSC, PingFang SC;
              font-weight: 500;
              color: #222222;
            }
            .box2 {
              width: 4rem;
              font-size: 1rem;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #666666;
            }
          }
        }
      }
    }
  }
}
</style>
