<script setup>
import { ref } from 'vue'
const isTrue = ref('0')
const handofUn = val => {
  isTrue.value = val
}
</script>
<template>
  <div class="image-main">
    <div class="mains">
      <div class="box1">
        <div class="box1_1">基层医防融合一体化服务</div>
        <div class="box1_2">用最小的学习成本实现最大的效率提升，万物智能互联，将一切化繁为简</div>
      </div>
    </div>
    <div class="section_1">
      <div class="section_1_box1">
        卫软基层医防融合一体化服务可提高基层医疗机构的服务水平为患者提供更加便捷高效的医疗服务，有利于提高基层医疗机构的信息化智能化水平推动基层医疗服务数字化、智能化、标准化发展。
      </div>
      <div class="section_1_box2">
        <div class="box_1"></div>
      </div>
      <div class="section_1_box3">
        <div class="box_1">云计算+大数据技术</div>
      </div>
      <div class="section_1_box4"></div>
    </div>
    <div class="section_2">
      <div class="section_2_box">
        <div class="box_1"></div>
      </div>
      <div class="section_2_box_1">
        <div class="box_1">智能硬件设备+平台服务</div>
      </div>
      <div class="section_2_box_2">
        <div class="box_1">
          <div class="box_1_1">AI智能引擎</div>
          <div class="box_1_2">提供语音识别、语义理解、图片内容识别等服务。</div>
          <div class="box_1_3"></div>
        </div>
        <div class="box_2">
          <div class="box_2_1">智能画像分析</div>
          <div class="box_2_2">支持慢性病人群智能筛选，构建患者慢病画像。</div>
          <div class="box_2_3"></div>
        </div>
        <div class="box_3">
          <div class="box_2_1">智能监测预警</div>
          <div class="box_2_2">数据实时监测，异常数据监控，智能分析提醒。</div>
          <div class="box_2_3"></div>
        </div>
      </div>
      <div class="section_2_box_3">
        <div class="box_1">
          <div class="box_1_1">区域可拓展</div>
          <div class="box_1_2">无缝升级至区域平台，无需重复建设。</div>
          <div class="box_1_3"></div>
        </div>
        <div class="box_2">
          <div class="box_2_1">依从性指数</div>
          <div class="box_2_2">建立专病档案，构建患者依从性指数，确保记录可追溯。</div>
          <div class="box_2_3"></div>
        </div>
        <div class="box_3">
          <div class="box_2_1">智能推荐慢病路径</div>
          <div class="box_2_2">根据患者情况，结合依从性指数，评估分析并匹配管理路径。</div>
          <div class="box_2_3"></div>
        </div>
      </div>
    </div>
    <div class="section_3">
      <div class="section_3_box1">
        <div class="box_1"></div>
      </div>
      <div class="section_3_box2">
        <div class="box_1">物联网健康网监测预警平台</div>
        <div class="box_2">打通主流厂商智能设备，平台开放、融合</div>
      </div>
      <div class="section_3_box3">
        <div class="left">
          <div class="box_1">
            <div class="box1"></div>
            <div class="box2">部署成本低</div>
          </div>
          <div class="box_2">
            按需分步部署，施工简单，升级扩展无需重复部署，降低施工成本。部署方式灵活，可独立部署和 区域部署。
          </div>
          <div class="box_3">
            <div class="left_s"></div>
            <div class="right_s">数据互联互通 丰富的数据接口</div>
          </div>
          <div class="box_4">可将海量物联网应用接入平台，进行一体化集成管理，实现数据互联互通，并形成大数据看板。</div>
          <div class="box_5">
            <div class="box_5_1"></div>
            <div class="box_5_2">运维更简单</div>
          </div>
          <div class="box_6">多种应用一套网络，物联网设备自助管理，固件远程升级，平台化运维简单便捷。</div>
        </div>
        <div class="right"></div>
      </div>
    </div>
    <div class="section_4">
      <div class="section_4_1">
        <div class="box1"></div>
      </div>
      <div class="section_4_2">
        <div class="box1">智能化随访健康管理平台</div>
        <div class="box2">多样化随访方式，补齐诊后随访短板</div>
      </div>
      <div class="section_4_3">
        <div class="left"></div>
        <div class="right">
          <div class="box_1">
            <div class="box_1_1"></div>
            <div class="box_1_2">随访知识库</div>
          </div>
          <div class="box_2">名医沉淀、权威模板复用，自动化生成随访方案和计划。</div>
          <div class="box_3">
            <div class="box_3_1"></div>
            <div class="box_3_2">随访机器人</div>
          </div>
          <div class="box_4">融合电话、短信、微信， 语音等，实现多样化随访。</div>
          <div class="box_5">
            <div class="box_5_1"></div>
            <div class="box_5_2">智能化穿戴设备</div>
          </div>
          <div class="box_6">24小时全天持续监测体征变化，实现数据自动采集。</div>
          <div class="box_7">
            <div class="box_7_1"></div>
            <div class="box_7_2">完善健康档案</div>
          </div>
          <div class="box_8">支持与第三方接口集成，自动化填入CRF表单，完善患者健康档案。</div>
        </div>
      </div>
    </div>
    <div class="section_5">
      <div class="section_5_box1">
        <div class="box_1"></div>
      </div>
      <div class="section_5_box2">
        <div class="box_1">助力基层数字化建设</div>
      </div>
    </div>
    <div class="section_6">
      <div class="section_6_box1">
        <div class="box_1"></div>
      </div>
      <div class="section_6_box2">
        <div class="box_1">软件展示</div>
      </div>
      <div class="section_6_box3"></div>
    </div>
    <div class="section_7">
      <div class="section_7_1">
        <div class="box_1"></div>
      </div>
      <div class="section_7_2">
        <div class="box_2">方案价值</div>
      </div>
      <div class="section_7_3">
        <div :class="isTrue === '0' ? 'section_7_3_1 isbox' : 'section_7_3_1'">
          <div class="box1" @click="handofUn('0')">对政府</div>
          <div v-show="isTrue === '0'" class="box2"></div>
        </div>
        <div :class="isTrue === '1' ? 'section_7_3_2 isbox' : 'section_7_3_2'">
          <div class="box1" @click="handofUn('1')">对医院</div>
          <div v-show="isTrue === '1'" class="box2"></div>
        </div>
        <div :class="isTrue === '2' ? 'section_7_3_3 isbox' : 'section_7_3_3'">
          <div class="box1" @click="handofUn('2')">对基层</div>
          <div v-show="isTrue === '2'" class="box2"></div>
        </div>
        <div :class="isTrue === '3' ? 'section_7_3_4 isbox' : 'section_7_3_4'">
          <div class="box1" @click="handofUn('3')">对村医</div>
          <div v-show="isTrue === '3'" class="box2"></div>
        </div>
      </div>
    </div>
    <div v-show="isTrue === '0'" class="section_8">
      <div class="left">
        <div class="box_1">
          <div class="box_1_left">01</div>
          <div class="box_1_right">
            <div class="top">疾病筛查和预防</div>
            <div class="bottom">
              智能化诊断和健康监测能够帮助卫健委更好地进行疾病筛查和预防。通过大数据分析和风险评估，预测并发现潜在的健康问题，及时采取措施进行干预和治疗，降低疾病发生的风险和危害。
            </div>
          </div>
        </div>
        <div class="box_2">
          <div class="box_2_left">02</div>
          <div class="box_2_right">
            <div class="top">家庭医生服务</div>
            <div class="bottom">
              能够帮助卫健委更好地管理和服务家庭医生。通过智能化病历管理和健康档案建设，更好地跟踪和管理患者的健康状况，为家庭医生提供更准确和及时的诊断和治疗建议。
            </div>
          </div>
        </div>
        <div class="box_3">
          <div class="box_3_left">03</div>
          <div class="box_3_right">
            <div class="top">慢病管理</div>
            <div class="bottom">
              能够帮助卫健委更好地管理和服务慢病患者。通过实时监测和数据分析，更好地评估患者的健康状况和病情变化，为患者提供更个性化、精准的慢病管理和治疗方案。
            </div>
          </div>
        </div>
        <div class="box_4">
          <div class="box_4_left">04</div>
          <div class="box_4_right">
            <div class="top">医疗资源优化配置</div>
            <div class="bottom">
              能够帮助卫健委更好地管理和规划医疗资源。通过智能化分析和预测，优化医疗资源的配置和共享，提高医疗资源的利用效率和管理效率。
            </div>
          </div>
        </div>
      </div>
      <div class="right">
        <div class="box_1">
          <div class="box_1_left">05</div>
          <div class="box_1_right">
            <div class="top">公共卫生服务</div>
            <div class="bottom">
              能够帮助卫健委更好地进行公共卫生管理和服务。通过智能化监测和数据分析，及时发现和应对公共卫生事件，为公众提供更全面、准确的公共卫生信息和建议。
            </div>
          </div>
        </div>
        <div class="box_2">
          <div class="box_2_left">06</div>
          <div class="box_2_right">
            <div class="top">健康干预</div>
            <div class="bottom">
              能够帮助卫健委更好地进行健康教育和干预工作。通过大数据分析和人工智能技术，评估个人的健康风险和疾病发展趋势，为个人提供个性化的健康建议和干预方案，提高人民群众的健康意识和自我保健能力。
            </div>
          </div>
        </div>
        <div class="box_3">
          <div class="box_3_left">07</div>
          <div class="box_3_right">
            <div class="top">基层医疗服务能力提升</div>
            <div class="bottom">
              智能化诊断和治疗工具能够帮助基层医疗机构提高医疗服务能力。通过智能化设备和系统平台的支持，基层医疗机构能够获得更多的医疗资源和技术支持，提高基层医疗服务的质量和效率。
            </div>
          </div>
        </div>
        <div class="box_4">
          <div class="box_4_left">08</div>
          <div class="box_4_right">
            <div class="top">患者就医服务优化</div>
            <div class="bottom">
              能够帮助卫健委更好地服务患者。通过智能化排班和预约系统，患者能够更加便捷地获取医疗服务，减少排队和等待时间，提高患者就医的满意度和体验。
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-show="isTrue === '1'" class="section_8_1">
      <div class="left">
        <div class="box_1">
          <div class="box_1_left">01</div>
          <div class="box_1_right">
            <div class="top">增加医院的收入和市场份额</div>
            <div class="bottom">
              医院可以赋能基层医疗机构，扩大医疗服务范围和服务内容，提高医疗服务的效率和质量，提高辖区内的患者就医满意度，让患者愿意留在辖区内，从而增加医院的收入和市场份额。
            </div>
          </div>
        </div>
        <div class="box_2">
          <div class="box_2_left">02</div>
          <div class="box_2_right">
            <div class="top">分级诊疗 提高床位周转率</div>
            <div class="bottom">
              平台帮助医院赋能基层医疗机构打造分级诊疗，通过慢病管理减少患者的发病率和住院率，提高医院床位周转率，增加医保开支，提高结余。
            </div>
          </div>
        </div>
        <div class="box_3">
          <div class="box_3_left">03</div>
          <div class="box_3_right">
            <div class="top">将本增效</div>
            <div class="bottom">
              平台整合上游医疗资源，通过集中采购、规模效应等方法降低医疗成本，提高医院的盈利能力。
            </div>
          </div>
        </div>
      </div>
      <div class="right">
        <div class="box_1">
          <div class="box_1_left">04</div>
          <div class="box_1_right">
            <div class="top">提高社会价值</div>
            <div class="bottom">医院通过该平台可以更好地履行社会责任，提高社会形象和声誉。</div>
          </div>
        </div>
        <div class="box_2">
          <div class="box_2_left">05</div>
          <div class="box_2_right">
            <div class="top">提高服务水平</div>
            <div class="bottom">
              通过疾病预防、智能诊疗、便捷随访、健康管理的一体化服务，可以更好地满足患者的健康需求，提高医院的服务水平和患者满意度。
            </div>
          </div>
        </div>
        <div class="box_3">
          <div class="box_3_left">06</div>
          <div class="box_3_right">
            <div class="top">推动医疗创新发展</div>
            <div class="bottom">可以为医院的科研和教学提供更多的数据和支持，推动医疗技术的创新和发展。</div>
          </div>
        </div>
      </div>
    </div>
    <div v-show="isTrue === '2'" class="section_8_2">
      <div class="left">
        <div class="box_1">
          <div class="box_1_left">01</div>
          <div class="box_1_right">
            <div class="top">资源共享</div>
            <div class="bottom">
              通过该平台，基层医疗机构可以获得更多的医疗资源和技术支持，提高基层医疗服务的质量和效率，增加基层医疗机构的收入和市场份额。
            </div>
          </div>
        </div>
        <div class="box_2">
          <div class="box_2_left">02</div>
          <div class="box_2_right">
            <div class="top">提高满意度 实现患者留存</div>
            <div class="bottom">
              平台提供的便捷随访和健康管理服务可以降低患者的流失率，提高患者的满意度和忠诚度，让更多的患者愿意留在基层医疗机构。
            </div>
          </div>
        </div>
        <div class="box_3">
          <div class="box_3_left">03</div>
          <div class="box_3_right">
            <div class="top">处方流转 个性化用药</div>
            <div class="bottom">
              为基层社区医院提供互联网医院及处方流转平台，拓展基础药品目录，满足辖区患者个性化用药的需求，提高患者满意度，降低药占比，减少医药的扣款。
            </div>
          </div>
        </div>
      </div>
      <div class="right">
        <div class="box_1">
          <div class="box_1_left">04</div>
          <div class="box_1_right">
            <div class="top">提高服务质量</div>
            <div class="bottom">
              基层医疗机构通过该平台可以更好地与上级医院合作和交流，提高基层医疗机构的服务水平和医疗技术水平。
            </div>
          </div>
        </div>
        <div class="box_2">
          <div class="box_2_left">05</div>
          <div class="box_2_right">
            <div class="top">一体化服务</div>
            <div class="bottom">
              平台提供的疾病预防、智能诊疗、便捷随访、健康管理的一体化服务可以更好地满足当地居民的健康需求，提高基层医疗机构的社会形象和声誉。
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-show="isTrue === '3'" class="section_8_3">
      <div class="left">
        <div class="box_1">
          <div class="box_1_left">01</div>
          <div class="box_1_right">
            <div class="top">创造经济价值</div>
            <div class="bottom">
              提高基层医疗服务的质量和效率，满足当地居民的健康需求，增加村卫生室的收入和市场份额。
            </div>
          </div>
        </div>
        <div class="box_2">
          <div class="box_2_left">02</div>
          <div class="box_2_right">
            <div class="top">避免重复建设</div>
            <div class="bottom">
              实现医疗资源的共享和优化配置，避免医疗资源的浪费和重复建设，提高村卫生室的管理效率。
            </div>
          </div>
        </div>
        <div class="box_3">
          <div class="box_3_left">03</div>
          <div class="box_3_right">
            <div class="top">提高工作效率</div>
            <div class="bottom">平台提供公卫相关功能，帮助村医提高工作效率，达到考核指标，增加经济收入。</div>
          </div>
        </div>
      </div>
      <div class="right">
        <div class="box_1">
          <div class="box_1_left">04</div>
          <div class="box_1_right">
            <div class="top">互动学习 医疗保障</div>
            <div class="bottom">
              村卫生室通过该平台可以更好地与上级医疗机构合作和交流，提高村卫生室的服务水平和医疗技术水平。
            </div>
          </div>
        </div>
        <div class="box_2">
          <div class="box_2_left">05</div>
          <div class="box_2_right">
            <div class="top">提高准确性</div>
            <div class="bottom">通过智能诊断设备，村医可以更快更准确地诊断病情，提高诊疗的准确性和效率。</div>
          </div>
        </div>
        <div class="box_3">
          <div class="box_3_left">06</div>
          <div class="box_3_right">
            <div class="top">实时监测 及时治疗</div>
            <div class="bottom">
              通过智能化的健康监测设备，村医可以实时监测患者的健康状况，及时发现和预防疾病，减少病情恶化的可能性。
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="section_9">
      <div class="section_9_box1">
        我们的客户评价
        <div class="zhe"></div>
      </div>
      <div class="section_9_box2">
        <div class="section_9_box2_1">
          <img src="@/assets/images/wenwen.png" alt="" class="hujis" />
          <div class="box_1">“平台带来的数字化管理帮我们节省了大量人力”</div>
          <div class="box_2">
            <div class="box_2_left"></div>
            <div class="box_2_right">
              <span class="box1">某医院</span>
              <span class="box2">某院长</span>
            </div>
          </div>
        </div>
        <div class="section_9_box2_3">
          <img src="@/assets/images/tanhua.png" alt="" class="hujis" />
          <div class="box_1">“平台化繁为简，帮助医院释放服务效能”</div>
          <div class="box_2">
            <div class="box_2_left"></div>
            <div class="box_2_right">
              <span class="box1">某医院</span>
              <span class="box2">主任医师</span>
            </div>
          </div>
        </div>
        <div class="section_9_box2_4">
          <img src="@/assets/images/kaihui.png" alt="" class="hujis" />
          <div class="box_1">“平台帮助我们使复杂的工作流程变得简单，对我们帮助很大”</div>
          <div class="box_2">
            <div class="box_2_left"></div>
            <div class="box_2_right">
              <span class="box1">某医院</span>
              <span class="box2">门诊医生</span>
            </div>
          </div>
        </div>
        <div class="section_9_box2_2">
          <img src="@/assets/images/tingzhens.png" alt="" class="hujis" />
          <div class="box_1">“使用该平台将我们的检查时间提升了20%”</div>
          <div class="box_2">
            <div class="box_2_left"></div>
            <div class="box_2_right">
              <span class="box1">某医院</span>
              <span class="box2">体检医生</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="section_10">
      <div class="section_10_box1">
        <div class="box_1">
          运营平台
          <div class="zhe"></div>
        </div>
        <div class="box_2">
          <img src="@/assets/images/dadianhua.png" alt="" class="svg" />
          <div class="text">详情咨询</div>
        </div>
        <div class="box_3">400-777-5100</div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.image-main {
  width: 100%;
  max-width: 160rem;
  overflow: hidden;
  margin: 0 auto;
  img {
    width: 100%;
  }
  .mains {
    margin-top: 3.5rem;
    width: 100%;
    height: 37.5rem;
    background-image: url('@/assets/images/solutionHeader.png');
    background-size: 100% 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    .box1 {
      display: flex;
      flex-direction: column;
      width: 92.5rem;
      .box1_1 {
        font-size: 2.25rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #222222;
      }
      .box1_2 {
        font-size: 1.25rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        margin-top: 0.9375rem;
      }
    }
  }
  .section_1 {
    width: 100%;
    height: 84.5625rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    background: #ffffff;
    .section_1_box1 {
      margin-top: 4.375rem;
      width: 92.5rem;
      height: 4.5rem;
      font-size: 1.5rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #666666;
      line-height: 2.25rem;
    }
    .section_1_box2 {
      width: 92.5rem;
      margin-top: 4.375rem;
      margin-bottom: 1.1875rem;
      .box_1 {
        width: 17.25rem;
        height: 0.375rem;
        background-image: url('@/assets/images/juxing.png');
        background-size: 100% 100%;
      }
    }
    .section_1_box3 {
      width: 92.5rem;
      margin-bottom: 2.5625rem;
      .box_1 {
        width: 17.25rem;
        height: 2.8125rem;
        font-size: 1.875rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #222222;
        line-height: 2.8125rem;
      }
    }
    .section_1_box4 {
      width: 92.5rem;
      height: 60.625rem;
      background-image: url('@/assets/images/yidui.png');
      background-size: 100% 100%;
    }
  }
  .section_2 {
    width: 100%;
    height: 39.4375rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    background-image: url('@/assets/images/lanbgrs.png');
    background-size: 100% 100%;
    .section_2_box {
      width: 91.5625rem;
      .box_1 {
        width: 21.25rem;
        height: 0.375rem;
        background: linear-gradient(90deg, #4865ff 0%, rgba(255, 255, 255, 0) 100%);
        border-radius: 0.1875rem;
        margin-top: 4.375rem;
      }
    }
    .section_2_box_1 {
      width: 91.5625rem;
      margin-top: 1.1875rem;
      margin-bottom: 2.5625rem;
      .box_1 {
        width: 21.25rem;
        height: 2.8125rem;
        font-size: 1.875rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #222222;
        line-height: 2.8125rem;
      }
    }
    .section_2_box_2 {
      width: 89.375rem;
      display: flex;
      margin-bottom: 2.5rem;
      .box_1 {
        position: relative;
        width: 28.125rem;
        height: 10.625rem;
        background: #ffffff;
        border-radius: 0.9375rem;
        .box_1_1 {
          height: 2.0625rem;
          font-size: 1.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #222222;
          line-height: 2.0625rem;
          position: absolute;
          top: 2.375rem;
          left: 1.875rem;
        }
        .box_1_2 {
          width: 24.375rem;
          height: 1.625rem;
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #666666;
          line-height: 1.625rem;
          position: absolute;
          top: 5.6875rem;
          left: 1.875rem;
        }
        .box_1_3 {
          width: 3.125rem;
          height: 3.125rem;
          background-image: url('@/assets/images/duanxin.png');
          background-size: 100% 100%;
          position: absolute;
          top: 1.875rem;
          right: 1.875rem;
        }
      }
      .box_2 {
        position: relative;
        width: 28.125rem;
        height: 10.625rem;
        background: #ffffff;
        margin: 0 2.5rem;
        border-radius: 0.9375rem;
        .box_2_1 {
          height: 2.0625rem;
          font-size: 1.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #222222;
          line-height: 2.0625rem;
          position: absolute;
          top: 2.375rem;
          left: 1.875rem;
        }
        .box_2_2 {
          width: 24.375rem;
          height: 1.625rem;
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #666666;
          line-height: 1.625rem;
          position: absolute;
          top: 5.6875rem;
          left: 1.875rem;
        }
        .box_2_3 {
          width: 3.125rem;
          height: 3.125rem;
          background-image: url('@/assets/images/caidan.png');
          background-size: 100% 100%;
          position: absolute;
          top: 1.875rem;
          right: 1.875rem;
        }
      }
      .box_3 {
        position: relative;
        width: 28.125rem;
        height: 10.625rem;
        background: #ffffff;
        border-radius: 0.9375rem;
        .box_2_1 {
          height: 2.0625rem;
          font-size: 1.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #222222;
          line-height: 2.0625rem;
          position: absolute;
          top: 2.375rem;
          left: 1.875rem;
        }
        .box_2_2 {
          width: 24.375rem;
          height: 1.625rem;
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #666666;
          line-height: 1.625rem;
          position: absolute;
          top: 5.6875rem;
          left: 1.875rem;
        }
        .box_2_3 {
          width: 3.125rem;
          height: 3.125rem;
          background-image: url('@/assets/images/bluephone.png');
          background-size: 100% 100%;
          position: absolute;
          top: 1.875rem;
          right: 1.875rem;
        }
      }
    }
    .section_2_box_3 {
      width: 89.375rem;
      display: flex;
      .box_1 {
        position: relative;
        width: 28.125rem;
        height: 10.625rem;
        background: #ffffff;
        border-radius: 0.9375rem;
        .box_1_1 {
          height: 2.0625rem;
          font-size: 1.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #222222;
          line-height: 2.0625rem;
          position: absolute;
          top: 2.375rem;
          left: 1.875rem;
        }
        .box_1_2 {
          width: 24.375rem;
          height: 1.625rem;
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #666666;
          line-height: 1.625rem;
          position: absolute;
          top: 5.6875rem;
          left: 1.875rem;
        }
        .box_1_3 {
          width: 3.125rem;
          height: 3.125rem;
          background-image: url('@/assets/images/anquan.png');
          background-size: 100% 100%;
          position: absolute;
          top: 1.875rem;
          right: 1.875rem;
        }
      }
      .box_2 {
        position: relative;
        width: 28.125rem;
        height: 10.625rem;
        background: #ffffff;
        margin: 0 2.5rem;
        border-radius: 0.9375rem;
        .box_2_1 {
          height: 2.0625rem;
          font-size: 1.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #222222;
          line-height: 2.0625rem;
          position: absolute;
          top: 2.375rem;
          left: 1.875rem;
        }
        .box_2_2 {
          width: 24.375rem;
          height: 3.25rem;
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #666666;
          line-height: 1.625rem;
          position: absolute;
          top: 5.6875rem;
          left: 1.875rem;
        }
        .box_2_3 {
          width: 3.125rem;
          height: 3.125rem;
          background-image: url('@/assets/images/baibaoxiang.png');
          background-size: 100% 100%;
          position: absolute;
          top: 1.875rem;
          right: 1.875rem;
        }
      }
      .box_3 {
        position: relative;
        width: 28.125rem;
        height: 10.625rem;
        background: #ffffff;
        border-radius: 0.9375rem;
        .box_2_1 {
          height: 2.0625rem;
          font-size: 1.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #222222;
          line-height: 2.0625rem;
          position: absolute;
          top: 2.375rem;
          left: 1.875rem;
        }
        .box_2_2 {
          width: 24.375rem;
          height: 3.25rem;
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #666666;
          line-height: 1.625rem;
          position: absolute;
          top: 5.6875rem;
          left: 1.875rem;
        }
        .box_2_3 {
          width: 3.125rem;
          height: 3.125rem;
          background-image: url('@/assets/images/zhenguan.png');
          background-size: 100% 100%;
          position: absolute;
          top: 1.875rem;
          right: 1.875rem;
        }
      }
    }
  }
  .section_3 {
    width: 100%;
    height: 44.5rem;
    background: rgba(255, 255, 255, 0);
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    .section_3_box1 {
      width: 92.5rem;
      margin-top: 4.375rem;
      .box_1 {
        width: 24rem;
        height: 0.375rem;
        background: linear-gradient(90deg, #4865ff 0%, rgba(255, 255, 255, 0) 100%);
        border-radius: 0.1875rem;
      }
    }
    .section_3_box2 {
      width: 92.5rem;
      margin-top: 1.1875rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .box_1 {
        width: 24rem;
        height: 2.8125rem;
        font-size: 1.875rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #222222;
        line-height: 2.8125rem;
      }
      .box_2 {
        font-size: 1.5rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #222222;
      }
    }
    .section_3_box3 {
      width: 92.5rem;
      height: 28.75rem;
      display: flex;
      margin-top: 2.5625rem;
      .left {
        flex: 1;
        display: flex;
        flex-direction: column;
        .box_1 {
          display: flex;
          align-items: center;
          margin-top: 4rem;
          .box1 {
            width: 1.25rem;
            height: 1.25rem;
            background-image: url('@/assets/images/fangkuai.png');
            background-size: 100% 100%;
            margin-right: 1.0625rem;
          }
          .box2 {
            width: 37.6875rem;
            height: 2.3125rem;
            font-size: 1.5rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #4865ff;
            line-height: 2.3125rem;
          }
        }
        .box_2 {
          width: 37.6875rem;
          height: 3.25rem;
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #666666;
          line-height: 1.625rem;
          margin-top: 0.625rem;
          margin-left: 2.3125rem;
        }
        .box_3 {
          display: flex;
          align-items: center;
          margin-top: 1.875rem;
          .left_s {
            width: 1.25rem;
            height: 1.25rem;
            background-image: url('@/assets/images/fangkuai.png');
            background-size: 100% 100%;
            margin-right: 1.0625rem;
          }
          .right_s {
            width: 37.6875rem;
            height: 2.3125rem;
            font-size: 1.5rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #4865ff;
            line-height: 2.3125rem;
          }
        }
        .box_4 {
          width: 37.6875rem;
          height: 3.25rem;
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #666666;
          line-height: 1.625rem;
          margin-top: 0.625rem;
          margin-left: 2.3125rem;
        }
        .box_5 {
          display: flex;
          align-items: center;
          margin-top: 1.875rem;
          .box_5_1 {
            width: 1.25rem;
            height: 1.25rem;
            background-image: url('@/assets/images/fangkuai.png');
            background-size: 100% 100%;
            margin-right: 1.0625rem;
          }
          .box_5_2 {
            width: 37.6875rem;
            height: 2.3125rem;
            font-size: 1.5rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #4865ff;
            line-height: 2.3125rem;
          }
        }
        .box_6 {
          width: 37.6875rem;
          height: 1.625rem;
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #666666;
          line-height: 1.625rem;
          margin-left: 2.3125rem;
          margin-top: 0.625rem;
        }
      }
      .right {
        width: 50rem;
        height: 28.75rem;
        background-image: url('@/assets/images/daping.png');
        background-size: 100% 100%;
      }
    }
  }
  .section_4 {
    width: 100%;
    height: 44.5rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    .section_4_1 {
      margin-top: 4.375rem;
      width: 92.4375rem;
      .box1 {
        width: 22rem;
        height: 0.375rem;
        background: linear-gradient(90deg, #4865ff 0%, rgba(255, 255, 255, 0) 100%);
        border-radius: 0.1875rem;
      }
    }
    .section_4_2 {
      margin-top: 0.625rem;
      width: 92.4375rem;
      display: flex;
      justify-content: space-between;
      .box1 {
        width: 22rem;
        height: 2.8125rem;
        font-size: 1.875rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #222222;
        line-height: 2.8125rem;
      }
      .box2 {
        font-size: 1.5rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #222222;
      }
    }
    .section_4_3 {
      width: 92.4375rem;
      margin-top: 2.6875rem;
      display: flex;
      .left {
        width: 50rem;
        height: 28.75rem;
        background-image: url('@/assets/images/guanli.png');
        background-size: 100% 100%;
      }
      .right {
        display: flex;
        flex-direction: column;
        margin-left: 2.5rem;
        .box_1 {
          width: 40rem;
          display: flex;
          align-items: center;
          margin-top: 2.9375rem;
          .box_1_1 {
            width: 1.25rem;
            height: 1.25rem;
            background-image: url('@/assets/images/fangkuai.png');
            background-size: 100% 100%;
            margin-right: 1.0625rem;
          }
          .box_1_2 {
            width: 37.6875rem;
            height: 2.3125rem;
            font-size: 1.5rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #4865ff;
            line-height: 2.3125rem;
          }
        }
        .box_2 {
          width: 37.6875rem;
          height: 1.375rem;
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #666666;
          line-height: 1.375rem;
          margin-left: 2.3125rem;
          margin-top: 0.625rem;
          margin-bottom: 1.875rem;
        }
        .box_3 {
          width: 40rem;
          display: flex;
          align-items: center;
          margin-top: 1.25rem;
          .box_3_1 {
            width: 1.25rem;
            height: 1.25rem;
            background-image: url('@/assets/images/fangkuai.png');
            background-size: 100% 100%;
            margin-right: 1.0625rem;
          }
          .box_3_2 {
            width: 37.6875rem;
            height: 2.3125rem;
            font-size: 1.5rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #4865ff;
            line-height: 2.3125rem;
          }
        }
        .box_4 {
          width: 37.6875rem;
          height: 1.375rem;
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #666666;
          line-height: 1.375rem;
          margin-left: 2.3125rem;
          margin-top: 0.625rem;
          margin-bottom: 1.875rem;
        }
        .box_5 {
          width: 40rem;
          display: flex;
          align-items: center;
          .box_5_1 {
            width: 1.25rem;
            height: 1.25rem;
            background-image: url('@/assets/images/fangkuai.png');
            background-size: 100% 100%;
            margin-right: 1.0625rem;
          }
          .box_5_2 {
            width: 37.6875rem;
            height: 2.3125rem;
            font-size: 1.5rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #4865ff;
            line-height: 2.3125rem;
          }
        }
        .box_6 {
          width: 37.6875rem;
          height: 1.375rem;
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #666666;
          line-height: 1.375rem;
          margin-left: 2.3125rem;
          margin-top: 0.625rem;
          margin-bottom: 1.875rem;
        }
        .box_7 {
          width: 40rem;
          display: flex;
          align-items: center;
          .box_7_1 {
            width: 1.25rem;
            height: 1.25rem;
            background-image: url('@/assets/images/fangkuai.png');
            background-size: 100% 100%;
            margin-right: 1.0625rem;
          }
          .box_7_2 {
            width: 37.6875rem;
            height: 2.3125rem;
            font-size: 1.5rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #4865ff;
            line-height: 2.3125rem;
          }
        }
        .box_8 {
          width: 37.6875rem;
          height: 1.375rem;
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #666666;
          line-height: 1.375rem;
          margin-left: 2.3125rem;
          margin-top: 0.625rem;
          margin-bottom: 1.25rem;
        }
      }
    }
  }
  .section_5 {
    width: 100%;
    height: 39.9375rem;
    display: flex;
    background-image: url('@/assets/images/xindian.png');
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    .section_5_box1 {
      width: 92.4375rem;
      margin-top: 4.375rem;
      .box_1 {
        width: 18rem;
        height: 0.375rem;
        background: linear-gradient(90deg, #4865ff 0%, rgba(255, 255, 255, 0) 100%);
        border-radius: 0.1875rem;
      }
    }
    .section_5_box2 {
      width: 92.4375rem;
      .box_1 {
        width: 18rem;
        height: 2.8125rem;
        font-size: 1.875rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #222222;
        line-height: 2.8125rem;
        margin-top: 1.125rem;
      }
    }
  }
  .section_6 {
    width: 100%;
    height: 72.75rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    .section_6_box1 {
      width: 92.5rem;
      margin-top: 4.375rem;
      .box_1 {
        width: 8rem;
        height: 0.375rem;
        background: linear-gradient(90deg, #4865ff 0%, rgba(255, 255, 255, 0) 100%);
        border-radius: 0.1875rem;
      }
    }
    .section_6_box2 {
      width: 92.5rem;
      margin-top: 1.125rem;
      margin-bottom: 2.5rem;
      .box_1 {
        width: 8rem;
        height: 2.8125rem;
        font-size: 1.875rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #222222;
        line-height: 2.8125rem;
      }
    }
    .section_6_box3 {
      width: 92.5rem;
      height: 57rem;
      background-image: url('@/assets/images/sige.png');
      background-size: 100% 100%;
    }
  }
  .section_7 {
    width: 100%;
    height: 15.75rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    background: #ffffff;
    .section_7_1 {
      width: 92.5rem;
      margin-top: 4.375rem;
      .box_1 {
        width: 8rem;
        height: 0.375rem;
        background: linear-gradient(90deg, #4865ff 0%, rgba(255, 255, 255, 0) 100%);
        border-radius: 0.1875rem;
      }
    }
    .section_7_2 {
      width: 92.5rem;
      margin-top: 1.125rem;
      .box_2 {
        width: 8rem;
        height: 2.8125rem;
        font-size: 1.875rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #222222;
        line-height: 2.8125rem;
      }
    }
    .section_7_3 {
      display: flex;
      margin-top: 2.6875rem;
      width: 92.5rem;
      height: 4.375rem;
      .isbox {
        .box1 {
          color: #4865ff !important;
        }
        .box2 {
          width: 12.5rem !important;
          height: 0.25rem !important;
          background: #4865ff !important;
          border-radius: 0.125rem !important;
        }
      }
      .section_7_3_1 {
        width: 23.125rem;
        height: 4.375rem;
        background: rgba(237, 245, 251, 0);
        display: flex;
        flex-direction: column;
        align-items: center;
        .box1 {
          width: 5.625rem;
          height: 2.625rem;
          font-size: 1.5rem;
          text-align: center;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #222222;
          line-height: 2.625rem;
          margin: 0.75rem 0;
          cursor: pointer;
        }
        .box2 {
          width: 12.5rem;
          height: 0.25rem;
          color: #222222;
          border-radius: 0.125rem;
        }
      }
      .section_7_3_2 {
        width: 23.125rem;
        height: 4.375rem;
        background: rgba(237, 245, 251, 0);
        display: flex;
        flex-direction: column;
        align-items: center;
        .box1 {
          width: 5.625rem;
          height: 2.625rem;
          font-size: 1.5rem;
          text-align: center;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #222222;
          line-height: 2.625rem;
          margin: 0.75rem 0;
          cursor: pointer;
        }
      }
      .section_7_3_3 {
        width: 23.125rem;
        height: 4.375rem;
        background: rgba(237, 245, 251, 0);
        display: flex;
        flex-direction: column;
        align-items: center;
        .box1 {
          width: 5.625rem;
          height: 2.625rem;
          font-size: 1.5rem;
          text-align: center;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #222222;
          line-height: 2.625rem;
          margin: 0.75rem 0;
          cursor: pointer;
        }
      }
      .section_7_3_4 {
        width: 23.125rem;
        height: 4.375rem;
        background: rgba(237, 245, 251, 0);
        display: flex;
        flex-direction: column;
        align-items: center;
        .box1 {
          width: 5.625rem;
          height: 2.625rem;
          font-size: 1.5rem;
          text-align: center;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #222222;
          line-height: 2.625rem;
          margin: 0.75rem 0;
          cursor: pointer;
        }
      }
    }
  }
  .section_8 {
    width: 100%;
    display: flex;
    justify-content: center;
    background-color: #f8f8f8;
    .left {
      width: 37.5rem;
      display: flex;
      flex-direction: column;
      margin-right: 4.375rem;
      margin-top: 2.5rem;
      .box_1 {
        width: 37.5rem;
        height: 11.25rem;
        display: flex;
        .box_1_left {
          width: 4.375rem;
          height: 4.375rem;
          background-image: url('@/assets/images/fangbian.png');
          background-size: 100% 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 2.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 4.375rem;
        }
        .box_1_right {
          display: flex;
          flex-direction: column;
          margin-left: 0.9375rem;
          .top {
            width: 13.125rem;
            height: 2.625rem;
            font-size: 1.5rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #222222;
            line-height: 2.625rem;
          }
          .bottom {
            width: 31.25rem;
            height: 8.25rem;
            font-size: 1rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #222222;
            line-height: 2.0625rem;
          }
        }
      }
      .box_2 {
        width: 37.5rem;
        height: 11.25rem;
        display: flex;
        .box_2_left {
          width: 4.375rem;
          height: 4.375rem;
          background-image: url('@/assets/images/fangbian.png');
          background-size: 100% 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 2.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 4.375rem;
        }
        .box_2_right {
          display: flex;
          flex-direction: column;
          margin-left: 0.9375rem;
          .top {
            width: 13.125rem;
            height: 2.625rem;
            font-size: 1.5rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #222222;
            line-height: 2.625rem;
          }
          .bottom {
            width: 31.25rem;
            height: 8.25rem;
            font-size: 1rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #222222;
            line-height: 2.0625rem;
          }
        }
      }
      .box_3 {
        width: 37.5rem;
        height: 11.25rem;
        display: flex;
        .box_3_left {
          width: 4.375rem;
          height: 4.375rem;
          background-image: url('@/assets/images/fangbian.png');
          background-size: 100% 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 2.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 4.375rem;
        }
        .box_3_right {
          display: flex;
          flex-direction: column;
          margin-left: 0.9375rem;
          .top {
            width: 13.125rem;
            height: 2.625rem;
            font-size: 1.5rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #222222;
            line-height: 2.625rem;
          }
          .bottom {
            width: 31.25rem;
            height: 8.25rem;
            font-size: 1rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #222222;
            line-height: 2.0625rem;
          }
        }
      }
      .box_4 {
        width: 37.5rem;
        height: 11.25rem;
        display: flex;
        .box_4_left {
          width: 4.375rem;
          height: 4.375rem;
          background-image: url('@/assets/images/fangbian.png');
          background-size: 100% 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 2.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 4.375rem;
        }
        .box_4_right {
          display: flex;
          flex-direction: column;
          margin-left: 0.9375rem;
          .top {
            width: 13.125rem;
            height: 2.625rem;
            font-size: 1.5rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #222222;
            line-height: 2.625rem;
          }
          .bottom {
            width: 31.25rem;
            height: 8.25rem;
            font-size: 1rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #222222;
            line-height: 2.0625rem;
          }
        }
      }
    }
    .right {
      margin-top: 2.5rem;
      width: 37.5rem;
      display: flex;
      flex-direction: column;
      .box_1 {
        width: 37.5rem;
        height: 11.25rem;
        display: flex;
        .box_1_left {
          width: 4.375rem;
          height: 4.375rem;
          background-image: url('@/assets/images/fangbian.png');
          background-size: 100% 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 2.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 4.375rem;
        }
        .box_1_right {
          display: flex;
          flex-direction: column;
          margin-left: 0.9375rem;
          .top {
            width: 13.125rem;
            height: 2.625rem;
            font-size: 1.5rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #222222;
            line-height: 2.625rem;
          }
          .bottom {
            width: 31.25rem;
            height: 8.25rem;
            font-size: 1rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #222222;
            line-height: 2.0625rem;
          }
        }
      }
      .box_2 {
        width: 37.5rem;
        height: 11.25rem;
        display: flex;
        .box_2_left {
          width: 4.375rem;
          height: 4.375rem;
          background-image: url('@/assets/images/fangbian.png');
          background-size: 100% 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 2.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 4.375rem;
        }
        .box_2_right {
          display: flex;
          flex-direction: column;
          margin-left: 0.9375rem;
          .top {
            width: 13.125rem;
            height: 2.625rem;
            font-size: 1.5rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #222222;
            line-height: 2.625rem;
          }
          .bottom {
            width: 31.25rem;
            height: 8.25rem;
            font-size: 1rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #222222;
            line-height: 2.0625rem;
          }
        }
      }
      .box_3 {
        width: 37.5rem;
        height: 11.25rem;
        display: flex;
        .box_3_left {
          width: 4.375rem;
          height: 4.375rem;
          background-image: url('@/assets/images/fangbian.png');
          background-size: 100% 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 2.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 4.375rem;
        }
        .box_3_right {
          display: flex;
          flex-direction: column;
          margin-left: 0.9375rem;
          .top {
            width: 15rem;
            height: 2.625rem;
            font-size: 1.5rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #222222;
            line-height: 2.625rem;
          }
          .bottom {
            width: 31.25rem;
            height: 8.25rem;
            font-size: 1rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #222222;
            line-height: 2.0625rem;
          }
        }
      }
      .box_4 {
        width: 37.5rem;
        height: 11.25rem;
        display: flex;
        .box_4_left {
          width: 4.375rem;
          height: 4.375rem;
          background-image: url('@/assets/images/fangbian.png');
          background-size: 100% 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 2.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 4.375rem;
        }
        .box_4_right {
          display: flex;
          flex-direction: column;
          margin-left: 0.9375rem;
          .top {
            width: 15rem;
            height: 2.625rem;
            font-size: 1.5rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #222222;
            line-height: 2.625rem;
          }
          .bottom {
            width: 31.25rem;
            height: 8.25rem;
            font-size: 1rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #222222;
            line-height: 2.0625rem;
          }
        }
      }
    }
  }
  .section_8_1 {
    width: 100%;
    height: 50rem;
    display: flex;
    justify-content: center;
    background-color: #f8f8f8;
    .left {
      width: 37.5rem;
      display: flex;
      flex-direction: column;
      margin-right: 4.375rem;
      margin-top: 2.5rem;
      .box_1 {
        width: 37.5rem;
        height: 11.25rem;
        display: flex;
        .box_1_left {
          width: 4.375rem;
          height: 4.375rem;
          background-image: url('@/assets/images/fangbian.png');
          background-size: 100% 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 2.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 4.375rem;
        }
        .box_1_right {
          display: flex;
          flex-direction: column;
          margin-left: 0.9375rem;
          .top {
            width: 18.125rem;
            height: 2.625rem;
            font-size: 1.5rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #222222;
            line-height: 2.625rem;
          }
          .bottom {
            width: 31.25rem;
            height: 8.25rem;
            font-size: 1rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #222222;
            line-height: 2.0625rem;
          }
        }
      }
      .box_2 {
        width: 37.5rem;
        height: 11.25rem;
        display: flex;
        .box_2_left {
          width: 4.375rem;
          height: 4.375rem;
          background-image: url('@/assets/images/fangbian.png');
          background-size: 100% 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 2.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 4.375rem;
        }
        .box_2_right {
          display: flex;
          flex-direction: column;
          margin-left: 0.9375rem;
          .top {
            width: 17.1875rem;
            font-size: 1.5rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #222222;
            line-height: 2.625rem;
          }
          .bottom {
            width: 31.25rem;
            height: 8.25rem;
            font-size: 1rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #222222;
            line-height: 2.0625rem;
          }
        }
      }
      .box_3 {
        width: 37.5rem;
        height: 11.25rem;
        display: flex;
        .box_3_left {
          width: 4.375rem;
          height: 4.375rem;
          background-image: url('@/assets/images/fangbian.png');
          background-size: 100% 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 2.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 4.375rem;
        }
        .box_3_right {
          display: flex;
          flex-direction: column;
          margin-left: 0.9375rem;
          .top {
            width: 13.125rem;
            height: 2.625rem;
            font-size: 1.5rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #222222;
            line-height: 2.625rem;
          }
          .bottom {
            width: 31.25rem;
            height: 8.25rem;
            font-size: 1rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #222222;
            line-height: 2.0625rem;
          }
        }
      }
    }
    .right {
      margin-top: 2.5rem;
      width: 37.5rem;
      display: flex;
      flex-direction: column;
      .box_1 {
        width: 37.5rem;
        height: 11.25rem;
        display: flex;
        .box_1_left {
          width: 4.375rem;
          height: 4.375rem;
          background-image: url('@/assets/images/fangbian.png');
          background-size: 100% 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 2.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 4.375rem;
        }
        .box_1_right {
          display: flex;
          flex-direction: column;
          margin-left: 0.9375rem;
          .top {
            width: 13.125rem;
            height: 2.625rem;
            font-size: 1.5rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #222222;
            line-height: 2.625rem;
          }
          .bottom {
            width: 31.25rem;
            height: 8.25rem;
            font-size: 1rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #222222;
            line-height: 2.0625rem;
          }
        }
      }
      .box_2 {
        width: 37.5rem;
        height: 11.25rem;
        display: flex;
        .box_2_left {
          width: 4.375rem;
          height: 4.375rem;
          background-image: url('@/assets/images/fangbian.png');
          background-size: 100% 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 2.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 4.375rem;
        }
        .box_2_right {
          display: flex;
          flex-direction: column;
          margin-left: 0.9375rem;
          .top {
            width: 13.125rem;
            height: 2.625rem;
            font-size: 1.5rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #222222;
            line-height: 2.625rem;
          }
          .bottom {
            width: 31.25rem;
            height: 8.25rem;
            font-size: 1rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #222222;
            line-height: 2.0625rem;
          }
        }
      }
      .box_3 {
        width: 37.5rem;
        height: 11.25rem;
        display: flex;
        .box_3_left {
          width: 4.375rem;
          height: 4.375rem;
          background-image: url('@/assets/images/fangbian.png');
          background-size: 100% 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 2.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 4.375rem;
        }
        .box_3_right {
          display: flex;
          flex-direction: column;
          margin-left: 0.9375rem;
          .top {
            width: 15rem;
            height: 2.625rem;
            font-size: 1.5rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #222222;
            line-height: 2.625rem;
          }
          .bottom {
            width: 31.25rem;
            height: 8.25rem;
            font-size: 1rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #222222;
            line-height: 2.0625rem;
          }
        }
      }
    }
  }
  .section_8_2 {
    width: 100%;
    height: 50rem;
    display: flex;
    justify-content: center;
    background-color: #f8f8f8;
    .left {
      width: 37.5rem;
      display: flex;
      flex-direction: column;
      margin-right: 4.375rem;
      margin-top: 2.5rem;
      .box_1 {
        width: 37.5rem;
        height: 11.25rem;
        display: flex;
        .box_1_left {
          width: 4.375rem;
          height: 4.375rem;
          background-image: url('@/assets/images/fangbian.png');
          background-size: 100% 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 2.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 4.375rem;
        }
        .box_1_right {
          display: flex;
          flex-direction: column;
          margin-left: 0.9375rem;
          .top {
            width: 13.125rem;
            height: 2.625rem;
            font-size: 1.5rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #222222;
            line-height: 2.625rem;
          }
          .bottom {
            width: 31.25rem;
            height: 8.25rem;
            font-size: 1rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #222222;
            line-height: 2.0625rem;
          }
        }
      }
      .box_2 {
        width: 37.5rem;
        height: 11.25rem;
        display: flex;
        .box_2_left {
          width: 4.375rem;
          height: 4.375rem;
          background-image: url('@/assets/images/fangbian.png');
          background-size: 100% 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 2.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 4.375rem;
        }
        .box_2_right {
          display: flex;
          flex-direction: column;
          margin-left: 0.9375rem;
          .top {
            width: 17.5rem;
            height: 2.625rem;
            font-size: 1.5rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #222222;
            line-height: 2.625rem;
          }
          .bottom {
            width: 31.25rem;
            height: 8.25rem;
            font-size: 1rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #222222;
            line-height: 2.0625rem;
          }
        }
      }
      .box_3 {
        width: 37.5rem;
        height: 11.25rem;
        display: flex;
        .box_3_left {
          width: 4.375rem;
          height: 4.375rem;
          background-image: url('@/assets/images/fangbian.png');
          background-size: 100% 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 2.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 4.375rem;
        }
        .box_3_right {
          display: flex;
          flex-direction: column;
          margin-left: 0.9375rem;
          .top {
            width: 15.625rem;
            height: 2.625rem;
            font-size: 1.5rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #222222;
            line-height: 2.625rem;
          }
          .bottom {
            width: 31.25rem;
            height: 8.25rem;
            font-size: 1rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #222222;
            line-height: 2.0625rem;
          }
        }
      }
    }
    .right {
      margin-top: 2.5rem;
      width: 37.5rem;
      display: flex;
      flex-direction: column;
      .box_1 {
        width: 37.5rem;
        height: 11.25rem;
        display: flex;
        .box_1_left {
          width: 4.375rem;
          height: 4.375rem;
          background-image: url('@/assets/images/fangbian.png');
          background-size: 100% 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 2.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 4.375rem;
        }
        .box_1_right {
          display: flex;
          flex-direction: column;
          margin-left: 0.9375rem;
          .top {
            width: 13.125rem;
            height: 2.625rem;
            font-size: 1.5rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #222222;
            line-height: 2.625rem;
          }
          .bottom {
            width: 31.25rem;
            height: 8.25rem;
            font-size: 1rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #222222;
            line-height: 2.0625rem;
          }
        }
      }
      .box_2 {
        width: 37.5rem;
        height: 11.25rem;
        display: flex;
        .box_2_left {
          width: 4.375rem;
          height: 4.375rem;
          background-image: url('@/assets/images/fangbian.png');
          background-size: 100% 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 2.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 4.375rem;
        }
        .box_2_right {
          display: flex;
          flex-direction: column;
          margin-left: 0.9375rem;
          .top {
            width: 13.125rem;
            height: 2.625rem;
            font-size: 1.5rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #222222;
            line-height: 2.625rem;
          }
          .bottom {
            width: 31.25rem;
            height: 8.25rem;
            font-size: 1rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #222222;
            line-height: 2.0625rem;
          }
        }
      }
    }
  }
  .section_8_3 {
    width: 100%;
    height: 50rem;
    display: flex;
    justify-content: center;
    background-color: #f8f8f8;
    .left {
      width: 37.5rem;
      display: flex;
      flex-direction: column;
      margin-right: 4.375rem;
      margin-top: 2.5rem;
      .box_1 {
        width: 37.5rem;
        height: 11.25rem;
        display: flex;
        .box_1_left {
          width: 4.375rem;
          height: 4.375rem;
          background-image: url('@/assets/images/fangbian.png');
          background-size: 100% 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 2.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 4.375rem;
        }
        .box_1_right {
          display: flex;
          flex-direction: column;
          margin-left: 0.9375rem;
          .top {
            width: 13.125rem;
            height: 2.625rem;
            font-size: 1.5rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #222222;
            line-height: 2.625rem;
          }
          .bottom {
            width: 31.25rem;
            height: 8.25rem;
            font-size: 1rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #222222;
            line-height: 2.0625rem;
          }
        }
      }
      .box_2 {
        width: 37.5rem;
        height: 11.25rem;
        display: flex;
        .box_2_left {
          width: 4.375rem;
          height: 4.375rem;
          background-image: url('@/assets/images/fangbian.png');
          background-size: 100% 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 2.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 4.375rem;
        }
        .box_2_right {
          display: flex;
          flex-direction: column;
          margin-left: 0.9375rem;
          .top {
            width: 13.125rem;
            height: 2.625rem;
            font-size: 1.5rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #222222;
            line-height: 2.625rem;
          }
          .bottom {
            width: 31.25rem;
            height: 8.25rem;
            font-size: 1rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #222222;
            line-height: 2.0625rem;
          }
        }
      }
      .box_3 {
        width: 37.5rem;
        height: 11.25rem;
        display: flex;
        .box_3_left {
          width: 4.375rem;
          height: 4.375rem;
          background-image: url('@/assets/images/fangbian.png');
          background-size: 100% 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 2.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 4.375rem;
        }
        .box_3_right {
          display: flex;
          flex-direction: column;
          margin-left: 0.9375rem;
          .top {
            width: 13.125rem;
            height: 2.625rem;
            font-size: 1.5rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #222222;
            line-height: 2.625rem;
          }
          .bottom {
            width: 31.25rem;
            height: 8.25rem;
            font-size: 1rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #222222;
            line-height: 2.0625rem;
          }
        }
      }
    }
    .right {
      margin-top: 2.5rem;
      width: 37.5rem;
      display: flex;
      flex-direction: column;
      .box_1 {
        width: 37.5rem;
        height: 11.25rem;
        display: flex;
        .box_1_left {
          width: 4.375rem;
          height: 4.375rem;
          background-image: url('@/assets/images/fangbian.png');
          background-size: 100% 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 2.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 4.375rem;
        }
        .box_1_right {
          display: flex;
          flex-direction: column;
          margin-left: 0.9375rem;
          .top {
            width: 13.125rem;
            height: 2.625rem;
            font-size: 1.5rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #222222;
            line-height: 2.625rem;
          }
          .bottom {
            width: 31.25rem;
            height: 8.25rem;
            font-size: 1rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #222222;
            line-height: 2.0625rem;
          }
        }
      }
      .box_2 {
        width: 37.5rem;
        height: 11.25rem;
        display: flex;
        .box_2_left {
          width: 4.375rem;
          height: 4.375rem;
          background-image: url('@/assets/images/fangbian.png');
          background-size: 100% 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 2.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 4.375rem;
        }
        .box_2_right {
          display: flex;
          flex-direction: column;
          margin-left: 0.9375rem;
          .top {
            width: 13.125rem;
            height: 2.625rem;
            font-size: 1.5rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #222222;
            line-height: 2.625rem;
          }
          .bottom {
            width: 31.25rem;
            height: 8.25rem;
            font-size: 1rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #222222;
            line-height: 2.0625rem;
          }
        }
      }
      .box_3 {
        width: 37.5rem;
        height: 11.25rem;
        display: flex;
        .box_3_left {
          width: 4.375rem;
          height: 4.375rem;
          background-image: url('@/assets/images/fangbian.png');
          background-size: 100% 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 2.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 4.375rem;
        }
        .box_3_right {
          display: flex;
          flex-direction: column;
          margin-left: 0.9375rem;
          .top {
            width: 15rem;
            height: 2.625rem;
            font-size: 1.5rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #222222;
            line-height: 2.625rem;
          }
          .bottom {
            width: 31.25rem;
            height: 8.25rem;
            font-size: 1rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #222222;
            line-height: 2.0625rem;
          }
        }
      }
    }
  }
  .section_9 {
    width: 100%;
    background-color: #f8f8f8;
    max-width: 160rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-bottom: 9.375rem;
    .section_9_box1 {
      font-size: 1.875rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      color: #222222;
      line-height: 2.8125rem;
      text-align: center;
      position: relative;
      margin-bottom: 3.125rem;
      .zhe {
        position: absolute;
        bottom: 0;
        width: 13.125rem;
        height: 0.75rem;
        background: linear-gradient(90deg, rgba(72, 101, 255, 0.1) 0%, #4865ff 49%, rgba(72, 101, 255, 0.1) 100%);
      }
    }
    .section_9_box2 {
      width: 95rem;
      display: flex;
      justify-content: center;
      border-radius: 0.9375rem;
      .hujis {
        border-top-left-radius: 0.9375rem;
        border-top-right-radius: 0.9375rem;
        width: 21.875rem;
        height: 11.25rem;
      }
      .section_9_box2_1 {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-right: 2.5rem;
        width: 21.875rem;
        background: #ffffff;
        box-shadow: inset 0 0 0.4375rem 0 rgba(255, 255, 255, 0.5);
        border-radius: 0.9375rem;
        border: 0.0625rem solid #dedede;
        padding-bottom: 1.25rem;
        .box_1 {
          width: 80%;
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #222222;
          margin-top: 0.9375rem;
        }
        .box_2 {
          display: flex;
          margin-top: 1.25rem;
          .box_2_left {
            width: 3.125rem;
            height: 3.125rem;
            background-image: url('@/assets/images/rentou.png');
            background-size: 100% 100%;
          }
          .box_2_right {
            display: flex;
            flex-direction: column;
            margin-left: 0.9375rem;
            .box1 {
              width: 3rem;
              font-size: 1rem;
              font-family: PingFangSC, PingFang SC;
              font-weight: 500;
              color: #222222;
            }
            .box2 {
              width: 3rem;
              font-size: 1rem;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #666666;
            }
          }
        }
      }
      .section_9_box2_2 {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 21.875rem;
        background: #ffffff;
        box-shadow: inset 0 0 0.4375rem 0 rgba(255, 255, 255, 0.5);
        border-radius: 0.9375rem;
        border: 0.0625rem solid #dedede;
        padding-bottom: 1.25rem;
        .box_1 {
          width: 80%;
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #222222;
          margin-top: 0.9375rem;
        }
        .box_2 {
          width: 8.0625rem;
          display: flex;
          margin-top: 1.25rem;
          .box_2_left {
            width: 3.125rem;
            height: 3.125rem;
            background-image: url('@/assets/images/rentou.png');
            background-size: 100% 100%;
          }
          .box_2_right {
            display: flex;
            flex-direction: column;
            margin-left: 1.25rem;
            .box1 {
              width: 3rem;
              font-size: 1rem;
              font-family: PingFangSC, PingFang SC;
              font-weight: 500;
              color: #222222;
            }
            .box2 {
              width: 4rem;
              font-size: 1rem;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #666666;
            }
          }
        }
      }
      .section_9_box2_3 {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-right: 2.5rem;
        width: 21.875rem;
        background: #ffffff;
        box-shadow: inset 0 0 0.4375rem 0 rgba(255, 255, 255, 0.5);
        border-radius: 0.9375rem;
        border: 0.0625rem solid #dedede;
        padding-bottom: 1.25rem;
        .box_1 {
          width: 80%;
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #222222;
          margin-top: 0.9375rem;
        }
        .box_2 {
          width: 8.0625rem;
          display: flex;
          margin-top: 1.25rem;
          .box_2_left {
            width: 3.125rem;
            height: 3.125rem;
            background-image: url('@/assets/images/rentou.png');
            background-size: 100% 100%;
          }
          .box_2_right {
            display: flex;
            flex-direction: column;
            margin-left: 0.9375rem;
            .box1 {
              width: 3rem;
              font-size: 1rem;
              font-family: PingFangSC, PingFang SC;
              font-weight: 500;
              color: #222222;
            }
            .box2 {
              width: 4rem;
              font-size: 1rem;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #666666;
            }
          }
        }
      }
      .section_9_box2_4 {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-right: 2.5rem;
        width: 21.875rem;
        background: #ffffff;
        box-shadow: inset 0 0 0.4375rem 0 rgba(255, 255, 255, 0.5);
        border-radius: 0.9375rem;
        border: 0.0625rem solid #dedede;
        padding-bottom: 1.25rem;
        .box_1 {
          width: 80%;
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #222222;
          margin-top: 0.9375rem;
        }
        .box_2 {
          width: 8.0625rem;
          display: flex;
          margin-top: 1.25rem;
          .box_2_left {
            width: 3.125rem;
            height: 3.125rem;
            background-image: url('@/assets/images/rentou.png');
            background-size: 100% 100%;
          }
          .box_2_right {
            display: flex;
            flex-direction: column;
            margin-left: 0.9375rem;
            .box1 {
              width: 3rem;
              font-size: 1rem;
              font-family: PingFangSC, PingFang SC;
              font-weight: 500;
              color: #222222;
            }
            .box2 {
              width: 4rem;
              font-size: 1rem;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #666666;
            }
          }
        }
      }
    }
  }
  .section_10 {
    width: 100%;
    height: 35.625rem;
    background-image: url('@/assets/images/zixunbottom.png');
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    .section_10_box1 {
      width: 89.125rem;
      height: 38.5rem;
      margin-top: -5.75rem;
      background-image: url('@/assets/images/fangkuais.png');
      background-size: 100% 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      position: relative;
      .box_1 {
        position: absolute;
        top: 5.625rem;
        width: 8rem;
        height: 2.8125rem;
        font-size: 2rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #222222;
        line-height: 2.8125rem;
        position: relative;
        .zhe {
          position: absolute;
          bottom: 0;
          width: 8rem;
          height: 0.75rem;
          background: linear-gradient(90deg, rgba(72, 101, 255, 0.1) 0%, #4865ff 53%, rgba(72, 101, 255, 0.1) 100%);
        }
      }
      .box_2 {
        position: absolute;
        top: 15.6875rem;
        right: 20.0625rem;
        display: flex;
        align-items: center;
        .svg {
          width: 1.25rem;
          height: 1.25rem;
          margin-right: 0.9375rem;
        }
        .text {
          width: 6rem;
          height: 2.0625rem;
          font-size: 1.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #222222;
          line-height: 2.0625rem;
        }
      }
      .box_3 {
        position: absolute;
        top: 20.1875rem;
        right: 13.0625rem;
        width: 17.1875rem;
        height: 3.5rem;
        font-size: 2.5rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #4865ff;
        line-height: 3.5rem;
      }
    }
  }
}
</style>
