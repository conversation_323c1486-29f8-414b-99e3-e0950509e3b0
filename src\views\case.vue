<script setup>
import myFooter from '@/components/myFooter.vue'
import myHeader from '@/components/myHeader.vue'
import contactUs from '@/components/contactUs.vue'
import { ref, reactive, getCurrentInstance } from 'vue'
import { useRouter, useRoute } from 'vue-router'
const router = useRouter()
const route = useRoute()
const isTrue = ref('1')
const screenWidth = ref(document.body.clientWidth)
window.onresize = () => {
  // console.log('screenWidth', screenWidth.value);
  return (screenWidth.value = document.body.clientWidth)
}
function goDetail(num) {
  if (screenWidth.value < 1024) {
    console.log('num', num)
    router.push({ path: '/Detail', name: 'detail', query: { page: 'coop', id: num } })
  }
}
function goContact() {
  if (screenWidth.value < 1024) {
    router.push({ path: '/Detail', name: 'detail', query: { page: 'contact' } })
  }
}
const clickFun = val => {
  isTrue.value = val
}
</script>
<!-- coop -->
<template>
  <my-header />
  <div class="banner">
    <div class="h1">合作案例</div>
    <el-carousel :interval="4000" type="card" height="calc(31.25vw - 140px)">
      <el-carousel-item>
        <img src="@/assets/images/case-carousel1.png" alt="" class="img-carousel" />
      </el-carousel-item>
      <el-carousel-item>
        <img src="@/assets/images/case-carousel2.png" alt="" class="img-carousel" />
      </el-carousel-item>
      <el-carousel-item>
        <img src="@/assets/images/case-carousel3.png" alt="" class="img-carousel" />
      </el-carousel-item>
    </el-carousel>
  </div>
  <div class="special-title">卫软如何帮助客户取得成功</div>
  <div class="product-feature">
    <div class="w">
      <div class="feature-info">
        <span>AI智能+自研穿戴</span>
        <p>助力将就医速度提高2倍以上</p>
        <span>基层医防融合一体化平台</span>
        <p>将数据留档保存，大大降低数据流失度</p>
      </div>
    </div>
  </div>
  <div class="service">
    <div class="title">
      <div class="title-label">服务案例</div>
      <div class="title-en">SERVICE CASES</div>
    </div>
    <!-- service-case -->
    <div class="service-content">
      <div class="w">
        <div class="top-service">
          <div class="left">
            <img src="@/assets/images/service-case1.png" class="case1" alt="" />
            <img src="@/assets/images/service-case2.png" class="case2" alt="" />
          </div>
          <div class="centre-service">
            <img src="@/assets/images/service-case3.png" class="case3" alt="" />
            <div class="mini-title" style="margin-left: 35%; margin-top: 6%">
              <div class="mini-title-label" >面对面服务</div>
              <div class="mini-title-en">Face-to-face service</div>
              <div class="text">现场培训，需求定制</div>
            </div>
          </div>
          <div class="right">
            <div class="mini-title">
              <div class="mini-title-label">远程服务</div>
              <div class="mini-title-en">Remote service</div>
              <div class="text">实时互动，专业知识分享</div>
            </div>
            <img src="@/assets/images/service-case4.png" class="case4" alt="" />
            <img src="@/assets/images/service-case5.png" class="case5" alt="" />
          </div>
        </div>
        <div class="bottom-service">
          <div class="mini-title">
            <div class="mini-title-label">临床指导</div>
            <div class="mini-title-en">Clinical guidance</div>
            <div class="text">临床诊断，学术交流</div>
          </div>
          <img src="@/assets/images/service-case6.png" class="case6" alt="" />
          <img src="@/assets/images/service-case7.png" class="case7" alt="" />
        </div>
      </div>
    </div>
  </div>
  <div class="title">
    <div class="title-label">精选案例</div>
    <div class="title-en">SELECTED CASES</div>
  </div>
  <div class="selected">
    <div class="w">
      <div class="selected-head">
        <div :class="isTrue === '1' ? 'selected-item active' : 'selected-item'" @click="clickFun('1')">
          <img src="@/assets/images/anl7.png" alt="" />
          <div class="label">星网计划</div>
          <div class="text">南京医科大学附属泰州人民医院</div>
        </div>
        <div :class="isTrue === '2' ? 'selected-item active' : 'selected-item'" @click="clickFun('2')">
          <img src="@/assets/images/anl3.png" alt="" />
          <div class="label">村医网</div>
          <div class="text">江苏省人民医院</div>
        </div>
        <div :class="isTrue === '3' ? 'selected-item active' : 'selected-item'" @click="clickFun('3')">
          <img src="@/assets/images/anl8.png" alt="" />
          <div class="label">医改3.0</div>
          <div class="text">深圳市罗湖区人民医院</div>
        </div>
      </div>
      <div v-show="isTrue === '1'" class="selected-info">
        <img src="@/assets/images/case-selected1.png" class="selected1" alt="" />
        <div class="intro">
          <div class="intro-label">项目简介</div>
          <div class="intro-text">
            “星网计划”成功搭建基层医疗数智化平台服务体系，提供多种智能医疗设备，满足基层医疗服务需求。目前平台已部署泰州市三甲医院以及50多家卫生服务中心和村卫生室，预计2024年内覆盖泰州全市基层医疗。
          </div>
        </div>
        <div class="selected-content">
          <div class="text-content">
            平台建设方案得到常委会副委员长肯定，并在工作会议中建议加大推广力度。<br />
            万市长表示：星网计划，助力泰州构建“市、县、乡、村”全区域以及“防、筛、管、治、研”全流程一体化防治体系，打造
            “泰州模式”。
          </div>
          <div class="img-box selected2">
            <img src="@/assets/images/case-selected2.png" class="" alt="" />
            <span>国家十三届全国人大常委会副委员长莅临参观</span>
          </div>
          <div class="img-box selected3">
            <img src="@/assets/images/case-selected3.png" alt="" />
            <span>泰州市市长和泰州市人民医院党委书记莅临参观</span>
          </div>
        </div>
      </div>
      <div v-show="isTrue === '2'" class="selected-info">
        <img src="@/assets/images/case1-selected1.png" class="selected1" alt="" />
        <div class="intro">
          <div class="intro-label">项目简介</div>
          <div class="intro-text">
            “村医网”是江苏省卫健委、南京医科大学开展实现移动健康在基层医防融合体系中的应用与探索的项目，由卫软负责整体开发与管理工作，项目通过可穿戴设备对患者进行健康管理，打造“智慧村医网”江都模式。
          </div>
        </div>
        <div class="selected-content">
          <div class="text-content">
            与卫健委合作，已在江苏省人民医院正式应用。<br />
            平台自动生成患者的体检报告，70%以上的数据均由物联网和穿戴设备主动上传，极大减轻了医护人员的工作量和后期数据维护。
          </div>
          <div class="img-box selected2">
            <img src="@/assets/images/case1-selected2.png" class="" alt="" />
            <span>国内医疗专家参观学习</span>
          </div>
          <div class="img-box selected3">
            <img src="@/assets/images/case1-selected3.png" alt="" />
            <span>“江都模式”专家沙龙</span>
          </div>
        </div>
      </div>
      <div v-show="isTrue === '3'" class="selected-info">
        <img src="@/assets/images/case2-selected1.png" class="selected1" alt="" />
        <div class="intro">
          <div class="intro-label">项目简介</div>
          <div class="intro-text">
            项目于2022年11月开始参与深圳罗湖医改3.0，将此方案应用到“深圳市罗湖区人民医院”集团一期基层智慧监测平台。
          </div>
        </div>
        <div class="selected-content">
          <div class="text-content">
            方案已全面应用于深圳市罗湖区人民医院集团一期基层智慧监测平台。<br />
            罗湖医院林院长向深圳市各位领导介绍了“卫软+华为”研发的穿戴院外监测与服务平台。
          </div>
          <div class="img-box selected2">
            <img src="@/assets/images/case2-selected2.png" class="" alt="" />
            <span>国家卫健委科教司梁组长带队参观</span>
          </div>
          <div class="img-box selected3">
            <img src="@/assets/images/case2-selected3.png" alt="" />
            <span>香港医务局卢局长带队考察罗湖医改和个人健康管理</span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <my-footer />
</template>
<style lang="scss" scoped>
.banner {
  width: 100%;
  max-width: 2560px;
  margin: 56px auto 0;
  // height: 31.25vw;
  overflow: auto;
  background-image: url('@/assets/images/case-banner-background.png');
  background-size: 100% 100%;
  .h1 {
    position: relative;
    width: 200px;
    display: block;
    margin: 40px auto 30px;
    font-size: 36px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    text-align: center;
    color: #222;
    line-height: 70px;
    z-index: 10;
    &::before {
      content: '';
      position: absolute;
      top: calc(50% - 3px);
      left: -50%;
      width: 80px;
      height: 6px;
      background: linear-gradient(270deg, #0281e0 0%, rgba(2, 129, 224, 0) 100%);
    }
    &::after {
      content: '';
      position: absolute;
      top: calc(50% - 3px);
      right: -50%;
      width: 80px;
      height: 6px;
      background: linear-gradient(90deg, #0281e0 0%, rgba(2, 129, 224, 0) 100%);
    }
  }
  .img-carousel {
    width: 41.66vw;
    height: 20.83vw;
  }
  :deep(.el-carousel__item--card) {
    width: 41.66vw;
    height: 20.83vw;
  }
}
.special-title {
  position: relative;
  width: 384px;
  margin: 70px auto 40px;
  font-size: 32px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  color: #222222;
  line-height: 56px;
  &::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 12px;
    background: linear-gradient(90deg, rgba(2, 129, 224, 0.1) 0%, #0281e0 48%, rgba(2, 129, 224, 0.1) 100%);
  }
}
.product-feature {
  width: 100%;
  max-width: 2560px;
  height: 31.25vw;
  max-height: 800px;
  margin: 0 auto 0;
  background-image: url('@/assets/images/case-product-feature.png');
  background-size: 100% 100%;
  display: flex;
  align-items: center;
  .feature-info {
    width: 46%;
    background-color: #fff;
    border-radius: 15px;
    padding: 35px 50px;
    span {
      font-size: 20px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #222222;
      line-height: 42px;
      margin-bottom: 20px;
    }
    p {
      font-size: 24px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      color: #f47d4c;
      line-height: 56px;
      margin-bottom: 40px;
    }
  }
}
.title {
  margin: 70px 0 40px;
  font-weight: 500;
  font-size: 24px;
  color: #3b3f47;
  text-align: center;
  .title-label {
    display: inline-block;
    position: relative;
    font-size: 32px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    color: #222222;
    line-height: 56px;
    padding-bottom: 14px;
    &::after {
      content: '';
      width: 50px;
      height: 4px;
      background-color: #0281e0;
      position: absolute;
      left: calc(50% - 25px);
      bottom: 0;
    }
  }
  .title-en {
    font-size: 16px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    color: #666666;
    line-height: 28px;
    margin-top: 10px;
  }

  @media screen and (max-width: 1024px) {
    margin: 24px 0 8px;
    font-size: 16px;
  }
}
.mini-title {
  font-weight: 500;
  font-size: 24px;
  color: #3b3f47;
  text-align: left;
  .mini-title-label {
    display: inline-block;
    position: relative;
    font-size: 24px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    color: #222222;
    line-height: 33px;
    
  }
  .mini-title-en {
    font-size: 16px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    color: #999999;
    line-height: 22px;
    margin-top: 10px;
  }
  .text {
    margin-top: 20px;
    font-size: 16px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    color: #222222;
    line-height: 22px;
  }
}
.service {
  position: relative;
  width: 100%;
  max-width: 2560px;
  margin: 0 auto 0;
  // overflow: auto;
  .service-content {
    background-color: #fff;
    .top-service {
      display: flex;
      margin-bottom: -10%;
      .left {
        width: 33.783%;
        margin-right: -6.756%;
        .case1 {
          width: 100%;
          height: auto;
        }
        .case2 {
          width: 80%;
          height: auto;
        }
      }
    }
    .centre-service {
      margin-top: 6.756%;
      width: 33.783%;
      text-align: center;
      .case3 {
        width: 100%;
        height: auto;
      }
    }
    .right {
      width: 34.45%;
      margin-left: 4.7%;
      position: relative;
      .mini-title {
        width: 88%;
        position: relative;
        z-index: 5;
        margin-top: 10%;
        background-color: #fff;
        padding-bottom: 3%;
        border-radius: 0px 0px 70px 0px;
        text-align: center;
      }
      .case4 {
        width: 100%;
        position: relative;
        top: -16.5%;
      }
      .case5 {
        width: 78%;
        position: absolute;
        bottom: 0;
        left: 0;
      }
    }
    .bottom-service {
      // display: flex;
      // align-content: center;
      // margin-bottom: -10%;
      .mini-title {
        position: relative;
        top: -240px;
        z-index: 5;
        display: inline-block;
        padding: 50px 150px 100px 70px;
        white-space: nowrap;
        border-radius: 0px 0px 70px 0px;
        background-color: #fff;
        left: -120px;
      }
      .case6 {
        width: 47.2%;
        height: auto;
        position: relative;
        left: -20%;
      }
      .case7 {
        position: absolute;
        width: 33.78%;
        height: auto;
        left: 50%;
        bottom: 17.5%;
      }
    }
  }
}
.selected {
  background-image: url('@/assets/images/selected-cases-background.png');
  background-repeat: no-repeat;
  background-size: 100% auto;
  background-color: #f3f7ff;
  overflow: auto;
  .selected-head {
    margin: 40px 0 80px;
    display: flex;
    justify-content: space-between;
    .selected-item {
      width: 29%;
      height: auto;
      text-align: center;
      background: #ffffff;
      border-radius: 15px;
      padding: 50px 0 45px;
      cursor: pointer;
      &.active {
        background-color: #4865ff;
        color: #fff;
      }
      img {
        width: 60px;
        height: 60px;
        background: #ffffff;
        box-shadow: 0px 1px 7px 0px rgba(0, 0, 0, 0.1);
        border-radius: 20px;
        padding: 9px;
      }
      .label {
        font-size: 24px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        line-height: 33px;
      }
      .text {
        margin-top: 10px;
        font-size: 16px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        line-height: 22px;
      }
      &:nth-child(2) {
        position: relative;
        top: 40px;
      }
    }
  }
  .selected-info {
    position: relative;
    margin-bottom: 70px;
    .selected1 {
      width: 47.29%;
      margin: 40px;
      height: 480px;
    }
    .intro {
      width: 56%;
      position: absolute;
      right: 0;
      top: 10%;
      padding: 40px 70px;
      background: #f3f3f3;
      border-radius: 15px;
      .intro-label {
        font-size: 24px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #222222;
        line-height: 33px;
      }
      .intro-text {
        margin-top: 20px;
        font-size: 16px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #666666;
        line-height: 26px;
      }
    }
    .selected-content {
      width: 100%;
      padding: 24px 0;
      position: relative;
      background: #ffffff;
      border-radius: 15px;
      .text-content {
        width: 49%;
        padding-left: 30px;
        font-size: 16px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #222222;
        line-height: 33px;
      }
      .selected2 {
        width: 21%;
        position: absolute;
        right: 23%;
        bottom: 40px;
        img {
          width: 100%;
        }
        span {
          position: absolute;
          bottom: 19px;
          left: 0;
          right: 0;
          margin: auto;
          text-align: center;
          width: 265px;
          z-index: 5;
          font-size: 16px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #ffffff;
          line-height: 22px;
        }
      }
      .selected3 {
        width: 21%;
        position: absolute;
        right: 0;
        bottom: 40px;
        img {
          width: 100%;
        }
        span {
          position: absolute;
          bottom: 19px;
          left: 0;
          right: 0;
          margin: auto;
          text-align: center;
          z-index: 5;
          font-size: 16px;
          width: 275px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #ffffff;
          line-height: 22px;
        }
      }
    }
  }
}
</style>
