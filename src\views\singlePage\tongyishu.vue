<template>
  <div class="box">
    <canvas id="pdf-render1" class="pdfRenderBox" />
    <br>
    <canvas id="pdf-render2" class="pdfRenderBox" />
    <!-- <a id="aTarget" ref="myLink" href="https://taizhou.cunyinet.cn/base-1259672728/videoReport/%E8%BE%B9%E4%BC%AF%E8%B4%A4_1703603630853754882/65703a9b8e12bd3733939819.pdf" style="display: none;" download="江苏省人民医院患者使用知情同意书.pdf" target="_blank" /> -->
  </div>
</template>

<script setup>
import pdfDocItem from '/document/wearAgreement.pdf'
import { onMounted, reactive } from 'vue'
const pdfParams = reactive({
  pageNumber: 1, // 当前页
  total: 0 // 总页数
})

// 不要定义为ref或reactive格式，就定义为普通的变量
let pdfDoc = null
// 这里必须使用异步去引用pdf文件，直接去import会报错，也不知道为什么
onMounted(async() => {
  const pdfjs = await import('pdfjs-dist/build/pdf')
  const pdfjsWorker = await import('pdfjs-dist/build/pdf.worker.entry')
  pdfjs.GlobalWorkerOptions.workerSrc = pdfjsWorker
  // 此文件位于public/test2.pdf
  // const url = ref(pdfDocItem)
  pdfjs.getDocument(pdfDocItem).promise.then(doc => {
    pdfDoc = doc
    pdfParams.total = doc.numPages
    initPdf()
  })
  // downloadUrl()
})

// const downloadUrl = () => {
//   // 使用 ref 创建一个响应式的数据引用
//   const aTarget = document.querySelector('#aTarget')
//   aTarget.click()
// }

const initPdf = () => {
  getPdfPage(1)
  getPdfPage(2)
}

// 加载pdf的某一页
const getPdfPage = number => {
  const _scale = 3
  pdfDoc.getPage(number).then(page => {
    const viewport = page.getViewport({ scale: _scale })
    const canvas = document.getElementById('pdf-render' + number)
    const context = canvas.getContext('2d')
    // 宽
    const STATIC_WIDTH = window.innerWidth * _scale
    const STATIS_HEIGHT = (STATIC_WIDTH / viewport.viewBox[2]) * viewport.viewBox[3]
    // 高
    viewport.width = STATIC_WIDTH
    viewport.height = STATIS_HEIGHT

    // 设置 Canvas 大小以适应屏幕宽度
    canvas.width = STATIC_WIDTH
    canvas.height = STATIS_HEIGHT
    canvas.style.width = Math.floor(STATIC_WIDTH / _scale) + 'px'
    canvas.style.height = Math.floor(STATIS_HEIGHT / _scale) + 'px'

    const renderContext = {
      canvasContext: context,
      viewport: page.getViewport({ scale: STATIC_WIDTH / viewport.viewBox[2] }),
      // 这里transform的六个参数，使用的是transform中的Matrix(矩阵)
      transform: [1, 0, 0, -1, 0, viewport.height]
    }
    // 进行渲染
    page.render(renderContext)
  })
}
</script>

<style lang="scss" scoped>
.pdfRenderBox {
  transform: scaleX(-1) rotateX(180deg) rotateY(180deg) !important;
  margin-right: -16px;
}
</style>
