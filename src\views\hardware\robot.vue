<script setup>
import { ref } from 'vue'
const isTrue = ref(true)
const isTrues = ref(true)
const isActive = ref('1')
const active = ref(1)
const ActiveFun = val => {
  isActive.value = val
}
const clickFun = () => {
  isTrue.value = !isTrue.value
}
const clickFuns = () => {
  isTrues.value = !isTrues.value
}
const ActiveFuns = val => {
  active.value = val
}
const ActiveFunes = index => {
  if (index === '加') {
    if (active.value === 5) {
      active.value = 1
    } else {
      active.value += 1
    }
  } else {
    if (active.value === 1) {
      active.value = 5
    } else {
      active.value -= 1
    }
  }
}
</script>
<template>
  <div class="banner">
    <div class="section_1"></div>
    <div class="section_2">
      <span class="text_10">智能硬件产品助您超越所能</span>
      <div class="section_6_1"></div>
      <span class="text_11">INTELLIGENT&nbsp;HARDWARE</span>
      <div v-show="active === 1" class="list_3">
        <div class="text-group_1-0" @click="ActiveFuns(1)">
          <span class="text_12-0">AI基层辅助机器人</span>
          <span class="text_13-0">人脸识别 自助问诊 远程诊断</span>
          <img
            class="image_4-0"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng3b9a8121db0a1bcc7c1a65f5a79361a0bbbf877f3a659cc948ad087a9b9f03cb"
          />
        </div>
        <div class="text-group_1-1" @click="ActiveFuns(2)">
          <span class="text_12-1">卫软穿戴设备</span>
          <span class="text_13-1">实时监测 安心保障</span>
          <img
            class="image_2-1"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
        <div class="text-group_1-2" @click="ActiveFuns(3)">
          <span class="text_12-2">健康检测一体机</span>
          <span class="text_13-2">智慧体检 一机搞定</span>
          <img
            class="image_2-2"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
          <img
            class="image_3-2"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
        <div class="text-group_1-3" @click="ActiveFuns(4)">
          <span class="text_12-3">智慧随访包</span>
          <span class="text_13-3">一机多用 实时上传</span>
          <img
            class="image_3-3"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
      </div>
      <div v-show="active === 2" class="list_3">
        <div class="text-group_1-0" @click="ActiveFuns(2)">
          <span class="text_12-0">卫软穿戴设备</span>
          <span class="text_13-0">实时监测&nbsp;安心保障</span>
          <img
            class="image_4-0"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng3b9a8121db0a1bcc7c1a65f5a79361a0bbbf877f3a659cc948ad087a9b9f03cb"
          />
        </div>
        <div class="text-group_1-1" @click="ActiveFuns(3)">
          <span class="text_12-1">健康检测一体机</span>
          <span class="text_13-1">智慧体检&nbsp;一机搞定</span>
          <img
            class="image_2-1"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
        <div class="text-group_1-2" @click="ActiveFuns(4)">
          <span class="text_12-2">智彗随访包</span>
          <span class="text_13-2">一机多用&nbsp;实时上传</span>
          <img
            class="image_2-2"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
          <img
            class="image_3-2"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
        <div class="text-group_1-3" @click="ActiveFuns(5)">
          <span class="text_12-3">边缘计算网关</span>
          <span class="text_13-3">万能互联&nbsp;实时监控</span>
          <img
            class="image_3-3"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
      </div>
      <div v-show="active === 3" class="list_3">
        <div class="text-group_1-0" @click="ActiveFuns(3)">
          <span class="text_12-0">健康检测一体机</span>
          <span class="text_13-0">智慧体检 一机搞定</span>
          <img
            class="image_4-0"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng3b9a8121db0a1bcc7c1a65f5a79361a0bbbf877f3a659cc948ad087a9b9f03cb"
          />
        </div>
        <div class="text-group_1-1" @click="ActiveFuns(4)">
          <span class="text_12-1">智慧随访包</span>
          <span class="text_13-1">一机多用 实时上传</span>
          <img
            class="image_2-1"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
        <div class="text-group_1-2" @click="ActiveFuns(5)">
          <span class="text_12-2">边缘计算网关</span>
          <span class="text_13-2">万物互联 实时监控</span>
          <img
            class="image_2-2"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
          <img
            class="image_3-2"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
        <div class="text-group_1-3" @click="ActiveFuns(1)">
          <span class="text_12-3">AI基层辅助机器人</span>
          <span class="text_13-3">人脸识别 自助问诊 远程诊断</span>
          <img
            class="image_3-3"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
      </div>
      <div v-show="active === 4" class="list_3">
        <div class="text-group_1-0" @click="ActiveFuns(4)">
          <span class="text_12-0">智慧随访包</span>
          <span class="text_13-0">一机多用 实时上传</span>
          <img
            class="image_4-0"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng3b9a8121db0a1bcc7c1a65f5a79361a0bbbf877f3a659cc948ad087a9b9f03cb"
          />
        </div>
        <div class="text-group_1-1" @click="ActiveFuns(5)">
          <span class="text_12-1">边缘计算网关</span>
          <span class="text_13-1">万物互联 实时监控</span>
          <img
            class="image_2-1"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
        <div class="text-group_1-2" @click="ActiveFuns(1)">
          <span class="text_12-2">AI基层辅助机器人</span>
          <span class="text_13-2">人脸识别 自助问诊 远程诊断</span>
          <img
            class="image_2-2"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
          <img
            class="image_3-2"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
        <div class="text-group_1-3" @click="ActiveFuns(2)">
          <span class="text_12-3">卫软穿戴设备</span>
          <span class="text_13-3">实时监测 安心保障</span>
          <img
            class="image_3-3"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
      </div>
      <div v-show="active === 5" class="list_3">
        <div class="text-group_1-0" @click="ActiveFuns(5)">
          <span class="text_12-0">边缘计算网关</span>
          <span class="text_13-0">万物互联 实时监控</span>
          <img
            class="image_4-0"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng3b9a8121db0a1bcc7c1a65f5a79361a0bbbf877f3a659cc948ad087a9b9f03cb"
          />
        </div>
        <div class="text-group_1-1" @click="ActiveFuns(1)">
          <span class="text_12-1">AI基层辅助机器人</span>
          <span class="text_13-1">人脸识别 自助问诊 远程诊断</span>
          <img
            class="image_2-1"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
        <div class="text-group_1-2" @click="ActiveFuns(2)">
          <span class="text_12-2">卫软穿戴设备</span>
          <span class="text_13-2">实时监测 安心保障</span>
          <img
            class="image_2-2"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
          <img
            class="image_3-2"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
        <div class="text-group_1-3" @click="ActiveFuns(3)">
          <span class="text_12-3">健康检测一体机</span>
          <span class="text_13-3">智慧体检 一机搞定</span>
          <img
            class="image_3-3"
            referrerpolicy="no-referrer"
            src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng836d2e97a8ce3f8d665a3954f4311e1b70f2e49b5f6dec379d9566b719c19975"
          />
        </div>
      </div>
    </div>
    <div v-show="active === 1" class="section_3">
      <img
        class="image_6"
        referrerpolicy="no-referrer"
        src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng5b727b1da8591b50a16f335462b668ed3c9d990d7539f3741af9ebde6c96b421"
        @click="ActiveFunes('减')"
      />
      <div class="group_2">
        <img src="@/assets/images/jiqiqiren.png" alt="" />
      </div>
      <div class="box_31">
        <span class="text_14">AI基层辅助机器人</span>
        <div class="section_8"></div>
        <span class="text_15"
          >科技元素赋能基层实用场景，机器人支持无人值守模式，支持动线引导、人脸身份识别、疾病问诊、远程诊断等，培养患者习惯，减少人力成本。</span
        >
      </div>
      <img
        class="image_7"
        referrerpolicy="no-referrer"
        src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPngce59f9644e820397797e92e07d3796333123469c09e7b719e032226453706ed3"
        @click="ActiveFunes('加')"
      />
    </div>
    <div v-show="active === 2" class="section_3">
      <img
        class="image_6"
        referrerpolicy="no-referrer"
        src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng5b727b1da8591b50a16f335462b668ed3c9d990d7539f3741af9ebde6c96b421"
        @click="ActiveFunes('减')"
      />
      <div class="group_2">
        <img src="@/assets/images/shoubiao1.png" alt="" />
      </div>
      <div class="box_31">
        <span class="text_14">卫软穿戴设备</span>
        <div class="section_8"></div>
        <span class="text_15"
          >卫软手表定制，支持单导心电、心率、PPG血压监测、血氧、睡眠、体温、GPS、4G通信模块等；卫软同时支持华为、OPPO等一线厂家品牌开发接入。</span
        >
      </div>
      <img
        class="image_7"
        referrerpolicy="no-referrer"
        src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPngce59f9644e820397797e92e07d3796333123469c09e7b719e032226453706ed3"
        @click="ActiveFunes('加')"
      />
    </div>
    <div v-show="active === 3" class="section_3">
      <img
        class="image_6"
        referrerpolicy="no-referrer"
        src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng5b727b1da8591b50a16f335462b668ed3c9d990d7539f3741af9ebde6c96b421"
        @click="ActiveFunes('减')"
      />
      <div class="group_2">
        <img src="@/assets/images/jixiebi.png" alt="" />
      </div>
      <div class="box_31">
        <span class="text_14">健康信息工作站</span>
        <div class="section_8"></div>
        <span class="text_15"
          >精准之道，内外兼修。高端配置高度集成一体化设计，健康宣教和自助体检结合，30多项健康检测指标，集秒级快检、健康指导、风险评估为一体，助力就诊、体检等多个场景，数据实时上传与统计。</span
        >
      </div>
      <img
        class="image_7"
        referrerpolicy="no-referrer"
        src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPngce59f9644e820397797e92e07d3796333123469c09e7b719e032226453706ed3"
        @click="ActiveFunes('加')"
      />
    </div>
    <div v-show="active === 4" class="section_3">
      <img
        class="image_6"
        referrerpolicy="no-referrer"
        src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng5b727b1da8591b50a16f335462b668ed3c9d990d7539f3741af9ebde6c96b421"
        @click="ActiveFunes('减')"
      />
      <div class="group_2">
        <img src="@/assets/images/yiliaoxiang.png" alt="" />
      </div>
      <div class="box_31">
        <span class="text_14">智慧随访包</span>
        <div class="section_8"></div>
        <span class="text_15"
          >支持多种检测设备，包含尿液、血糖、血压、体温、心电图（六导、十二导）、支持中医体质辨识、身份识别、血氧、脉率等检测，手提箱设计，数据实时同步。</span
        >
      </div>
      <img
        class="image_7"
        referrerpolicy="no-referrer"
        src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPngce59f9644e820397797e92e07d3796333123469c09e7b719e032226453706ed3"
        @click="ActiveFunes('加')"
      />
    </div>
    <div v-show="active === 5" class="section_3">
      <img
        class="image_6"
        referrerpolicy="no-referrer"
        src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng5b727b1da8591b50a16f335462b668ed3c9d990d7539f3741af9ebde6c96b421"
        @click="ActiveFunes('减')"
      />
      <div class="group_2">
        <img src="@/assets/images/wifihe.png" alt="" />
      </div>
      <div class="box_31">
        <span class="text_14">边缘计算网关</span>
        <div class="section_8"></div>
        <span class="text_15"
          >超低功耗边缘计算网关，支持多种经典协议接入，支持有线、无线接入，支持设备定位、开关机监测、距离监测。</span
        >
      </div>
      <img
        class="image_7"
        referrerpolicy="no-referrer"
        src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPngce59f9644e820397797e92e07d3796333123469c09e7b719e032226453706ed3"
        @click="ActiveFunes('加')"
      />
    </div>
    <div class="section_4">
      <div class="section_4_box1">产品优势</div>
      <div class="section_4_box2"></div>
      <div class="section_4_box3">PRODUCT ADVANTAGES</div>
      <div class="section_4_box4">
        <div class="section_4_box4_top">
          <div class="box">
            <div class="left"></div>
            <div class="right">
              <div class="box_s">语音识别准</div>
              <div class="box_s1">内嵌防火墙功能和各种加密方案，实现全方位防护，高效的同时，用的更安心。</div>
            </div>
          </div>
          <div class="box_1">
            <div class="left"></div>
            <div class="right">
              <div class="box_s">智能导航</div>
              <div class="box_s1">机器人可灵活规划路径，适应复杂室内环境。</div>
            </div>
          </div>
        </div>
        <div class="section_4_box4_bottom">
          <div class="box">
            <div class="left"></div>
            <div class="right">
              <div class="box_s">自主避障</div>
              <div class="box_s1">机器人在路径规划过程中，会通过传感器自主避障，避免与人发生接触，安全可靠。</div>
            </div>
          </div>
          <div class="box_1">
            <div class="left"></div>
            <div class="right">
              <div class="box_s">医疗能力</div>
              <div class="box_s1">
                机器人支持多种医疗检测能力，如心电检测、体温检测等，支持通过语音会话生成结构化电子病历，支持生成推荐处方。
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="section_5">
      <div class="section_5_box1">产品特色</div>
      <div class="section_5_box2"></div>
      <div class="section_5_box3">PRODUCT ADVANTAGES</div>
      <div class="section_5_box4">
        <div class="box1"></div>
        <div class="box2">
          <span class="hao">人脸识别 快速锁定</span>
        </div>
        <div class="box3">
          <span class="haos"> 问卷调查 对话互动 </span>
        </div>
        <div class="box4">
          <span class="hao_2"> 检查结果 语音播报 </span>
        </div>
        <div class="box5">
          <span class="hao_1"> 无人值守模式 </span>
        </div>
      </div>
    </div>
    <div class="section_6">
      <div class="section_6_box">
        <div class="section_6_box_top">AI智能 科技领先</div>
        <div class="section_6_box_bottom">
          通过机器人多传感器的联动配合，给用户一个惊喜的人机交互体验，大大节省了人力、物力。
        </div>
      </div>
    </div>
    <div class="section_7">
      <div class="section_5_box1">产品介绍</div>
      <div class="section_5_box2"></div>
      <div class="section_5_box3">PRODUCT ADVANTAGES</div>
      <div class="section_5_box4">
        <div class="section_5_box4_left"></div>
        <div class="section_5_box4_right">
          <div class="box">
            <div class="hao"></div>
            <div class="hao_1">打造科技诊室</div>
          </div>
          <div class="box_1">
            <div>有人/无人双模式一键切换</div>
            <div>
              患者走到机器人面前，机器人自动进行人脸识别，识别成功后，带领患者进行一系列检查，支持自动/手动触发远程会诊。
            </div>
          </div>
        </div>
      </div>
      <div class="section_5_box5">
        <div class="section_5_box4_left">
          <div class="box">
            <div class="hao"></div>
            <div class="hao_1">高质量的语音识别</div>
          </div>
          <div class="box_1">
            能够在嘈杂的诊室环境中智能区分患者对话，支持语音关闭/打开功能，在智能语音同时，有效保护患者隐私。
          </div>
        </div>
        <div class="section_5_box4_right"></div>
      </div>
      <div class="section_5_box6">
        <div class="section_5_box4_left"></div>
        <div class="section_5_box4_right">
          <div class="box">
            <div class="hao"></div>
            <div class="hao_1">智能的数据同步</div>
          </div>
          <div class="box_1">
            针对不同测量设备，通过蓝牙等方式进行连接，快速进行数据同步，支持能够筛查异常数据并进行提醒及标注。
          </div>
        </div>
      </div>
      <div class="section_5_box7">
        <div class="section_5_box4_left">
          <div class="box">
            <div class="hao"></div>
            <div class="hao_1">智能处方推荐</div>
          </div>
          <div class="box_1">
            在医生问诊结束后，诊室听译机器人屏幕将会显示完整对话内容和所有检查数据。支持线上医生远程开具处方/现场医生语音识别后自动填写诊断结果，采集过程及数据将实时同步到后台系统。
          </div>
        </div>
        <div class="section_5_box4_right"></div>
      </div>
    </div>
    <div class="section_8">
      <div class="left">
        <div class="box_1">产 品 图 片</div>
        <div class="box_2"></div>
        <div class="box_3">PRODUCT ADVANTAGES</div>
        <div class="box_4">多场景应用机器人</div>
        <div class="box_5">
          <div class="boxLeft">患者回访｜智能引导｜基本检查｜医疗诊断</div>
          <div v-show="isActive === '1'" class="boxright">01</div>
          <div v-show="isActive === '2'" class="boxright">02</div>
          <div v-show="isActive === '3'" class="boxright">03</div>
          <div v-show="isActive === '4'" class="boxright">04</div>
        </div>
      </div>
      <div class="right">
        <div class="box_1">
          <img
            v-show="isActive === '1'"
            src="@/assets/images/renlian.png"
            alt=""
            style="width: 43.75rem; height: 24.5625rem"
          />
          <img
            v-show="isActive === '2'"
            src="@/assets/images/renliantwo.png"
            alt=""
            style="width: 43.75rem; height: 24.5625rem"
          />
          <img
            v-show="isActive === '3'"
            src="@/assets/images/renlianthree.png"
            alt=""
            style="width: 43.75rem; height: 24.5625rem"
          />
          <img
            v-show="isActive === '4'"
            src="@/assets/images/renlianfore.png"
            alt=""
            style="width: 43.75rem; height: 24.5625rem"
          />
        </div>
        <div class="box_2">
          <template v-if="isActive === '1'">
            <span class="box_2_1">患者回访</span>
            <span class="box_2_2">机器人主动对患者进行随访，并把数据同步到PC端。</span>
          </template>
          <template v-if="isActive === '2'">
            <span class="box_2_1">智能引导</span>
            <span class="box_2_2">机器人带领患者到指定位置，并语音引导患者进行检查。</span>
          </template>
          <template v-if="isActive === '3'">
            <span class="box_2_1">基本检查</span>
            <span class="box_2_2">机器人与多种检查设备互通，进行数据同步及智能分析。</span>
          </template>
          <template v-if="isActive === '4'">
            <span class="box_2_1">医疗诊断</span>
            <span class="box_2_2">机器人支持远程会诊、处方开具、语音识别等功能。</span>
          </template>
        </div>
      </div>
      <div class="banners">
        <div :class="isActive === '1' ? 'active' : 'box_1'" @mouseover="ActiveFun('1')"></div>
        <div :class="isActive === '2' ? 'active' : 'box_2'" @mouseover="ActiveFun('2')"></div>
        <div :class="isActive === '3' ? 'active' : 'box_3'" @mouseover="ActiveFun('3')"></div>
        <div :class="isActive === '4' ? 'active' : 'box_4'" @mouseover="ActiveFun('4')"></div>
      </div>
    </div>
    <div class="section_9">
      <div class="section_7_box1">基本参数</div>
      <div class="section_7_box2"></div>
      <div class="section_7_box3">PRODUCT ADVANTAGES</div>
      <div class="section_7_box4">
        <div class="box_1">外观参数</div>
        <img
          v-if="isTrue"
          src="@/assets/images/shangla.png"
          alt=""
          style="width: 1.375rem; height: 0.75rem; margin-right: 4.375rem"
          @click="clickFun"
        />
        <img
          v-else
          @click="clickFun"
          src="@/assets/images/xiala.png"
          alt=""
          style="width: 1.375rem; height: 0.75rem; margin-right: 4.375rem"
        />
      </div>
      <div v-show="isTrue" class="section_7_box4_2">
        <div class="box_1">
          <span class="text_1"> 尺寸 </span>
          <span class="text_2">780*560*1500mm</span>
        </div>
        <div class="box_1">
          <span class="text_1"> 重量 </span>
          <span class="text_2">45kg</span>
        </div>
        <div class="box_1">
          <span class="text_1"> 屏幕尺寸 </span>
          <span class="text_2">13.3/21.5inch</span>
        </div>
        <div class="box_1">
          <span class="text_1"> 电池容量 </span>
          <span class="text_2">20Ah</span>
        </div>
        <div class="box_1">
          <span class="text_1"> 功率 </span>
          <span class="text_2">50W</span>
        </div>
        <div class="box_1">
          <span class="text_1"> 材质 </span>
          <span class="text_2">ABS</span>
        </div>
        <div class="box_1">
          <span class="text_1"> 最大承重 </span>
          <span class="text_2">5kg</span>
        </div>
        <div class="box_1">
          <span class="text_1"> 托盘尺寸 </span>
          <span class="text_2">340*250mm</span>
        </div>
      </div>
      <div class="section_7_box4">
        <div class="box_1">功能参数</div>
        <img
          v-if="isTrues"
          @click="clickFuns"
          src="@/assets/images/shangla.png"
          alt=""
          style="width: 1.375rem; height: 0.75rem; margin-right: 4.375rem"
        />
        <img
          v-else
          @click="clickFuns"
          src="@/assets/images/xiala.png"
          alt=""
          style="width: 1.375rem; height: 0.75rem; margin-right: 4.375rem"
        />
      </div>
      <div v-show="isTrues" class="section_7_box4_1">
        <div class="section_7_box4_1_0">
          <div class="box_1">
            <span class="text_1"> 续航时间 </span>
            <span class="text_2">≥10h</span>
          </div>
          <div class="box_1">
            <span class="text_1"> 充电时间 </span>
            <span class="text_2">≦6h</span>
          </div>
          <div class="box_1">
            <span class="text_1"> 移动速度 </span>
            <span class="text_2">0-1.0m/s</span>
          </div>
          <div class="box_1">
            <span class="text_1"> 定位精准 </span>
            <span class="text_2">± 50mm</span>
          </div>
          <div class="box_1">
            <span class="text_1"> 导航方式 </span>
            <span class="text_2">激光自主导航</span>
          </div>
        </div>
        <div class="box_2">
          <span class="text_1">检测功能：</span>
          <span class="text_2">体脂、血压、单导心电、问卷、远程会诊、诊断</span>
        </div>
      </div>
    </div>
  </div>
</template>
<style scoped lang="scss">
.banner {
  max-width: 160rem;
  width: 100%;
  position: relative;
  margin: 0 auto;
  margin-top: 3.5rem;
  overflow: hidden;
  .section_1 {
    width: 100%;
    height: 37.5rem;
    background-image: url('@/assets/images/jiqirens.png');
    background-size: 100% 100%;
  }
  .section_2 {
    background-color: rgba(255, 255, 255, 1);
    width: 100%;
    height: 22.6875rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    .text_10 {
      width: 30rem;
      height: 3.5rem;
      overflow-wrap: break-word;
      color: rgba(34, 34, 34, 1);
      font-size: 1.875rem;
      font-family: PingFangSC-Medium;
      font-weight: 500;
      text-align: left;
      white-space: nowrap;
      line-height: 3.5rem;
      margin: 4.375rem 0 0 0;
      text-align: center;
    }
    .section_6_1 {
      background-color: rgba(2, 129, 224, 1);
      width: 3.125rem;
      height: 0.25rem;
      margin: 0.625rem 0 0 0;
    }
    .text_11 {
      width: 15.4375rem;
      height: 1.75rem;
      overflow-wrap: break-word;
      color: rgba(102, 102, 102, 1);
      font-size: 1rem;
      font-family: PingFangSC-Regular;
      font-weight: normal;
      text-align: left;
      white-space: nowrap;
      line-height: 1.75rem;
      margin: 0.625rem 0 0 0;
      text-align: center;
    }
    .list_3 {
      cursor: pointer;
      width: 92.5rem;
      height: 7.8125rem;
      display: flex;
      justify-content: space-between;
      margin: 2.5rem 0 1.25rem 0;
    }
    .text-group_1-0 {
      border-radius: 0 0.625rem 0.625rem 0;
      position: relative;
      width: 23.125rem;
      height: 7.8125rem;
      display: flex;
      flex-direction: column;
      justify-content: flex-center;
      background: rgba(72, 101, 255, 1);
    }
    .text_12-0 {
      width: 11.9375rem;
      height: 2.0625rem;
      overflow-wrap: break-word;
      font-size: 1.5rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      text-align: left;
      white-space: nowrap;
      margin: 1.6875rem 0 0 2.5rem;
      color: rgba(255, 255, 255, 1);
    }
    .text_13-0 {
      width: 12.6875rem;
      height: 1.375rem;
      overflow-wrap: break-word;
      font-size: 1rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      text-align: left;
      white-space: nowrap;
      margin: 0.9375rem 0 1.75rem 2.5rem;
      color: rgba(255, 255, 255, 1);
    }
    .image_4-0 {
      position: absolute;
      left: 0;
      top: -2.5625rem;
      width: 0.125rem;
      height: 10.3125rem;
    }
    .text-group_1-1 {
      border-radius: 0 0.625rem 0.625rem 0rem;
      position: relative;
      width: 23.125rem;
      height: 7.8125rem;
      display: flex;
      flex-direction: column;
      justify-content: flex-center;
      background: rgba(216, 216, 216, 0);
    }
    .text_12-1 {
      width: 12rem;
      height: 2.0625rem;
      overflow-wrap: break-word;
      font-size: 1.5rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      text-align: left;
      white-space: nowrap;
      margin: 1.6875rem 0 0 2.5rem;
      color: rgba(34, 34, 34, 1);
    }
    .text_13-1 {
      width: 8.375rem;
      height: 1.375rem;
      overflow-wrap: break-word;
      font-size: 1rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      text-align: left;
      white-space: nowrap;
      margin: 0.9375rem 0 1.75rem 2.5rem;
      color: rgba(102, 102, 102, 1);
    }
    .image_2-1 {
      position: absolute;
      left: 0;
      top: 1.25rem;
      width: 0.0625rem;
      height: 7.8125rem;
    }
    .text-group_1-2 {
      border-radius: 0 0.625rem 0.625rem 0;
      position: relative;
      width: 23.125rem;
      height: 7.8125rem;
      display: flex;
      flex-direction: column;
      justify-content: flex-center;
      background: rgba(216, 216, 216, 0);
    }
    .text_12-2 {
      width: 10.5rem;
      height: 2.0625rem;
      overflow-wrap: break-word;
      font-size: 1.5rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      text-align: left;
      white-space: nowrap;
      margin: 1.75rem 0 0 2.5rem;
      color: rgba(34, 34, 34, 1);
    }
    .text_13-2 {
      width: 8.375rem;
      height: 1.375rem;
      overflow-wrap: break-word;
      font-size: 1rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      text-align: left;
      white-space: nowrap;
      margin: 0.875rem 0 1.75rem 2.5rem;
      color: rgba(102, 102, 102, 1);
    }
    .image_2-2 {
      position: absolute;
      left: 0;
      top: 1.25rem;
      width: 0.0625rem;
      height: 7.8125rem;
    }
    .image_3-2 {
      position: absolute;
      left: 23.0625rem;
      top: 1.25rem;
      width: 0.0625rem;
      height: 7.8125rem;
    }
    .text-group_1-3 {
      border-radius: 0 0.625rem 0.625rem 0;
      position: relative;
      width: 23.125rem;
      height: 7.8125rem;
      display: flex;
      flex-direction: column;
      justify-content: flex-center;
      background: rgba(216, 216, 216, 0);
    }
    .text_12-3 {
      width: 7.5rem;
      height: 2.0625rem;
      overflow-wrap: break-word;
      font-size: 1.5rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      text-align: left;
      white-space: nowrap;
      margin: 1.6875rem 0 0 2.5rem;
      color: rgba(34, 34, 34, 1);
    }
    .text_13-3 {
      width: 8.375rem;
      height: 1.375rem;
      overflow-wrap: break-word;
      font-size: 1rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      text-align: left;
      white-space: nowrap;
      margin: 0.9375rem 0 1.75rem 2.5rem;
      color: rgba(102, 102, 102, 1);
    }
    .image_3-3 {
      position: absolute;
      left: 23.0625rem;
      top: 1.25rem;
      width: 0.0625rem;
      height: 7.8125rem;
    }
  }
  .section_3 {
    background-color: rgba(34, 34, 34, 1);
    width: 100%;
    height: 12.5rem;
    display: flex;
    justify-content: center;
    align-items: center;
    .image_7 {
      width: 1.25rem;
      height: 2.1875rem;
      margin: 0 10.0625rem 0 2.4375rem;
    }
    .group_2 {
      width: 23.125rem;
      height: 12.5rem;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .image_5 {
      width: 1.25rem;
      height: 2.1875rem;
      margin: 6.75rem 0 0 10.0625rem;
    }
    .box_31 {
      width: 65rem;
      height: 9.6875rem;
      margin: 2.5rem 0 0 4.375rem;
    }
    .text_14 {
      width: 11.9375rem;
      height: 2.0625rem;
      overflow-wrap: break-word;
      color: rgba(255, 255, 255, 1);
      font-size: 1.5rem;
      font-family: PingFangSC-Medium;
      font-weight: 500;
      text-align: left;
      white-space: nowrap;
      line-height: 2.0625rem;
    }
    .section_8 {
      background-color: rgba(255, 255, 255, 1);
      width: 5rem;
      height: 0.25rem;
      margin-top: 0.9375rem;
    }
    .text_15 {
      width: 56.25rem;
      height: 3.25rem;
      overflow-wrap: break-word;
      color: rgba(255, 255, 255, 1);
      font-size: 1rem;
      font-family: PingFangSC-Regular;
      font-weight: normal;
      text-align: left;
      display: block;
      margin-top: 1.25rem;
    }
    .image_6 {
      width: 1.25rem;
      height: 2.5rem;
      margin-right: 2.5rem;
      margin-left: 10rem;
    }
  }
  .section_4 {
    width: 100%;
    height: 39.25rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    background: linear-gradient(180deg, #dfe9ff 0%, #f6f9ff 100%);
    .section_4_box1 {
      width: 8rem;
      height: 2.8125rem;
      font-size: 1.875rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      color: #222222;
      line-height: 2.8125rem;
      margin-top: 5rem;
      text-align: center;
    }
    .section_4_box2 {
      width: 3.125rem;
      height: 0.25rem;
      background: #0281e0;
      margin: 0.8125rem 0 0.875rem 0;
    }
    .section_4_box3 {
      height: 1.375rem;
      font-size: 1rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #666666;
      line-height: 1.375rem;
      text-align: center;
    }
    .section_4_box4 {
      margin-top: 2.5rem;
      width: 92.5rem;
      height: 21.25rem;
      .section_4_box4_top {
        width: 92.5rem;
        height: 9.375rem;
        display: flex;
        .box {
          display: flex;
          width: 45rem;
          height: 9.375rem;
          background-color: #ffffff;
          .left {
            width: 3.75rem;
            height: 3.75rem;
            background-image: url('@/assets/images/shuangbiao.png');
            background-size: 100% 100%;
            margin-left: 2.5rem;
            margin-top: 2.8125rem;
          }
          .right {
            display: flex;
            flex-direction: column;
            margin-left: 1.875rem;
            .box_s {
              width: 7.5rem;
              height: 2.0625rem;
              font-size: 1.5rem;
              font-family: PingFangSC, PingFang SC;
              font-weight: 500;
              color: #222222;
              line-height: 2.0625rem;
              margin-top: 1.8125rem;
              margin-bottom: 0.9375rem;
            }
            .box_s1 {
              width: 34.375rem;
              height: 2.75rem;
              font-size: 1rem;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #666666;
            }
          }
        }
        .box_1 {
          display: flex;
          width: 45rem;
          height: 9.375rem;
          background-color: #ffffff;
          margin-left: 2.5rem;
          .left {
            width: 3.75rem;
            height: 3.75rem;
            background-image: url('@/assets/images/shangjian.png');
            background-size: 100% 100%;
            margin-left: 2.5rem;
            margin-top: 2.8125rem;
          }
          .right {
            display: flex;
            flex-direction: column;
            margin-left: 1.875rem;
            .box_s {
              width: 6rem;
              height: 2.0625rem;
              font-size: 1.5rem;
              font-family: PingFangSC, PingFang SC;
              font-weight: 500;
              color: #222222;
              margin-top: 2.5rem;
              margin-bottom: 0.9375rem;
            }
            .box_s1 {
              width: 34.375rem;
              height: 1.375rem;
              font-size: 1rem;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #666666;
            }
          }
        }
      }
      .section_4_box4_bottom {
        margin-top: 2.5rem;
        width: 92.5rem;
        height: 9.375rem;
        display: flex;
        .box {
          display: flex;
          width: 45rem;
          height: 9.375rem;
          background-color: #ffffff;
          .left {
            width: 3.75rem;
            height: 3.75rem;
            background-image: url('@/assets/images/shuangdun.png');
            background-size: 100% 100%;
            margin-left: 2.5rem;
            margin-top: 2.8125rem;
          }
          .right {
            display: flex;
            flex-direction: column;
            margin-left: 1.875rem;
            .box_s {
              width: 6rem;
              height: 2.0625rem;
              font-size: 1.5rem;
              font-family: PingFangSC, PingFang SC;
              font-weight: 500;
              color: #222222;
              margin-top: 1.8125rem;
              margin-bottom: 0.9375rem;
            }
            .box_s1 {
              width: 34.375rem;
              height: 2.75rem;
              font-size: 1rem;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #666666;
              line-height: 1.375rem;
            }
          }
        }
        .box_1 {
          display: flex;
          width: 45rem;
          height: 9.375rem;
          background-color: #ffffff;
          margin-left: 2.5rem;
          .left {
            width: 3.75rem;
            height: 3.75rem;
            background-image: url('@/assets/images/shexiang.png');
            background-size: 100% 100%;
            margin-left: 2.5rem;
            margin-top: 2.8125rem;
          }
          .right {
            display: flex;
            flex-direction: column;
            margin-left: 1.875rem;
            .box_s {
              width: 6rem;
              height: 2.0625rem;
              font-size: 1.5rem;
              font-family: PingFangSC, PingFang SC;
              font-weight: 500;
              color: #222222;
              line-height: 2.0625rem;
              margin-top: 1.8125rem;
              margin-bottom: 0.9375rem;
            }
            .box_s1 {
              width: 34.375rem;
              height: 2.75rem;
              font-size: 1rem;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #666666;
              line-height: 1.375rem;
            }
          }
        }
      }
    }
  }
  .section_5 {
    width: 100%;
    height: 39.875rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    background: #f8f8f8;
    .section_5_box1 {
      margin-top: 4.375rem;
      width: 8rem;
      height: 2.8125rem;
      font-size: 1.875rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      color: #222222;
      line-height: 2.8125rem;
      text-align: center;
    }
    .section_5_box2 {
      width: 3.125rem;
      height: 0.25rem;
      background: #0281e0;
      margin: 0.8125rem 0 0.875rem 0;
    }
    .section_5_box3 {
      height: 1.375rem;
      font-size: 1rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #666666;
      line-height: 1.375rem;
      text-align: center;
    }
    .section_5_box4 {
      width: 100%;
      height: 26.25rem;
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-top: 2.625rem;
      .box1 {
        background-image: url('@/assets/images/zhongjiqiren.png');
        background-size: 100% 100%;
        width: 25.125rem;
        height: 26.25rem;
      }
      .box2 {
        width: 21.25rem;
        height: 6.4375rem;
        background-image: url('@/assets/images/leftkuang.png');
        background-size: 100% 100%;
        position: absolute;
        top: 1.25rem;
        left: 25rem;
        display: flex;
        justify-content: center;
        .hao {
          width: 12.5rem;
          height: 2.0625rem;
          font-size: 1.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #666666;
          line-height: 2.0625rem;
          margin-top: 1.625rem;
        }
      }
      .box3 {
        width: 21.25rem;
        height: 6.5625rem;
        background-image: url('@/assets/images/leftkuang.png');
        background-size: 100% 100%;
        position: absolute;
        top: 13.4375rem;
        left: 17.5rem;
        display: flex;
        justify-content: center;
        .haos {
          width: 12.5rem;
          height: 2.0625rem;
          font-size: 1.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #666666;
          line-height: 2.0625rem;
          margin-top: 1.625rem;
        }
      }
      .box4 {
        width: 21.25rem;
        height: 6.4375rem;
        background-image: url('@/assets/images/rightkuang.png');
        background-size: 100% 100%;
        position: absolute;
        top: 1.25rem;
        right: 24.9375rem;
        display: flex;
        justify-content: center;
        .hao_2 {
          width: 12.5rem;
          height: 2.0625rem;
          font-size: 1.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #666666;
          line-height: 2.0625rem;
          margin-top: 1.625rem;
        }
      }
      .box5 {
        width: 21.25rem;
        height: 6.5625rem;
        background-image: url('@/assets/images/rightkuang.png');
        background-size: 100% 100%;
        position: absolute;
        top: 13.4375rem;
        right: 17.4375rem;
        display: flex;
        justify-content: center;
        .hao_1 {
          width: 9rem;
          height: 2.0625rem;
          font-size: 1.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #666666;
          line-height: 2.0625rem;
          margin-top: 1.625rem;
        }
      }
    }
  }
  .section_6 {
    margin-top: -1.625rem;
    width: 100%;
    height: 21.875rem;
    background-image: url('@/assets/images/caise.png');
    background-size: 100% 100%;
    position: relative;
    .section_6_box {
      position: absolute;
      top: 6.3125rem;
      right: 22.875rem;
      width: 43.75rem;
      height: 9.1875rem;
      .section_6_box_top {
        height: 2.8125rem;
        font-size: 30px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #ffffff;
        line-height: 2.8125rem;
      }
      .section_6_box_bottom {
        height: 4.5rem;
        font-size: 20px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #ffffff;
        line-height: 2.25rem;
        margin-top: 1.875rem;
      }
    }
  }
  .section_7 {
    width: 100%;
    height: 125.5rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: #ffffff;
    .section_5_box1 {
      width: 8rem;
      height: 2.8125rem;
      font-size: 1.875rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      color: #222222;
      line-height: 3.5rem;
      margin-top: 4.375rem;
      text-align: center;
    }
    .section_5_box2 {
      width: 3.125rem;
      height: 0.25rem;
      background: #0281e0;
      margin: 0.8125rem 0 0.875rem 0;
    }
    .section_5_box3 {
      height: 1.375rem;
      font-size: 1rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #666666;
      line-height: 1.375rem;
      text-align: center;
    }
    .section_5_box4 {
      width: 81.1875rem;
      height: 25rem;
      margin-top: 2.625rem;
      display: flex;
      .section_5_box4_left {
        width: 37.5rem;
        height: 25rem;
        background-image: url('@/assets/images/leftshuang.png');
        background-size: 100% 100%;
      }
      .section_5_box4_right {
        flex: 1;
        display: flex;
        flex-direction: column;
        .box {
          margin: 4.8125rem 0 1.25rem 5rem;
          display: flex;
          align-items: center;
          .hao {
            width: 0.25rem;
            height: 1.25rem;
            background: #0281e0;
            border-radius: 0.125rem;
            margin-right: 0.9375rem;
          }
          .hao_1 {
            width: 9rem;
            height: 2.0625rem;
            font-size: 1.5rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #222222;
            line-height: 2.0625rem;
          }
        }
        .box_1 {
          width: 37.5rem;
          height: 4.875rem;
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #666666;
          line-height: 1.625rem;
          margin-left: 6.1875rem;
        }
      }
    }
    .section_5_box5 {
      width: 81.1875rem;
      height: 25rem;
      margin-top: 2.5rem;
      display: flex;
      .section_5_box4_left {
        flex: 1;
        display: flex;
        flex-direction: column;
        .box {
          display: flex;
          align-items: center;
          margin-top: 4.8125rem;
          .hao {
            width: 0.25rem;
            height: 1.25rem;
            background: #0281e0;
            border-radius: 0.125rem;
            margin-right: 0.9375rem;
          }
          .hao_1 {
            width: 12rem;
            height: 2.0625rem;
            font-size: 1.5rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #222222;
            line-height: 2.0625rem;
          }
        }
        .box_1 {
          width: 37.5rem;
          height: 3.25rem;
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #666666;
          line-height: 1.625rem;
          margin-top: 1.25rem;
          margin-left: 1.1875rem;
        }
      }
      .section_5_box4_right {
        width: 37.5rem;
        height: 25rem;
        background-image: url('@/assets/images/zhiling.png');
        background-size: 100% 100%;
      }
    }
    .section_5_box6 {
      width: 81.1875rem;
      height: 25rem;
      margin-top: 2.5rem;
      display: flex;
      .section_5_box4_left {
        width: 37.5rem;
        height: 25rem;
        background-image: url('@/assets/images/zhinengfenxi.png');
        background-size: 100% 100%;
      }
      .section_5_box4_right {
        flex: 1;
        display: flex;
        flex-direction: column;
        .box {
          display: flex;
          align-items: center;
          margin-top: 4.8125rem;
          margin-left: 5rem;
          .hao {
            width: 0.25rem;
            height: 1.25rem;
            background: #0281e0;
            border-radius: 0.125rem;
            margin-right: 0.9375rem;
          }
          .hao_1 {
            width: 10.5625rem;
            height: 2.0625rem;
            font-size: 1.5rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #222222;
            line-height: 2.0625rem;
          }
        }
        .box_1 {
          width: 37.5rem;
          height: 3.25rem;
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #666666;
          line-height: 1.625rem;
          margin-top: 2.5rem;
          margin-left: 6.1875rem;
        }
      }
    }
    .section_5_box7 {
      width: 81.1875rem;
      height: 25rem;
      margin-top: 2.5rem;
      display: flex;
      .section_5_box4_left {
        flex: 1;
        display: flex;
        flex-direction: column;
        margin-top: 4.8125rem;
        .box {
          display: flex;
          align-items: center;
          .hao {
            width: 0.25rem;
            height: 1.25rem;
            background: #0281e0;
            border-radius: 0.125rem;
            margin-right: 0.9375rem;
          }
          .hao_1 {
            width: 9rem;
            height: 2.0625rem;
            font-size: 1.5rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #222222;
            line-height: 2.0625rem;
          }
        }
        .box_1 {
          width: 37.5rem;
          height: 4.875rem;
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #666666;
          line-height: 1.625rem;
          margin-top: 2.5rem;
          margin-left: 1.1875rem;
        }
      }
      .section_5_box4_right {
        width: 37.5rem;
        height: 25rem;
        background-image: url('@/assets/images/yuyin.png');
        background-size: 100% 100%;
      }
    }
  }
  .section_8 {
    width: 100%;
    height: 39.5625rem;
    background-color: #f3faff;
    display: flex;
    justify-content: center;
    position: relative;
    .left {
      margin-top: 4.375rem;
      width: 38.75rem;
      height: 27.5rem;
      display: flex;
      flex-direction: column;
      .box_1 {
        width: 4.6875rem;
        height: 5.625rem;
        font-size: 2rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #222222;
        line-height: 2.8125rem;
      }
      .box_2 {
        width: 3.125rem;
        height: 0.25rem;
        background: #0281e0;
        margin: 0.625rem 0 1.25rem 0;
      }
      .box_3 {
        width: 11.875rem;
        height: 1.375rem;
        font-size: 1rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #666666;
        line-height: 1.375rem;
      }
      .box_4 {
        width: 12rem;
        height: 2.0625rem;
        font-size: 1.5rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #222222;
        line-height: 2.0625rem;
        margin-top: 10.125rem;
      }
      .box_5 {
        width: 35rem;
        height: 3.5rem;
        margin-top: 1.25rem;
        display: flex;
        .boxLeft {
          width: 28.5rem;
          height: 2.0625rem;
          font-size: 1.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #222222;
          line-height: 2.0625rem;
        }
        .boxright {
          margin-left: 3.9375rem;
          width: 2.5625rem;
          height: 3.5rem;
          font-size: 2.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #c9cdd1;
          line-height: 3.5rem;
        }
      }
    }
    .right {
      margin-top: 4.375rem;
      width: 43.75rem;
      height: 27.875rem;
      display: flex;
      flex-direction: column;
      .box_2 {
        display: flex;
        margin-top: 1.25rem;
        align-items: center;
        .box_2_1 {
          width: 6rem;
          height: 2.0625rem;
          font-size: 1.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #222222;
          line-height: 2.0625rem;
          margin-left: -1.3125rem;
        }
        .box_2_2 {
          margin-left: 1.5625rem;
          width: 37.5rem;
          height: 1.375rem;
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #666666;
          line-height: 1.375rem;
          text-align: right;
        }
      }
    }
    .banners {
      position: absolute;
      bottom: 1.25rem;
      width: 18.75rem;
      display: flex;
      justify-content: space-between;
      .active {
        width: 3.75rem;
        height: 0.375rem;
        background: #0281e0;
        border-radius: 0.3125rem;
      }
      .box_1 {
        width: 3.75rem;
        height: 0.375rem;
        background: #0281e0;
        border-radius: 0.3125rem;
        opacity: 0.2;
      }
      .box_2 {
        width: 3.75rem;
        height: 0.375rem;
        background: #0281e0;
        border-radius: 0.3125rem;
        opacity: 0.2;
      }
      .box_3 {
        width: 3.75rem;
        height: 0.375rem;
        background: #0281e0;
        border-radius: 0.3125rem;
        opacity: 0.2;
      }
      .box_4 {
        width: 3.75rem;
        height: 0.375rem;
        background: #0281e0;
        border-radius: 0.3125rem;
        opacity: 0.2;
      }
    }
  }
  .section_9 {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    padding-bottom: 5rem;
    background-color: #f8f8f8;
    .section_7_box1 {
      height: 2.8125rem;
      font-size: 2rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      color: #222222;
      line-height: 2.8125rem;
      margin-top: 4.375rem;
      text-align: center;
    }
    .section_7_box2 {
      width: 3.125rem;
      height: 0.25rem;
      background: #0281e0;
      margin: 0.8125rem 0 0.875rem 0;
    }
    .section_7_box3 {
      height: 1.375rem;
      font-size: 1rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #666666;
      line-height: 1.375rem;
      text-align: center;
    }
    .section_7_box4 {
      display: flex;
      width: 92.5rem;
      height: 6.25rem;
      align-items: center;
      justify-content: space-between;
      background-color: #ffffff;
      margin-top: 2.5rem;
      border-top-left-radius: 0.9375rem;
      border-top-right-radius: 0.9375rem;
      .box_1 {
        width: 6rem;
        height: 2.0625rem;
        font-size: 1.5rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #222222;
        line-height: 2.0625rem;
        margin-left: 4.375rem;
      }
    }
    .section_7_box4_2 {
      border-top: 0.0625rem solid #c7c7c7;
      height: 13.75rem;
      width: 92.5rem;
      display: flex;
      flex-wrap: wrap;
      background: #ffffff;
      align-items: center;
      border-bottom-left-radius: 0.9375rem;
      border-bottom-right-radius: 0.9375rem;
      .box_1 {
        margin-top: 0.625rem;
        width: 23.125rem;
        height: 3.125rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        .text_1 {
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 1rem;
          color: #666666;
        }
        .text_2 {
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #222222;
          line-height: 1.375rem;
        }
      }
    }
    .section_7_box4_1 {
      border-top: 0.0625rem solid #c7c7c7;
      height: 14.375rem;
      width: 92.5rem;
      display: flex;
      flex-direction: column;
      background: #ffffff;
      border-bottom-left-radius: 0.9375rem;
      border-bottom-right-radius: 0.9375rem;
      .section_7_box4_1_0 {
        display: flex;
        flex-wrap: wrap;
      }
      .box_1 {
        margin-top: 0.625rem;
        width: 23.125rem;
        height: 3.125rem;
        padding: 2.5rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        .text_1 {
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 1rem;
          color: #666666;
        }
        .text_2 {
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #222222;
          line-height: 1.375rem;
        }
      }
      .box_2 {
        display: flex;
        align-items: center;
        margin-left: 4.375rem;
        .text_1 {
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #666666;
        }
        .text_2 {
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #222222;
        }
      }
    }
  }
}
</style>
