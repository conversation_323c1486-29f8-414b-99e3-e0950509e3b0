{"name": "weilai", "version": "0.0.0", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview --port 4173", "lint": "eslint --ext .js,.vue --ignore-path .gitignore --fix src", "format": "prettier .  --write"}, "dependencies": {"@kjgl77/datav-vue3": "^1.2.2", "axios": "^0.27.2", "echarts": "^5.3.3", "element-plus": "^2.2.28", "lib-flexible-computer": "^1.0.2", "pdfjs-dist": "^2.11.338", "svg-sprite-loader": "^6.0.11", "vue": "^3.2.37", "vue-router": "^4.1.3"}, "devDependencies": {"@babel/eslint-parser": "^7.21.8", "@vitejs/plugin-vue": "^3.0.1", "esbuild": "^0.20.0", "eslint": "^8.41.0", "eslint-config-alloy": "^5.0.0", "eslint-plugin-vue": "^9.14.1", "husky": "1.3.1", "lint-staged": "8.1.5", "postcss-px-to-viewport": "^1.1.1", "postcss-px2rem": "^0.3.0", "postcss-pxtorem": "^6.0.0", "prettier": "2.8.8", "px2rem-loader": "^0.1.9", "sass": "^1.54.8", "vite": "^3.0.4"}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "husky": {"hooks": {"pre-commit": "lint-staged"}}}