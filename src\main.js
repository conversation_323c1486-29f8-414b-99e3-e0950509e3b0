import { createApp } from 'vue'
import App from './App.vue'
import router from './router'

import './assets/main.css'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'

import svgIcon from './components/svgIcon.vue'
import '@/assets/text/text.css'

const app = createApp(App)
import DataVVue3 from '@kjgl77/datav-vue3'
app.use(DataVVue3)
app.use(router)
app.use(ElementPlus, {
  locale: zhCn
})
app.component('SvgIcon', svgIcon).mount('#app')
