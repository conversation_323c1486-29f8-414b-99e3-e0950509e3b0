<script setup>
import contactUs from '@/components/contactUs.vue'
import myFooter from '@/components/myFooter.vue'
import myHeader from '@/components/myHeader.vue'
import { useRouter, useRoute } from 'vue-router'
const router = useRouter()
const route = useRoute()

function goContact() {
  router.push({ path: '/Detail', name: 'detail', query: { page: 'contact' } })
}
</script>
<!-- research -->
<template>
  <my-header />
  <div class="banner">
    <img src="@/assets/images/studyHeader.png" alt="" />
    <div class="banner-info">
      <div class="w">
        <p>
          卫软有丰富的高校和医疗机构研究项目开发经验， <br />
          与您一起探索更多创新可能性
        </p>
      </div>
    </div>
  </div>
  <div class="w">
    <div class="study-box">
      <div class="item pc">
        <h5>研究开发为什么找我们</h5>
        <p>
          卫来健康是一个开放、包容的数字健康监测平台，平台是用户和医生的承载核心，为用户和医生提供监测数据和AI报告，平台不参与用户数据解读与诊断，我们提供由专业医生为用户提供健康咨询。卫来健康是一个开放、包容的数字健康监测平台，平台是用户和医生的承载核心，为用户和医生提供监测数据和AI报告，平台不参与用户数据解读与诊断，我们提供由专业医生为用户提供健康咨询。
        </p>

        <el-popover placement="top-end" title="" :width="510" trigger="click">
          <contact-us />
          <template #reference>
            <button>联系我们</button>
          </template>
        </el-popover>
        <!-- <button>联系我们 &ensp; <svg-icon name="Right" color="#fff" /></button> -->
      </div>
      <div class="item phone">
        <h5>研究开发为什么找我们</h5>
        <img src="@/assets/images/study-one.png" alt="" />
        <p>
          卫来健康是一个开放、包容的数字健康监测平台，平台是用户和医生的承载核心，为用户和医生提供监测数据和AI报告，平台不参与用户数据解读与诊断，我们提供由专业医生为用户提供健康咨询。卫来健康是一个开放、包容的数字健康监测平台，平台是用户和医生的承载核心，为用户和医生提供监测数据和AI报告，平台不参与用户数据解读与诊断，我们提供由专业医生为用户提供健康咨询。
        </p>
        <button @click="goContact">联系我们</button>
      </div>
      <div class="item pc">
        <img src="@/assets/images/study-one.png" alt="" />
      </div>
      <div class="item has-mask">
        <img src="@/assets/images/study-two.png" alt="" />
        <div class="mask">团队介绍</div>
      </div>
      <div class="item has-mask">
        <img src="@/assets/images/study-three.png" alt="" />
        <div class="mask">项目管理与交付</div>
      </div>
    </div>
  </div>
  <my-footer />
</template>
<style lang="scss" scoped>
.banner {
  max-width: 2560px;
  width: 100%;
  position: relative;
  margin: 0 auto;
  margin-bottom: 50px;

  img {
    width: 100%;
    height: auto;
  }

  .banner-info {
    width: 100%;
    height: 100%;
    padding-top: 56px;
    position: absolute;
    left: 0;
    top: 0;
    color: #fff;
    display: flex;
    flex-direction: column;
    justify-content: center;
    font-family: 'Alibaba PuHuiTi 2.0';

    p {
      font-weight: 900;
      font-size: 48px;
      line-height: 67px;
      margin-bottom: 20px;
    }

    span {
      font-weight: 400;
      font-size: 16px;
      line-height: 25px;
    }

    button {
      width: 238px;
      height: 72px;
      background-color: #2f75d3;
      border: 0;
      border-radius: 5px;
      font-weight: 400;
      font-size: 18px;
      line-height: 34px;
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 80px;
      cursor: pointer;
    }
  }

  @media screen and (max-width: 1024px) {
    margin-bottom: 24px;
    overflow: hidden;

    img {
      height: 600px;
      width: auto;
      position: relative;
      left: calc(-594px + 50%);
    }

    .banner-info {
      display: flex;

      .w {
        width: 100vw;
        padding: 0 12%;
      }

      p {
        font-size: 32px;
        line-height: 48px;
      }

      span {
        font-size: 14px;
        line-height: 20px;

        br {
          display: none;
        }
      }

      button {
        width: 94px;
        height: 34px;
        font-size: 14px;
        margin-top: 40px;
      }
    }
  }

  @media screen and (max-width: 800px) {
    margin-bottom: 24px;
    overflow: hidden;

    img {
      height: 600px;
      width: auto;
      position: relative;
      left: calc(-594px + 50%);
    }

    .banner-info {
      display: flex;

      .w {
        width: 100vw;
        padding: 0 12%;
      }

      p {
        font-size: 18px;
        line-height: 24px;
      }

      span {
        font-size: 12px;
        line-height: 16px;

        br {
          display: none;
        }
      }

      button {
        width: 94px;
        height: 34px;
        font-size: 12px;
      }
    }
  }
}

.study-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;

  .item:nth-child(1) {
    width: 50%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin-right: 80px;

    h5 {
      font-style: normal;
      font-weight: 500;
      font-size: 24px;
      line-height: 34px;
      letter-spacing: -0.3px;
      color: #3b3f47;
    }

    p {
      font-style: normal;
      font-weight: 400;
      font-size: 16px;
      line-height: 22px;
      color: #63676f;
      margin: 15px 0 60px;
    }

    button {
      width: 172px;
      height: 54px;
      background-color: #2f75d3;
      border: 0;
      border-radius: 5px;
      font-weight: 400;
      font-size: 18px;
      line-height: 34px;
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
    }
  }

  .item:nth-child(3) {
    // width: 605px;
    // height: 370px;
    width: 40%;
    height: auto;
  }

  .item:nth-child(4),
  .item:nth-child(5) {
    width: 42.56%;
    height: auto;
    // height: 350px;
    position: relative;

    .mask {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 62px;
      backdrop-filter: blur(4px);
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .item:nth-child(4) {
    .mask {
      color: #fff;
      background: rgba(69, 71, 94, 0.58);
    }
  }

  .item:nth-child(5) {
    .mask {
      color: #3b3f47;
      background: #eef7ff;
    }
  }

  .phone {
    display: none;
  }

  .item {
    margin-bottom: 80px;

    img {
      width: 100%;
      height: 100%;
    }
  }

  @media screen and (max-width: 1024px) {
    .pc {
      display: none !important;
    }

    .phone {
      display: block;
    }

    .phone {
      width: 100% !important;

      h5 {
        font-family: 'PingFang SC';
        font-style: normal;
        font-weight: 500;
        font-size: 14px;
        /* identical to box height */
        letter-spacing: -0.3px;
        color: #3b3f47;
        margin: 0 0 12px;
      }

      p {
        margin: 8px 0 12px;
      }

      button {
        width: 88px;
        height: 30px;
        background-color: #2f75d3;
        border: 0;
        border-radius: 5px;
        font-weight: 400;
        font-size: 12px;
        line-height: 34px;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        margin-bottom: 20px;
      }
    }
    .item {
      width: 100% !important;
      margin-bottom: 12px;
      .mask {
        font-size: 12px;
        height: 20% !important;
      }
    }
  }
}
</style>
