<template>
  <div class="banner">
    <img src="@/assets/images/yuanchengtu.png" alt="" />
    <div class="info">
      <div class="text_1">远程会诊系统</div>
      <div class="text_2">
        为基层医疗机构提供与等级医院进行远程会诊的解决方案。该系统简单易用，无需专用远程会诊设备，学习成本低，普通电脑和平板即可支持。通过该系统，基层医疗机构可以方便地与上级医院进行音视频沟通，同时实现诊断协同，提高医疗服务水平。
      </div>
    </div>
  </div>
  <div class="section_main">
    <div class="section_1">
      <div class="section_1_top">
        <div class="section_1_top_text">系统特色</div>
        <div class="section_1_top_xian"></div>
        <div class="section_1_top_zimu">SYSTEM FEATURES</div>
      </div>
      <div class="section_1_main">
        <div class="section_1_main_box">
          <img src="@/assets/images/dianzan.png" alt="" style="width: 3.125rem; height: 3.125rem" />
          <div class="section_1_main_box_text">专为基层与等级医院联动设计</div>
          <div class="section_1_main_box_text1">
            系统针对基层医疗机构与等级医院的联动需求进行设计，满足不同医疗机构之间的协作需求。
          </div>
        </div>
        <div class="section_2_main_box">
          <img src="@/assets/images/lvduanxin.png" alt="" style="width: 3.125rem; height: 3.125rem" />
          <div class="section_1_main_box_text">诊断协同</div>
          <div class="section_1_main_box_text1">在音视频通信的同时实现诊断协同，提高诊断的准确性和效率。</div>
        </div>
        <div class="section_3_main_box">
          <img src="@/assets/images/bluephone.png" alt="" style="width: 3.125rem; height: 3.125rem" />
          <div class="section_1_main_box_text">跨设备、跨平台使用</div>
          <div class="section_1_main_box_text1">支持PC端与移动端互联，设备通信不受限制，满足不同场景下的使用需求。</div>
        </div>
      </div>
      <div class="section_1_bottom">
        <div class="section_1_bottom_box">
          <img src="@/assets/images/lanshouzhi.png" alt="" style="width: 3.125rem; height: 3.125rem" />
          <div class="section_1_bottom_box_text">简单易用</div>
          <div class="section_1_bottom_box_text1">无需专用远程会诊设备，普通电脑和平板即可支持，学习成本低。</div>
        </div>
        <div class="section_2_bottom_box">
          <img src="@/assets/images/lvdun.png" alt="" style="width: 3.125rem; height: 3.125rem" />
          <div class="section_1_bottom_box_text">数据存储与共享</div>
          <div class="section_1_bottom_box_text1">会诊过程中的数据可以存储在服务器端，方便后续查阅和共享。</div>
        </div>
      </div>
    </div>
    <div class="section_2">
      <div class="section_2_text">专为基层医疗设计，就医不再难</div>
    </div>
    <div class="section_3">
      <div class="section_3_box">
        <div class="section_3_box_text">功能特点</div>
        <div class="section_3_box_xian"></div>
        <div class="section_3_box_zimu">SPECIFICATIONS</div>
      </div>
      <div class="section_3_box_1">
        <div class="section_3_box_1_left"></div>
        <div class="section_3_box_1_right">
          <div class="section_3_box_1_right_box">
            <div class="videos">音视频通信</div>
            <div class="zhivideos">支持音视频通信功能，实现基层医疗机构与上级医院之间的实时沟通。</div>
          </div>
        </div>
      </div>
      <div class="section_3_box_2">
        <div class="section_3_box_2_left">
          <div class="section_3_box_2_left_box">
            <div class="section_3_box_2_left_box_1">诊断协同</div>
            <div class="section_3_box_2_left_box_2">
              在音视频通信同时，支持多边专家实时调阅患者电子健康档案，支持主诉、诊断协同编辑，有效提高诊断效率和准确性。
            </div>
          </div>
        </div>
        <div class="section_3_box_2_right"></div>
      </div>
      <div class="section_3_box_3">
        <div class="section_3_box_1_left"></div>
        <div class="section_3_box_1_right">
          <div class="section_3_box_1_right_box">
            <div class="videos">跨设备、跨平台使用</div>
            <div class="zhivideos">
              支持PC端与移动端互联，设备通信不受限制，支持低带宽模式和高清模式，满足不同场景下的使用需求。
            </div>
          </div>
        </div>
      </div>
      <div class="section_3_box_4">
        <div class="section_3_box_2_left">
          <div class="section_3_box_2_left_box">
            <div class="section_3_box_2_left_box_1">会议录制</div>
            <div class="section_3_box_2_left_box_2">
              系统支持配置会诊全程音视频录制，支持云端存储、云端播放，方便随时查阅调取。
            </div>
          </div>
        </div>
        <div class="section_3_box_2_right"></div>
      </div>
    </div>
    <div class="section_4">
      <div class="section_9_top">
        <div class="section_9_top_text">系统价值</div>
        <div class="section_9_top_xain"></div>
        <div class="section_9_top_zimu">VALUES</div>
      </div>
      <div class="section_9_bottom">
        <div class="section_9_bottom_box1">
          <div class="section_9_bottom_box1_left">
            <span class="section_9_bottom_box1_left_text">01</span>
          </div>
          <div class="section_9_bottom_box1_right">
            <div class="section_9_bottom_box1_right_text">降低患者就医成本与提高可及性</div>
            <div class="section_9_bottom_box1_right_text1">
              远程会诊系统有效降低了患者因就医而产生的时间成本和经济成本。患者无需远距离奔波至等级医院，便能通过远程会诊系统接受专业诊疗服务。这种降低就医成本的方式，特别对于那些居住在偏远地区或交通不便的患者而言，提高了医疗服务的可及性。
            </div>
          </div>
        </div>
        <div class="section_9_bottom_box2">
          <div class="section_9_bottom_box1_left">
            <span class="section_9_bottom_box1_left_text">03</span>
          </div>
          <div class="section_9_bottom_box1_right">
            <div class="section_9_bottom_box1_right_text">强化医疗团队协同与实时决策</div>
            <div class="section_9_bottom_box1_right_text1">
              远程会诊系统加强了医疗团队的协同工作能力。不同级别医疗机构的医生能够通过系统进行实时沟通，共同制定治疗方案，迅速做出医学决策。这种实时协同有助于提高医疗效率，确保患者能够及时获得高质量的医疗服务。
            </div>
          </div>
        </div>
        <div class="section_9_bottom_box3">
          <div class="section_9_bottom_box1_left">
            <span class="section_9_bottom_box1_left_text">02</span>
          </div>
          <div class="section_9_bottom_box1_right">
            <div class="section_9_bottom_box1_right_text">医疗资源整合与优化利用</div>
            <div class="section_9_bottom_box1_right_text1">
              远程会诊系统通过实现基层医疗机构与等级医院之间的无缝连接，促使医疗资源的整合与共享。这种整合使得基层医疗机构能够迅速获取等级医院的专业医疗资源，优化利用各级医疗机构的专业技能，从而提升基层医疗服务的水平。
            </div>
          </div>
        </div>
        <div class="section_9_bottom_box4">
          <div class="section_9_bottom_box1_left">
            <span class="section_9_bottom_box1_left_text">04</span>
          </div>
          <div class="section_9_bottom_box1_right">
            <div class="section_9_bottom_box1_right_text">专业远程会诊</div>
            <div class="section_9_bottom_box1_right_text1">
              不同于普通的远程会诊，在支持音视频的同时，卫软会诊平台还加入了患者健康档案实时调阅、患者主诉、诊断内容双向同步显示、检查结果实时展现，极大增加了远程会诊的实用性、可操作性和准确性。
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.banner {
  max-width: 160rem;
  width: 100%;
  position: relative;
  margin: 0 auto;
  margin-top: 3.5rem;
  img {
    width: 100%;
    height: auto;
  }
  .info {
    position: absolute;
    top: 9.8125rem;
    right: 12.5rem;
    width: 37.5rem;
    height: 17.8125rem;
    .text_1 {
      width: 18.75rem;
      height: 4.375rem;
      font-size: 2.75rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      color: #ffffff;
      line-height: 4.375rem;
    }
    .text_2 {
      width: 37.5rem;
      height: 11.5625rem;
      font-size: 1.25rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #ffffff;
      line-height: 2.3125rem;
      margin-top: 1.875rem;
    }
  }
}
.section_main {
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow: hidden;
  max-width: 160rem;
  margin: 0 auto;
  .section_1 {
    width: 100%;
    height: 47.9375rem;
    height: 47.9375rem;
    background-image: url('@/assets/images/bgxite.png');
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    .section_1_top {
      display: flex;
      flex-direction: column;
      align-items: center;
      .section_1_top_text {
        width: 10rem;
        height: 3.5rem;
        font-size: 1.875rem;
        font-size: 1.875rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #222222;
        line-height: 3.5rem;
        margin-top: 4.4375rem;
        text-align: center;
      }
      .section_1_top_xian {
        width: 3.125rem;
        height: 0.25rem;
        background: #0281e0;
        margin: 0.625rem 0;
      }
      .section_1_top_zimu {
        height: 1.75rem;
        font-size: 1rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #666666;
        line-height: 1.75rem;
      }
    }
    .section_1_main {
      width: 80rem;
      width: 80rem;
      height: 13.8125rem;
      margin: 2.5rem 0 3.125rem 0;
      margin: 2.5rem 0 3.125rem 0;
      display: flex;
      .section_1_main_box {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 25rem;
        height: 14.375rem;
        width: 25rem;
        height: 14.375rem;
        .section_1_main_box_text {
          width: 21.875rem;
          height: 2.0625rem;
          font-size: 1.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #222222;
          margin: 1.25rem 0 0.9375rem 0;
          text-align: center;
          text-align: center;
        }
        .section_1_main_box_text1 {
          width: 21.25rem;
          height: 2.75rem;
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #666666;
          line-height: 1.375rem;
          text-align: center;
        }
      }
      .section_2_main_box {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 25rem;
        height: 14.375rem;
        width: 25rem;
        height: 14.375rem;
        margin: 0 4.375rem;
        .section_1_main_box_text {
          width: 21.875rem;
          height: 2.0625rem;
          font-size: 1.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #222222;
          margin: 1.25rem 0 0.9375rem 0;
          text-align: center;
          text-align: center;
        }
        .section_1_main_box_text1 {
          width: 21.25rem;
          height: 2.75rem;
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #666666;
          line-height: 1.375rem;
          text-align: center;
        }
      }
      .section_3_main_box {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 25rem;
        height: 14.375rem;
        width: 25rem;
        height: 14.375rem;
        .section_1_main_box_text {
          width: 21.875rem;
          height: 2.0625rem;
          font-size: 1.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #222222;
          margin: 1.25rem 0 0.9375rem 0;
          text-align: center;
          text-align: center;
        }
        .section_1_main_box_text1 {
          width: 21.25rem;
          height: 2.75rem;
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #666666;
          line-height: 1.375rem;
          text-align: center;
        }
      }
    }
    .section_1_bottom {
      width: 55rem;
      width: 55rem;
      height: 13.8125rem;
      display: flex;
      .section_1_bottom_box {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 25rem;
        height: 14.375rem;
        width: 25rem;
        height: 14.375rem;
        .section_1_bottom_box_text {
          width: 21.875rem;
          height: 2.0625rem;
          font-size: 1.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #222222;
          text-align: center;
          text-align: center;
          margin: 1.25rem 0 0.9375rem 0;
        }
        .section_1_bottom_box_text1 {
          width: 21.25rem;
          height: 2.75rem;
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #666666;
          line-height: 1.375rem;
          text-align: center;
        }
      }
      .section_2_bottom_box {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 25rem;
        height: 14.375rem;
        width: 25rem;
        height: 14.375rem;
        margin-left: 4.3125rem;
        .section_1_bottom_box_text {
          width: 21.875rem;
          height: 2.0625rem;
          font-size: 1.5rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #222222;
          margin: 1.25rem 0 0.9375rem 0;
          text-align: center;
          text-align: center;
        }
        .section_1_bottom_box_text1 {
          width: 21.25rem;
          height: 2.75rem;
          font-size: 1rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #666666;
          line-height: 1.375rem;
          text-align: center;
        }
      }
    }
  }
  .section_2 {
    width: 100%;
    height: 25rem;
    background-image: url('@/assets/images/jiceng.png');
    background-size: 100% 100%;
    position: relative;
    .section_2_text {
      position: absolute;
      top: 10.125rem;
      left: 17.5%;
      width: 47.25rem;
      height: 4.6875rem;
      font-size: 2.5rem;
      font-size: 2.5rem;
      font-family: LiGothicMed;
      color: #ffffff;
    }
  }
  .section_3 {
    width: 100%;
    height: 138rem;
    background-color: #f3f7ff;
    display: flex;
    flex-direction: column;
    align-items: center;
    .section_3_box {
      display: flex;
      flex-direction: column;
      align-items: center;
      .section_3_box_text {
        width: 10rem;
        height: 3.5rem;
        font-size: 1.875rem;
        font-size: 1.875rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #222222;
        line-height: 3.5rem;
        margin-top: 4.375rem;
        text-align: center;
      }
      .section_3_box_xian {
        width: 3.125rem;
        height: 0.25rem;
        background: #0281e0;
        margin: 0.625rem 0;
      }
      .section_3_box_zimu {
        height: 1.75rem;
        font-size: 1rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #666666;
      }
    }
    .section_3_box_1 {
      width: 92.5rem;
      height: 28.125rem;
      background: #ffffff;
      border-radius: 0.9375rem;
      display: flex;
      margin-top: 2.5rem;
      .section_3_box_1_left {
        background-image: url('@/assets/images/daphone.png');
        background-size: 100% 100%;
        border-radius: 0.9375rem;
        width: 53.125rem;
        height: 28.125rem;
      }
      .section_3_box_1_right {
        flex: 1;
        position: relative;
        .section_3_box_1_right_box {
          position: absolute;
          top: 9.375rem;
          left: 3.125rem;
          .videos {
            width: 9.375rem;
            font-size: 1.5rem;
            font-size: 1.5rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #222222;
          }
          .zhivideos {
            margin-top: 1.875rem;
            width: 33.125rem;
            font-size: 1rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #666666;
          }
        }
      }
    }
    .section_3_box_2 {
      width: 92.5rem;
      height: 28.125rem;
      display: flex;
      margin-top: 2.5rem;
      background-color: #ffffff;
      border-top-right-radius: .9375rem;
      border-bottom-right-radius: .9375rem;
      .section_3_box_2_left {
        flex: 1;
        position: relative;
        .section_3_box_2_left_box {
          position: absolute;
          top: 9.375rem;
          top: 9.375rem;
          left: 3.125rem;
          display: flex;
          flex-direction: column;
          .section_3_box_2_left_box_1 {
            width: 7.5rem;
            font-size: 1.5rem;
            font-size: 1.5rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #222222;
          }
          .section_3_box_2_left_box_2 {
            margin-top: 1.875rem;
            width: 33.125rem;
            font-size: 1rem;
            font-size: 1rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #666666;
          }
        }
      }
      .section_3_box_2_right {
        width: 53.125rem;
        height: 28.125rem;
        background-image: url('@/assets/images/duoren.png');
        background-size: 100% 100%;
        border-radius: 0.9375rem;
      }
    }
    .section_3_box_3 {
      width: 92.5rem;
      height: 28.125rem;
      background: #ffffff;
      border-radius: 0.9375rem;
      display: flex;
      margin-top: 2.5rem;
      .section_3_box_1_left {
        background-image: url('@/assets/images/duorenshipin.png');
        background-size: 100% 100%;
        width: 53.125rem;
        height: 28.125rem;
        border-radius: 0.9375rem;
      }
      .section_3_box_1_right {
        flex: 1;
        position: relative;
        .section_3_box_1_right_box {
          position: absolute;
          top: 9.375rem;
          top: 9.375rem;
          left: 3.125rem;
          .videos {
            width: 16.875rem;
            font-size: 1.5rem;
            font-size: 1.5rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #222222;
          }
          .zhivideos {
            margin-top: 1.875rem;
            width: 33.125rem;
            font-size: 1rem;
            font-size: 1rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #666666;
          }
        }
      }
    }
    .section_3_box_4 {
      width: 92.5rem;
      height: 28.125rem;
      display: flex;
      margin-top: 2.5rem;
      .section_3_box_2_left {
        flex: 1;
        position: relative;
        background-color: #ffffff;
        .section_3_box_2_left_box {
          position: absolute;
          top: 9.375rem;
          top: 9.375rem;
          left: 3.125rem;
          display: flex;
          flex-direction: column;
          .section_3_box_2_left_box_1 {
            width: 7.5rem;
            font-size: 1.5rem;
            font-size: 1.5rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #222222;
          }
          .section_3_box_2_left_box_2 {
            margin-top: 1.875rem;
            width: 33.125rem;
            font-size: 1rem;
            font-size: 1rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #666666;
          }
        }
      }
      .section_3_box_2_right {
        width: 53.125rem;
        height: 28.125rem;
        background-image: url('@/assets/images/xiao.png');
        background-size: 100% 100%;
        border-radius: 0.9375rem;
      }
    }
  }
  .section_4 {
    width: 100%;
    height: 42.625rem;
    height: 42.625rem;
    background-image: url('@/assets/images/bgxi.png');
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    .section_9_top {
      display: flex;
      flex-direction: column;
      align-items: center;
      .section_9_top_text {
        width: 10rem;
        font-size: 1.875rem;
        font-size: 1.875rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #222222;
        margin-top: 4.375rem;
        text-align: center;
      }
      .section_9_top_xain {
        width: 3.125rem;
        height: 0.25rem;
        background: #0281e0;
        margin: 0.625rem 0;
      }
      .section_9_top_zimu {
        font-size: 1rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #666666;
      }
    }
    .section_9_bottom {
      width: 91.875rem;
      height: 30.875rem;
      display: flex;
      flex-wrap: wrap;
      margin-top: 2.5rem;
      justify-content: center;
      justify-content: center;
      .section_9_bottom_box1 {
        width: 43.75rem;
        height: 11.25rem;
        height: 11.25rem;
        display: flex;
        .section_9_bottom_box1_left {
          height: 5rem;
          height: 5rem;
          background: url('@/assets/images/fangbian.png');
          background-size: 100% 100%;
          width: 5rem;
          width: 5rem;
          .section_9_bottom_box1_left_text {
            width: 3.1875rem;
            display: block;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 2.5rem;
            font-size: 2.5rem;
            font-family: PingFangSC-Medium;
            font-weight: 500;
            white-space: nowrap;
            margin: 0.4375rem 0 0 1.25rem;
            margin: 0.4375rem 0 0 1.25rem;
          }
        }
        .section_9_bottom_box1_right {
          margin-left: 1.25rem;
          .section_9_bottom_box1_right_text {
            width: 26.25rem;
            font-size: 1.5rem;
            font-size: 1.5rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #222222;
            margin-bottom: 0.9375rem;
          }
          .section_9_bottom_box1_right_text1 {
            width: 36.25rem;
            font-size: 1rem;
            font-size: 1rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #222222;
          }
        }
      }
      .section_9_bottom_box2 {
        width: 43.75rem;
        height: 11.25rem;
        height: 11.25rem;
        display: flex;
        margin-left: 1.25rem;
        margin-left: 1.25rem;
        .section_9_bottom_box1_left {
          height: 5rem;
          height: 5rem;
          background: url('@/assets/images/fangbian.png') 100% no-repeat;
          background-size: 100% 100%;
          width: 5rem;
          width: 5rem;
          .section_9_bottom_box1_left_text {
            width: 3.1875rem;
            display: block;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 2.5rem;
            font-size: 2.5rem;
            font-family: PingFangSC-Medium;
            font-weight: 500;
            white-space: nowrap;
            margin: 0.4375rem 0 0 1.125rem;
          }
        }
        .section_9_bottom_box1_right {
          margin-left: 1.25rem;
          .section_9_bottom_box1_right_text {
            width: 26.25rem;
            font-size: 1.5rem;
            font-size: 1.5rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #222222;
          }
          .section_9_bottom_box1_right_text1 {
            width: 36.25rem;
            font-size: 1rem;
            font-size: 1rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #222222;
            margin-top: 0.9375rem;
          }
        }
      }
      .section_9_bottom_box3 {
        width: 43.75rem;
        height: 11.25rem;
        height: 11.25rem;
        display: flex;
        .section_9_bottom_box1_left {
          height: 5rem;
          height: 5rem;
          background: url('@/assets/images/fangbian.png') 100% no-repeat;
          background-size: 100% 100%;
          width: 5rem;
          width: 5rem;
          .section_9_bottom_box1_left_text {
            width: 3.1875rem;
            display: block;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 2.5rem;
            font-size: 2.5rem;
            font-family: PingFangSC-Medium;
            font-weight: 500;
            white-space: nowrap;
            margin: 0.4375rem 0 0 1.125rem;
            margin: 0.4375rem 0 0 1.125rem;
          }
        }
        .section_9_bottom_box1_right {
          margin-left: 1.25rem;
          .section_9_bottom_box1_right_text {
            width: 20.625rem;
            font-size: 1.5rem;
            font-size: 1.5rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #222222;
          }
          .section_9_bottom_box1_right_text1 {
            width: 36.25rem;
            font-size: 1rem;
            font-size: 1rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #222222;
            margin-top: 0.9375rem;
          }
        }
      }
      .section_9_bottom_box4 {
        width: 43.75rem;
        height: 11.25rem;
        height: 11.25rem;
        display: flex;
        margin-left: 1.25rem;
        margin-left: 1.25rem;
        .section_9_bottom_box1_left {
          height: 5rem;
          height: 5rem;
          background: url('@/assets/images/fangbian.png') 100% no-repeat;
          background-size: 100% 100%;
          width: 5rem;
          width: 5rem;
          .section_9_bottom_box1_left_text {
            width: 3.1875rem;
            display: block;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 2.5rem;
            font-size: 2.5rem;
            font-family: PingFangSC-Medium;
            font-weight: 500;
            white-space: nowrap;
            margin: 0.4375rem 0 0 1.125rem;
            margin: 0.4375rem 0 0 1.125rem;
          }
        }
        .section_9_bottom_box1_right {
          margin-left: 1.25rem;
          .section_9_bottom_box1_right_text {
            width: 11.25rem;
            font-size: 1.5rem;
            font-size: 1.5rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #222222;
          }
          .section_9_bottom_box1_right_text1 {
            width: 36.25rem;
            font-size: 1rem;
            font-size: 1rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #222222;
            margin-top: 0.9375rem;
          }
        }
      }
    }
  }
}
</style>
