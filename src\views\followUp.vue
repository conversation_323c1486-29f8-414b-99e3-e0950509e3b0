<script setup>
import myFooter from '@/components/myFooter.vue'
import myHeader from '@/components/myHeader.vue'
</script>
<template>
  <my-header />
  <div class="main_box">
    <div class="section_1">
      <div class="section_1box">
        <div class="section_1box_top">智慧随访系统</div>
        <div class="section_1box_bottom">
          旨在提高医疗机构对患者随访工作的效率和质量。该系统结合了AI电话自动随访、随访录音、CRF表格回填等先进技术，支持按病种类型配置随访计划和自定义随访表单，适用于各类医疗机构进行患者随访管理。
        </div>
      </div>
    </div>
    <div class="section_2">
      <div class="section_2_1">AI智能+智慧随访系统双引擎</div>
      <div class="section_2_2"></div>
      <div class="section_2_3">TECHNOLOGY LEADING THE ERA</div>
      <div class="section_2_4">
        <div class="section_2_text">
          <div class="section_2_text_1">大数据统计</div>
          <div class="section_2_text_2">多端同步 预警提示</div>
        </div>
        <div class="section_3_text">
          <div class="section_2_text_1">AI机器人</div>
          <div class="section_2_text_2">自动化回访 减少人力</div>
        </div>
        <div class="section_4_text">
          <div class="section_2_text_1">智慧随访</div>
          <div class="section_2_text_2">病状记录 便于浏览</div>
        </div>
        <div class="section_5_text">
          <div class="section_2_text_1">运营管理</div>
          <div class="section_2_text_2">降低管理成本 强化企业风控</div>
        </div>
      </div>
    </div>
    <div class="section_3">
      <div class="section_3_left">
        <div class="section_3_left_box">
          <span class="text_1">数据可视化</span>
          <span class="text_2">可视化大屏统计，实时同步、实时更新，便于患者及慢病风险统计。</span>
        </div>
        <div class="section_4_left_box">
          <span class="text_1">多端同步</span>
          <span class="text_2">PC、PAD、APP三端数据同步，便于监控患者健康状况。</span>
        </div>
        <div class="section_5_left_box">
          <span class="text_1">智能分析</span>
          <span class="text_2">检查数据分析，异常数据筛查并标注提示。</span>
        </div>
      </div>
      <div class="section_3_right"></div>
    </div>
    <div class="section_4">
      <div class="section_4_top">
        <div class="section_4_top_1">系统特色</div>
        <div class="section_4_top_2"></div>
        <div class="section_4_top_3">SYSTEM FEATURES</div>
      </div>
      <div class="section_4_bottom">
        <div class="section_4_bottom_box">
          <img class="section_4_bottom_box_img" alt="" src="@/assets/images/duanxin.png" />
          <div class="section_4_bottom_box_zhi">智能化随访</div>
          <div class="section_4_bottom_box_text">
            通过AI电话自动随访和随访录音等技术手段，实现智能化随访管理，提高工作效率和质量。
          </div>
        </div>
        <div class="section_5_bottom_box">
          <img class="section_4_bottom_box_img" alt="" src="@/assets/images/caidan.png" />
          <div class="section_4_bottom_box_zhi">自定义随访表单</div>
          <div class="section_4_bottom_box_text">
            支持用户自定义随访表单和评分标准设计，满足不同医疗机构的个性化需求。
          </div>
        </div>
        <div class="section_6_bottom_box">
          <img class="section_4_bottom_box_img" alt="" src="@/assets/images/shouji.png" />
          <div class="section_4_bottom_box_zhi">多平台支持</div>
          <div class="section_4_bottom_box_text">支持在不同设备和平台上使用，方便用户随时随地进行管理。</div>
        </div>
        <div class="section_7_bottom_box">
          <img class="section_4_bottom_box_img" alt="" src="@/assets/images/anquan.png" />
          <div class="section_4_bottom_box_zhi">数据安全保障</div>
          <div class="section_4_bottom_box_text">通过本地化部署和权限管理，确保患者数据和隐私的安全。</div>
        </div>
        <div class="section_8_bottom_box">
          <img class="section_4_bottom_box_img" alt="" src="@/assets/images/baibaoxiang.png" />
          <div class="section_4_bottom_box_zhi">国际医学标准问卷模板</div>
          <div class="section_4_bottom_box_text">提供多种国际医学标准问卷模板，方便用户参考和使用。</div>
        </div>
        <div class="section_9_bottom_box">
          <img class="section_4_bottom_box_img" alt="" src="@/assets/images/zhenguan.png" />
          <div class="section_4_bottom_box_zhi">科研支持</div>
          <div class="section_4_bottom_box_text">支持科研数据定制和导出，方便进行科研数据分析和统计。</div>
        </div>
      </div>
    </div>
    <div class="section_5">
      <div class="section_5_top">
        <div class="section_5_top_gong">功能特点</div>
        <div class="section_5_top_xian"></div>
        <div class="section_5_top_zimu">SYSTEM FUNCTIONS</div>
      </div>
      <div class="section_5_bottom">
        <div class="section_5_bottom_left"></div>
        <div class="section_5_bottom_right">
          <div class="section_5_bottom_right_text">
            <div class="group_9"></div>
            AI电话自动随访
          </div>
          <div class="section_5_bottom_right_text_1">
            系统可以根据预设的随访计划，自动拨打患者电话进行随访，并记录随访过程和结果。
          </div>
          <div class="section_5_bottom_right_text_2">
            <div class="group_9"></div>
            跨设备、跨平台使用
          </div>
          <div class="section_5_bottom_right_text_3">系统支持在不同设备和平台上使用，如电脑、平板、手机等。</div>
        </div>
      </div>
    </div>
    <div class="section_6">
      <div class="section_6_left">
        <div class="jiji">
          <div class="section_6_left_left">
            <div class="group_10"></div>
            随访录音
          </div>
          <div class="section_6_left_right">系统支持对随访过程进行录音，方便后续回顾和分析。</div>
        </div>
      </div>
      <div class="section_6_right"></div>
    </div>
    <div class="section_7">
      <div class="section_5_bottom">
        <div class="section_5_bottom_left"></div>
        <div class="section_5_bottom_right">
          <div class="section_5_bottom_right_text">
            <div class="group_9"></div>
            AI电话自动随访
          </div>
          <div class="section_5_bottom_right_text_1">
            系统可以根据预设的随访计划，自动拨打患者电话进行随访，并记录随访过程和结果。
          </div>
          <div class="section_5_bottom_right_text_2">
            <div class="group_9"></div>
            跨设备、跨平台使用
          </div>
          <div class="section_5_bottom_right_text_3">系统支持在不同设备和平台上使用，如电脑、平板、手机等。</div>
        </div>
      </div>
    </div>
    <div class="section_8">
      <div class="section_5_bottom">
        <div class="section_5_bottom_left">
          <div class="section_5_bottom_right_text">
            <div class="group_9"></div>
            院内HIS对接
          </div>
          <div class="section_5_bottom_right_text_1">系统支持从医疗机构的HIS系统中导入患者信息，避免重复录入。</div>
          <div class="section_5_bottom_right_text_2">
            <div class="group_9"></div>
            科研数据定制和导出
          </div>
          <div class="section_5_bottom_right_text_3">
            用户可以根据科研需求定制数据导出格式，方便进行数据分析和统计。
          </div>
        </div>
        <div class="section_5_bottom_right"></div>
      </div>
    </div>
    <div class="section_9">
      <div class="section_5_bottom">
        <div class="section_5_bottom_left"></div>
        <div class="section_5_bottom_right">
          <div class="section_5_bottom_right_text">
            <div class="group_9"></div>
            内置国际医学标准问卷模板
          </div>
          <div class="section_5_bottom_right_text_1">
            系统内置多种国际医学标准问卷模板，用户可以根据需要进行选择和修改。
          </div>
          <div class="section_5_bottom_right_text_2">
            <div class="group_9"></div>
            支持本地化部署
          </div>
          <div class="section_5_bottom_right_text_3">
            为了满足不同医疗机构的数据安全和隐私保护需求，系统支持本地化部署。
          </div>
        </div>
      </div>
    </div>
    <div class="section_10">
      <div class="section_11_top">
        <div class="text_xi">系统价值</div>
        <div class="text_se"></div>
        <div>SYSTEM VALUES</div>
      </div>
      <div class="section_11_bootom">
        <div class="xiao_box">
          <div class="xiaobox_left">
            <span class="text_44">01</span>
          </div>
          <div class="xiaobox_right">
            <div class="xiaobox_top">全面智能化服务</div>
            <div class="xiaobox_bottom">
              卫软智慧随访系统融合智能提醒、远程监测与数据分析，为用户提供智能化医疗服务，同时为医疗机构创造高效便捷的患者管理流程。
            </div>
          </div>
        </div>
        <div class="xiao_box">
          <div class="xiaobox_left">
            <span class="text_44">03</span>
          </div>
          <div class="xiaobox_right">
            <div class="xiaobox_top">个性化健康护航</div>
            <div class="xiaobox_bottom">
              通过数据分析和预测，系统能够为用户提供个性化的健康管理建议，为每位用户量身打造独特的健康护航方案。
            </div>
          </div>
        </div>
        <div class="xiao_box">
          <div class="xiaobox_left">
            <span class="text_44">02</span>
          </div>
          <div class="xiaobox_right">
            <div class="xiaobox_top">时效高效的医疗体验</div>
            <div class="xiaobox_bottom">
              智能化的提醒、自动随访简化医疗流程，远程监测与实时数据分析提高了医疗响应的时效性，为用户和医疗机构创造高效的医疗体验。
            </div>
          </div>
        </div>
        <div class="xiao_box">
          <div class="xiaobox_left">
            <span class="text_44">04</span>
          </div>
          <div class="xiaobox_right">
            <div class="xiaobox_top">持续关怀与科研支持</div>
            <div class="xiaobox_bottom">
              智慧随访系统实现了持续的医疗关怀和健康管理，同时为医疗机构提供丰富的患者数据，支持科研创新，促进医学进步。
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="section_11">
      <div class="section_11_top">
        <div class="section_11_top_1">应用场景</div>
        <div class="section_11_top_2"></div>
        <div class="section_11_top_3">APPLICATION SCENARIO</div>
      </div>
      <div class="section_11_bottom">
        <div class="section_11_bottom_1">
          <img src="@/assets/images/yiyuan.png" alt="" class="section_11_bottom_1_1" />
          <div class="section_11_bottom_1_2">基层卫生医疗机构</div>
          <div class="section_11_bottom_1_3">医疗机构整合随访，系统统计支持，方便管理</div>
        </div>
        <div class="section_11_bottom_2">
          <img src="@/assets/images/yiyuan.png" alt="" class="section_11_bottom_1_1" />
          <div class="section_11_bottom_1_2">基层卫生医疗机构</div>
          <div class="section_11_bottom_1_3">医疗机构整合随访，系统统计支持，方便管理</div>
        </div>
        <div class="section_11_bottom_3">
          <img src="@/assets/images/yiyuan.png" alt="" class="section_11_bottom_1_1" />
          <div class="section_11_bottom_1_2">基层卫生医疗机构</div>
          <div class="section_11_bottom_1_3">医疗机构整合随访，系统统计支持，方便管理</div>
        </div>
      </div>
    </div>
  </div>
  <myFooter />
</template>
<style style lang="scss" scoped>
.main_box {
  width: 100%;
  position: relative;
  margin: 0 auto;
  margin-top: 80px;
  display: flex;
  flex-direction: column;
  align-items: center;
  .section_1 {
    width: 1920px;
    height: 600px;
    background-image: url('@/assets/images/tingzhen.png');
    background-size: 100% 100%;
    position: relative;
    .section_1box {
      position: absolute;
      top: 176px;
      left: 1120px;
      width: 600px;
      height: 248px;
      .section_1box_top {
        color: #ffffff;
        font-size: 50px;
      }
      .section_1box_bottom {
        font-size: 24px;
        color: #ffffff;
        margin-top: 30px;
      }
    }
  }
  .section_2 {
    width: 1918px;
    height: 343px;
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: white;
    .section_2_1 {
      font-size: 40px;
      color: #222222;
      margin-top: 70px;
    }
    .section_2_2 {
      width: 50px;
      height: 4px;
      background: #0281e0;
      margin: 10px 0;
    }
    .section_2_3 {
      font-size: 20px;
      color: #666666;
    }
    .section_2_4 {
      margin-top: 40px;
      display: flex;
      justify-content: space-around;
      width: 100%;
      .section_2_text {
        display: flex;
        flex-direction: column;
        align-items: center;
        color: #4865ff;
        border-bottom: 4px solid #4865ff;
        .section_2_text_1 {
          font-size: 30px;
        }
        .section_2_text_2 {
          font-size: 24px;
        }
      }
      .section_3_text {
        display: flex;
        flex-direction: column;
        align-items: center;
        .section_2_text_1 {
          font-size: 30px;
        }
        .section_2_text_2 {
          font-size: 24px;
        }
      }
      .section_4_text {
        display: flex;
        flex-direction: column;
        align-items: center;
        .section_2_text_1 {
          font-size: 30px;
        }
        .section_2_text_2 {
          font-size: 24px;
        }
      }
      .section_5_text {
        display: flex;
        flex-direction: column;
        align-items: center;
        .section_2_text_1 {
          font-size: 30px;
        }
        .section_2_text_2 {
          font-size: 24px;
        }
      }
    }
  }
  .section_3 {
    width: 1918px;
    height: 770px;
    background: #f8f8f8;
    display: flex;
    .section_3_left {
      width: 500px;
      height: 660px;
      display: flex;
      flex-direction: column;
      margin: 40px 0 0 218px;
      .section_3_left_box {
        display: flex;
        flex-direction: column;
        box-shadow: 0px 0px 7px 0px rgba(0, 0, 0, 0.15);
        border-radius: 15px;
        .text_1 {
          font-size: 30px;
          color: #222222;
          margin: 36px 0 20px 40px;
        }
        .text_2 {
          font-size: 24px;
          color: #666666;
          width: 420px;
          margin: 0 0 36px 40px;
        }
      }
      .section_4_left_box {
        display: flex;
        flex-direction: column;
        background: #4865ff;
        box-shadow: 0px 0px 7px 0px rgba(0, 0, 0, 0.15);
        border-radius: 15px;
        margin: 30px 0;
        .text_1 {
          font-size: 30px;
          color: #ffffff;
          margin: 36px 0 20px 40px;
        }
        .text_2 {
          font-size: 24px;
          color: #ffffff;
          width: 420px;
          margin: 0 0 36px 40px;
        }
      }
      .section_5_left_box {
        display: flex;
        flex-direction: column;
        box-shadow: 0px 0px 7px 0px rgba(0, 0, 0, 0.15);
        border-radius: 15px;
        .text_1 {
          font-size: 30px;
          color: #222222;
          margin: 36px 0 20px 40px;
        }
        .text_2 {
          font-size: 24px;
          color: #666666;
          width: 420px;
          margin: 0 0 36px 40px;
        }
      }
    }
    .section_3_right {
      width: 940px;
      height: 660px;
      margin: 40px 0 0 40px;
      background-image: url('@/assets/images/ipadphone.png');
      background-size: 100% 100%;
    }
  }
  .section_4 {
    width: 1920px;
    height: 948px;
    display: flex;
    flex-direction: column;
    align-items: center;
    background-image: url('@/assets/images/xitebgr.png');
    background-size: 100% 100%;
    .section_4_top {
      display: flex;
      flex-direction: column;
      align-items: center;
      .section_4_top_1 {
        color: #222222;
        font-size: 40px;
        margin-top: 70px;
      }
      .section_4_top_2 {
        width: 50px;
        height: 4px;
        background: #0281e0;
        margin: 10px 0;
      }
      .section_4_top_3 {
        color: #666666;
        font-size: 20px;
      }
    }
    .section_4_bottom {
      width: 1430px;
      height: 660px;
      display: flex;
      flex-wrap: wrap;
      margin-top: 40px;
      .section_4_bottom_box {
        width: 450px;
        height: 310px;
        background: #ffffff;
        border-radius: 15px;
        display: flex;
        flex-direction: column;
        .section_4_bottom_box_img {
          width: 60px;
          height: 60px;
          margin: 30px 0 0 30px;
        }
        .section_4_bottom_box_zhi {
          color: #222222;
          font-size: 30px;
          margin: 20px 0 20px 30px;
        }
        .section_4_bottom_box_text {
          font-size: 24px;
          color: #666666;
          width: 390px;
          margin-left: 30px;
        }
      }
      .section_5_bottom_box {
        width: 450px;
        height: 310px;
        background: #ffffff;
        border-radius: 15px;
        display: flex;
        flex-direction: column;
        margin: 0 40px 40px 40px;
        .section_4_bottom_box_img {
          width: 60px;
          height: 60px;
          margin: 30px 0 0 30px;
        }
        .section_4_bottom_box_zhi {
          color: #222222;
          font-size: 30px;
          margin: 20px 0 20px 30px;
        }
        .section_4_bottom_box_text {
          font-size: 24px;
          color: #666666;
          width: 390px;
          margin-left: 30px;
        }
      }
      .section_6_bottom_box {
        width: 450px;
        height: 310px;
        background: #ffffff;
        border-radius: 15px;
        display: flex;
        flex-direction: column;
        .section_4_bottom_box_img {
          width: 60px;
          height: 60px;
          margin: 30px 0 0 30px;
        }
        .section_4_bottom_box_zhi {
          color: #222222;
          font-size: 30px;
          margin: 20px 0 20px 30px;
        }
        .section_4_bottom_box_text {
          font-size: 24px;
          color: #666666;
          width: 390px;
          margin-left: 30px;
        }
      }
      .section_7_bottom_box {
        width: 450px;
        height: 310px;
        background: #ffffff;
        border-radius: 15px;
        display: flex;
        flex-direction: column;
        .section_4_bottom_box_img {
          width: 60px;
          height: 60px;
          margin: 30px 0 0 30px;
        }
        .section_4_bottom_box_zhi {
          color: #222222;
          font-size: 30px;
          margin: 20px 0 20px 30px;
        }
        .section_4_bottom_box_text {
          font-size: 24px;
          color: #666666;
          width: 390px;
          margin-left: 30px;
        }
      }
      .section_8_bottom_box {
        width: 450px;
        height: 310px;
        background: #ffffff;
        border-radius: 15px;
        display: flex;
        flex-direction: column;
        margin: 0 40px;
        .section_4_bottom_box_img {
          width: 60px;
          height: 60px;
          margin: 30px 0 0 30px;
        }
        .section_4_bottom_box_zhi {
          color: #222222;
          font-size: 30px;
          margin: 20px 0 20px 30px;
        }
        .section_4_bottom_box_text {
          font-size: 24px;
          color: #666666;
          width: 390px;
          margin-left: 30px;
        }
      }
      .section_9_bottom_box {
        width: 450px;
        height: 310px;
        background: #ffffff;
        border-radius: 15px;
        display: flex;
        flex-direction: column;
        .section_4_bottom_box_img {
          width: 60px;
          height: 60px;
          margin: 30px 0 0 30px;
        }
        .section_4_bottom_box_zhi {
          color: #222222;
          font-size: 30px;
          margin: 20px 0 20px 30px;
        }
        .section_4_bottom_box_text {
          font-size: 24px;
          color: #666666;
          width: 390px;
          margin-left: 30px;
        }
      }
    }
  }
  .section_5 {
    width: 1920px;
    height: 688px;
    display: flex;
    flex-direction: column;
    align-items: center;
    .section_5_top {
      display: flex;
      flex-direction: column;
      align-items: center;
      .section_5_top_gong {
        color: #222222;
        font-size: 40px;
        margin-top: 70px;
      }
      .section_5_top_xian {
        width: 50px;
        height: 4px;
        background: #0281e0;
      }
      .section_5_top_zimu {
        color: #666666;
        font-size: 20px;
      }
    }
    .section_5_bottom {
      width: 1469px;
      height: 400px;
      display: flex;
      margin-top: 40px;
      .section_5_bottom_left {
        width: 600px;
        height: 400px;
        background-image: url('@/assets/images/xiangjiphone.png');
        background-size: 100% 100%;
      }
      .section_5_bottom_right {
        .section_5_bottom_right_text {
          display: flex;
          align-items: center;
          margin: 44px 0 0 70px;
          font-size: 30px;
          color: #222222;
          .group_9 {
            background-color: rgba(2, 129, 224, 1);
            border-radius: 2px;
            width: 4px;
            height: 20px;
            margin-right: 15px;
          }
        }
        .section_5_bottom_right_text_1 {
          width: 780px;
          color: #666666;
          font-size: 24px;
          margin: 20px 0 0 89px;
        }
        .section_5_bottom_right_text_2 {
          display: flex;
          align-items: center;
          margin: 40px 0 0 70px;
          font-size: 30px;
          color: #222222;
          .group_9 {
            background-color: rgba(2, 129, 224, 1);
            border-radius: 2px;
            width: 4px;
            height: 20px;
            margin-right: 15px;
          }
        }
        .section_5_bottom_right_text_3 {
          width: 780px;
          color: #666666;
          font-size: 24px;
          margin: 20px 0 0 89px;
        }
      }
    }
  }
  .section_6 {
    width: 1920px;
    height: 540px;
    display: flex;
    justify-content: center;
    .section_6_left {
      width: 869px;
      height: 400px;
      display: flex;
      flex-direction: column;
      .jiji {
        display: flex;
        flex-direction: column;
        justify-content: center;
        height: 100%;
        .section_6_left_left {
          color: #222222;
          font-size: 30px;
          display: flex;
          align-items: center;
          .group_10 {
            background-color: rgba(2, 129, 224, 1);
            border-radius: 2px;
            width: 4px;
            height: 20px;
            margin-right: 15px;
          }
        }
        .section_6_left_right {
          font-size: 24px;
          color: #666666;
          margin-top: 20px;
        }
      }
    }
    .section_6_right {
      width: 600px;
      height: 400px;
      background-image: url('@/assets/images/luyin.png');
      background-size: 100% 100%;
    }
  }
  .section_7 {
    width: 1920px;
    height: 540px;
    display: flex;
    justify-content: center;
    .section_5_bottom {
      width: 1469px;
      height: 400px;
      display: flex;
      margin-top: 40px;
      .section_5_bottom_left {
        width: 600px;
        height: 400px;
        background-image: url('@/assets/images/crf.png');
        background-size: 100% 100%;
      }
      .section_5_bottom_right {
        .section_5_bottom_right_text {
          display: flex;
          align-items: center;
          margin: 44px 0 0 70px;
          font-size: 30px;
          color: #222222;
          .group_9 {
            background-color: rgba(2, 129, 224, 1);
            border-radius: 2px;
            width: 4px;
            height: 20px;
            margin-right: 15px;
          }
        }
        .section_5_bottom_right_text_1 {
          width: 780px;
          color: #666666;
          font-size: 24px;
          margin: 20px 0 0 89px;
        }
        .section_5_bottom_right_text_2 {
          display: flex;
          align-items: center;
          margin: 40px 0 0 70px;
          font-size: 30px;
          color: #222222;
          .group_9 {
            background-color: rgba(2, 129, 224, 1);
            border-radius: 2px;
            width: 4px;
            height: 20px;
            margin-right: 15px;
          }
        }
        .section_5_bottom_right_text_3 {
          width: 780px;
          color: #666666;
          font-size: 24px;
          margin: 20px 0 0 89px;
        }
      }
    }
  }
  .section_8 {
    width: 1920px;
    height: 540px;
    display: flex;
    justify-content: center;
    .section_5_bottom {
      width: 1469px;
      height: 400px;
      display: flex;
      margin-top: 40px;
      .section_5_bottom_left {
        .section_5_bottom_right_text {
          display: flex;
          align-items: center;
          margin: 44px 0 0 70px;
          font-size: 30px;
          color: #222222;
          .group_9 {
            background-color: rgba(2, 129, 224, 1);
            border-radius: 2px;
            width: 4px;
            height: 20px;
            margin-right: 15px;
          }
        }
        .section_5_bottom_right_text_1 {
          width: 780px;
          color: #666666;
          font-size: 24px;
          margin: 20px 0 0 89px;
        }
        .section_5_bottom_right_text_2 {
          display: flex;
          align-items: center;
          margin: 40px 0 0 70px;
          font-size: 30px;
          color: #222222;
          .group_9 {
            background-color: rgba(2, 129, 224, 1);
            border-radius: 2px;
            width: 4px;
            height: 20px;
            margin-right: 15px;
          }
        }
        .section_5_bottom_right_text_3 {
          width: 780px;
          color: #666666;
          font-size: 24px;
          margin: 20px 0 0 89px;
        }
      }
      .section_5_bottom_right {
        width: 600px;
        height: 400px;
        background-image: url('@/assets/images/his.png');
        background-size: 100% 100%;
      }
    }
  }
  .section_9 {
    width: 1920px;
    height: 540px;
    display: flex;
    justify-content: center;
    background-color: #ffffff;
    .section_5_bottom {
      width: 1469px;
      height: 400px;
      display: flex;
      margin-top: 40px;
      .section_5_bottom_left {
        width: 600px;
        height: 400px;
        background-image: url('@/assets/images/tabtu.png');
        background-size: 100% 100%;
      }
      .section_5_bottom_right {
        .section_5_bottom_right_text {
          display: flex;
          align-items: center;
          margin: 44px 0 0 70px;
          font-size: 30px;
          color: #222222;
          .group_9 {
            background-color: rgba(2, 129, 224, 1);
            border-radius: 2px;
            width: 4px;
            height: 20px;
            margin-right: 15px;
          }
        }
        .section_5_bottom_right_text_1 {
          width: 780px;
          color: #666666;
          font-size: 24px;
          margin: 20px 0 0 89px;
        }
        .section_5_bottom_right_text_2 {
          display: flex;
          align-items: center;
          margin: 40px 0 0 70px;
          font-size: 30px;
          color: #222222;
          .group_9 {
            background-color: rgba(2, 129, 224, 1);
            border-radius: 2px;
            width: 4px;
            height: 20px;
            margin-right: 15px;
          }
        }
        .section_5_bottom_right_text_3 {
          width: 780px;
          color: #666666;
          font-size: 24px;
          margin: 20px 0 0 89px;
        }
      }
    }
  }
  .section_10 {
    width: 1920px;
    height: 594px;
    background-image: url('@/assets/images/bg1.png');
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    .section_11_top {
      width: 166px;
      height: 108px;
      margin-top: 70px;
      display: flex;
      flex-direction: column;
      align-items: center;
      .text_xi {
        color: #222222;
        font-size: 40px;
      }
      .text_se {
        width: 50px;
        height: 4px;
        background-color: #0281e0;
      }
    }
    .section_11_bootom {
      width: 1470px;
      height: 500px;
      margin-top: 40px;
      display: flex;
      flex-wrap: wrap;

      .xiao_box {
        display: flex;
        width: 700px;
        height: 128px;
        margin-right: 20px;
        align-items: center;
        justify-content: center;
        .xiaobox_left {
          height: 98px;
          background: url(https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng25c4e2e3560403c0c1cfd66c2e009ad268568a7aa421387be320f4b538848f31)
            100% no-repeat;
          background-size: 100% 100%;
          width: 88px;
          display: flex;
          flex-direction: column;
          .text_44 {
            width: 51px;
            height: 70px;
            color: rgba(255, 255, 255, 1);
            font-size: 50px;
            font-family: PingFangSC-Medium;
            font-weight: 500;
            white-space: nowrap;
            margin: 10px 0 0 18px;
          }
        }
        .xiaobox_right {
          width: 580px;
          display: flex;
          flex-direction: column;
          margin-left: 15px;
          .xiaobox_top {
            margin-bottom: 15px;
            color: #222222;
            font-size: 30px;
          }
          .xiaobox_bottom {
            width: 580px;
            color: #222222;
            font-size: 24px;
          }
        }
      }
    }
  }
  .section_11 {
    width: 1920px;
    height: 818px;
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: #ffffff;
    .section_11_top {
      display: flex;
      flex-direction: column;
      align-items: center;
      .section_11_top_1 {
        color: #222222;
        font-size: 40px;
        margin-top: 70px;
      }
      .section_11_top_2 {
        width: 50px;
        height: 4px;
        background: #0281e0;
        margin: 10px 0;
      }
      .section_11_top_3 {
        color: #666666;
        font-size: 20px;
      }
    }
    .section_11_bottom {
      width: 1670px;
      height: 530px;
      display: flex;

      .section_11_bottom_1 {
        display: flex;
        flex-direction: column;
        background-color: #f4f4f4;
        .section_11_bottom_1_1 {
          width: 530px;
          height: 350px;
        }
        .section_11_bottom_1_2 {
          color: #222222;
          font-size: 30px;
          margin: 20px 0 20px 30px;
        }
        .section_11_bottom_1_3 {
          width: 460px;
          color: #666666;
          font-size: 24px;
          margin-left: 30px;
        }
      }

      .section_11_bottom_2 {
        display: flex;
        flex-direction: column;
        background-color: #f4f4f4;
        margin: 0 40px;
        .section_11_bottom_1_1 {
          width: 530px;
          height: 350px;
        }
        .section_11_bottom_1_2 {
          color: #222222;
          font-size: 30px;
          margin: 20px 0 20px 30px;
        }
        .section_11_bottom_1_3 {
          width: 460px;
          color: #666666;
          font-size: 24px;
          margin-left: 30px;
        }
      }

      .section_11_bottom_3 {
        display: flex;
        flex-direction: column;
        background-color: #f4f4f4;
        .section_11_bottom_1_1 {
          width: 530px;
          height: 350px;
        }
        .section_11_bottom_1_2 {
          color: #222222;
          font-size: 30px;
          margin: 20px 0 20px 30px;
        }
        .section_11_bottom_1_3 {
          width: 460px;
          color: #666666;
          font-size: 24px;
          margin-left: 30px;
        }
      }
    }
  }
}
</style>
