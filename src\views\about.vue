<script setup>
import myFooter from '@/components/myFooter.vue'
import myHeader from '@/components/myHeader.vue'
import { ref, reactive, getCurrentInstance } from 'vue'
import { useRouter, useRoute } from 'vue-router'
const router = useRouter()
const route = useRoute()
const screenWidth = ref(document.body.clientWidth)
window.onresize = () => {
  // console.log('screenWidth', screenWidth.value);
  return (screenWidth.value = document.body.clientWidth)
}
function goDetail(num) {
  console.log('asdasdasd')
  if (screenWidth.value < 1024) {
    console.log('num', num)
    router.push({ path: '/Detail', name: 'detail', query: { page: 'job', id: num } })
  }
}
const idea = ref('使命')
const ideaText = reactive({
  '使命': {
    label: '打造数字化医疗生态系统',
    details: [
      '致力于以科技的力量，提升医疗服务的质量和可及性，打造一个数字化医疗生态系统，让每个人都能够享受到全面、个性化的健康管理，实现生命的更大价值。'
    ]
  },
  '愿景': {
    label: '',
    details: [
      '卫软致力于成为数字医疗创新的引领者，通过前瞻性技术和卓越服务，构建智慧医疗生态，使更多人能够享受到高品质、易得、可持续的医疗服务。'
    ]
  },
  '核心价值观': {
    label: '',
    details: [
      '卓越服务： 我们不仅是提供数字医疗解决方案的企业，更是客户卓越服务的合作伙伴。我们致力于创造卓越，为客户带来更多的价值。',
      '团队协同： 我们深信于团队的力量，鼓励员工间积极协作，共同应对挑战，实现共同的目标。',
      '诚信正直： 诚信是我们企业的灵魂，我们始终遵循道义和法规，与客户、员工、合作伙伴保持真实透明的沟通。',
      '持续学习： 数字医疗领域蓬勃发展，我们鼓励员工不断学习，紧跟时代，保持在行业的领先地位。'
    ]
  }
})
</script>

<template>
  <my-header />
  <div class="banner">
    <img src="@/assets/images/aboutHeader.png" alt="" />
    <div class="h1">关于卫软</div>
    <div class="h-info">让优质医疗触手可及</div>
  </div>
  <div class="w">
    <div class="company-profile">
      <div class="left">
        <div class="title">
          <div class="title-label">公司简介</div>
          <div class="title-en">COMPANY PROFILE</div>
        </div>
        <div class="profile">
          卫软（江苏）科技有限公司成立于2020年12月，是一家深耕于智慧医疗领域的高科技公司，是华为生态合作单位，江苏运动健康研究院孵化合作单位。<br />
          卫软科技围绕基层医疗服务场景，开发系统平台和智能硬件工具，赋能基层医疗最后一环，实现“健康管理、疾病预防、智能诊疗、便捷随访”一体化服务智慧运营。
        </div>
      </div>
      <div class="right">
        <!-- <img src="@/assets/images/company-profile1.png" class="profile1" alt="" /> -->
        <img src="@/assets/images/company-profile2.png" class="profile2" alt="" />
        <img src="@/assets/images/company-profile3.png" class="profile3" alt="" />
      </div>
    </div>
  </div>
  <div class="my-idea">
    <div class="title">
      <div class="title-label">我们的理念</div>
      <div class="title-en">OUR PHILOSOPHY</div>
    </div>
    <div class="w idea-content">
      <div class="left">
        <div class="left-tabs">
          <div class="tab-line" />
          <div class="tab-item" :class="{ active: idea === '使命' }" @click="idea = '使命'">
            使命
            <div v-if="idea === '使命'" class="after"></div>
          </div>
          <div class="tab-line" />
          <div class="tab-item" :class="{ active: idea === '愿景' }" @click="idea = '愿景'">
            愿景
            <div v-if="idea === '愿景'" class="after"></div>
          </div>
          <div class="tab-line" />
          <div class="tab-item" :class="{ active: idea === '核心价值观' }" @click="idea = '核心价值观'">
            核心价值观
            <div v-if="idea === '核心价值观'" class="after"></div>
          </div>
          <div class="tab-line" />
        </div>
        <img src="@/assets/images/adout-idea.png" alt="" />
      </div>
      <div class="right">
        <div>
          <div class="label" v-if="ideaText[idea].label">{{ ideaText[idea].label }}</div>
          <div class="details" v-for="(i, index) in ideaText[idea].details" :key="index">{{ i }}</div>
        </div>
      </div>
    </div>
  </div>
  <div class="achievement">
    <div class="title">
      <div class="title-label">我们的成就</div>
      <div class="title-en">OUR ACHIEVEMENTS</div>
    </div>
    <div class="w">
      <div class="text-info">
        <div class="label">坚守初心 共创未来</div>
        <div class="text">
          卫软科技 已发展成为一家现代化的、高科技的医疗技术公司。<br />
          卫软
          始终坚守初心，专注于医疗行业的技术创新，以物联网、5G、AI等为技术支撑，以健康档案与健康惠民为核心，在产品、技术、临床等方面取得重大成就。<br />
          卫软 与国内多家TOP医疗机构建立了紧密的技术合作关系，不断提高医疗效率，降低成本。同时，卫软
          始终坚持“客户至上”的理念，我们高度重视客户的反馈，致力于为广大客户提供优质的产品与服务，深入洞察客户需求，不断创新，快速推出高效的医疗解决方案。<br />
          卫软科技 愿与合作伙伴共同书写医疗新篇章。
        </div>
        <img src="@/assets/images/adout-achievement4.png" class="achievement4" alt="" />
        <img src="@/assets/images/adout-achievement5.png" class="achievement5" alt="" />
      </div>
      <div class="img-box">
        <img src="@/assets/images/adout-achievement2.png" class="achievement2" alt="" />
        <div class="bottom-img-box">
          <img src="@/assets/images/adout-achievement1.png" class="achievement1" alt="" />
          <img src="@/assets/images/adout-achievement3.png" class="achievement3" alt="" />
        </div>
      </div>
    </div>
    <div class="patent">
      <div class="li">江苏省科技企业认证</div>
      <div class="li">“一种基于心电信息激活的心电监测方法及装置”专利</div>
      <div class="client">
        <div class="title">
          <div class="title-label">典型客户</div>
          <div class="title-en">
            TYPICAL <br />
            CUSTOMERS
          </div>
        </div>
        <div class="cooperation-case">
          <a target="_blank" rel="noopener noreferrer">
            <div class="img-box">
              <img src="@/assets/images/anl9.png" alt="复旦大学附属中山医院" />
            </div>
            <div class="name">卫健委</div>
          </a>
          <a target="_blank" rel="noopener noreferrer">
            <div class="img-box">
              <img src="@/assets/images/anl1.png" alt="复旦大学附属中山医院" />
            </div>
            <div class="name">复旦大学附属中山医院</div>
          </a>
          <a target="_blank" rel="noopener noreferrer">
            <div class="img-box">
              <img src="@/assets/images/anl2.png" alt="南京市鼓楼医院" />
            </div>
            <div class="name">南京市鼓楼医院</div>
          </a>
          <a target="_blank" rel="noopener noreferrer">
            <div class="img-box">
              <img src="@/assets/images/anl3.png" alt="江苏省人民医院" />
            </div>
            <div class="name">江苏省人民医院</div>
          </a>
          <a target="_blank" rel="noopener noreferrer">
            <div class="img-box">
              <img src="@/assets/images/anl4.png" alt="华中科技大学附属同济医院" />
            </div>
            <div class="name">华中科技大学附属同济医院</div>
          </a>
          <a target="_blank" rel="noopener noreferrer">
            <div class="img-box">
              <img src="@/assets/images/anl5.png" alt="浙江大学附属第二医院" />
            </div>
            <div class="name">浙江大学附属第二医院</div>
          </a>
        </div>
      </div>
    </div>
  </div>
  <my-footer />
</template>
<style lang="scss" scoped>
.banner {
  max-width: 2560px;
  width: 100%;
  position: relative;
  margin: 0 auto;
  margin-bottom: calc(-31.25vw + 260px);
  margin-top: 56px;
  @media screen and (min-width: 2560px) {
    margin-bottom: calc(-2560px * 0.3125 + 260px);
  }

  img {
    width: 100%;
    height: auto;
    filter: brightness(50%);
  }

  @media screen and (max-width: 1024px) {
    margin-bottom: 24px;
    overflow: hidden;

    img {
      height: 600px;
      width: auto;
      position: relative;
      left: calc(-594px + 50%);
    }
  }
  .h1 {
    width: 200px;
    position: absolute;
    left: calc(50%);
    top: 70px;
    text-align: center;
    margin: 0 auto;
    transform: translateX(-50%);
    font-size: 36px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    color: #ffffff;
    line-height: 50px;
    z-index: 10;
    &::before {
      content: '';
      position: absolute;
      top: calc(50% - 3px);
      left: -50%;
      width: 80px;
      height: 6px;
      background: linear-gradient(270deg, #ffffff 0%, rgba(255, 255, 255, 0) 100%);
    }
    &::after {
      content: '';
      position: absolute;
      top: calc(50% - 3px);
      right: -50%;
      width: 80px;
      height: 6px;
      background: linear-gradient(90deg, #ffffff 0%, rgba(255, 255, 255, 0) 100%);
    }
  }
  .h-info {
    position: absolute;
    left: calc(50%);
    top: 70px;
    transform: translateX(-50%);
    top: 150px;
    font-size: 20px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    color: #ffffff;
    line-height: 28px;
  }
}
.title {
  margin: 70px 0 40px;
  font-weight: 500;
  font-size: 24px;
  color: #3b3f47;
  text-align: center;
  .title-label {
    display: inline-block;
    position: relative;
    font-size: 32px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    color: #222222;
    line-height: 45px;
    padding-bottom: 14px;
    &::after {
      content: '';
      width: 50px;
      height: 4px;
      background-color: #0281e0;
      position: absolute;
      left: calc(50% - 25px);
      bottom: 0;
    }
  }
  .title-en {
    font-size: 16px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    color: #666666;
    line-height: 28px;
    margin-top: 10px;
  }

  @media screen and (max-width: 1024px) {
    margin: 24px 0 8px;
    font-size: 16px;
  }
}
.company-profile {
  width: 100%;
  min-height: 500px;
  background: rgba(255, 255, 255, 0.8);
  box-shadow: 0px 2px 20px 0px rgba(121, 162, 203, 0.5);
  border-radius: 15px;
  backdrop-filter: blur(20px);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 70px;
  margin-bottom: 70px;
  .left {
    width: 42%;
    .title {
      margin-top: 30px;
      text-align: left;
      .title-label {
        font-size: 30px;
      }
      .title-label::after {
        left: 0;
      }
      .title-en {
        font-size: 16px;
        line-height: 22px;
      }
    }
    .profile {
      width: 600px;
      font-size: 20px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #222222;
      line-height: 36px;
    }
  }
  .right {
    position: relative;
    width: 45.2%;
    min-height: 500px;
    // .profile1 {
    //   position: absolute;
    //   left: 0;
    //   top: 0;
    //   width: 60%;
    //   height: 400px;
    //   background: #ededed;
    //   border-radius: 15px;
    // }
    .profile2 {
      width: 400px;
      height: 420px;
      position: absolute;
      left: 40%;
      bottom: 30px;
      width: 60%;
      background: #ededed;
      border-radius: 15px;
    }
    .profile3 {
      position: absolute;
      left: 0;
      bottom: 30px;
      width: 40%;
      height: 200px;
      background: #ededed;
      border-radius: 15px;
    }
  }
}
.my-idea {
  width: 100%;
  height: auto;
  max-width: 2560px;
  margin: 0 auto;
  background-image: url('@/assets/images/about-idea-background.png');
  background-size: 100% 100%;
  overflow: auto;
  padding-bottom: 70px;
  .idea-content {
    display: flex;
    .left {
      width: 58%;
      overflow: auto;
      .left-tabs {
        display: flex;
        align-items: center;
        width: 95%;
        margin-bottom: 48px;
        .tab-line {
          flex-grow: 1;
          height: 1px;
          background-color: #979797;
        }
        .tab-item {
          position: relative;
          font-size: 20px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #666666;
          line-height: 28px;
          padding: 0 20px;
          cursor: pointer;
          .after {
            position: absolute;
            left: calc(50% - 12px);
            bottom: -34px;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #0281e0;
            border: 7px solid #d9ecfb;
            box-sizing: content-box;
          }
          &.active {
            color: #0281e0;
          }
        }
      }
      img {
        width: 100%;
      }
    }
    .right {
      width: 41%;
      background: #ffffff;
      border-radius: 15px 15px 15px 0px;
      display: flex;
      align-items: center;
      padding: 70px;
      .label {
        font-size: 24px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #222222;
        line-height: 33px;
        margin-bottom: 20px;
      }
      .details {
        font-size: 16px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #222222;
        line-height: 26px;
      }
    }
  }
}
.achievement {
  width: 100%;
  position: relative;
  overflow: hidden;
  background-color: #f8f8f8;
  .w {
    display: flex;
    justify-content: space-between;
  }
  .text-info {
    width: 47%;
    // margin-bottom: 320px;
    position: relative;
    .label {
      font-size: 24px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      color: #222222;
      line-height: 33px;
      margin-bottom: 30px;
    }
    .text {
      font-size: 16px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #666666;
      line-height: 26px;
    }
    .achievement4 {
      width: 51%;
      margin-left: 49%;
      margin-top: 15px;
      margin-bottom: -17.857%;
    }
    .achievement5 {
      width: 85.7%;
      margin-bottom: -28.57%;
    }
  }
  .img-box {
    width: 50%;
    position: relative;
    .bottom-img-box {
      position: relative;
      width: 100%;
      margin-top: 27.3%;
    }
    .achievement1 {
      width: 54%;
      position: absolute;
      left: 0;
      top: 0;
      z-index: 7;
    }
    .achievement2 {
      width: 82%;
      position: absolute;
      right: 0;
      top: 0;
      z-index: 5;
    }
    .achievement3 {
      width: 75%;
      position: absolute;
      left: 54%;
      top: 0;
      z-index: 3;
    }
  }
  .patent {
    width: 68.75vw;
    margin-left: 31.25vw;
    background-color: #fff;
    padding: 50px 0 0px 70px;
    border-radius: 70px 0px 0px 0px;
    position: relative;
    z-index: 10;
    .li {
      position: relative;
      font-size: 24px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      color: #222222;
      line-height: 33px;
      margin-bottom: 40px;
      &::after {
        content: '';
        width: 20px;
        height: 20px;
        background: #4865ff;
        position: absolute;
        left: -30px;
        top: 7px;
        border-radius: 4px;
        transform: rotate(45deg);
      }
      &::before {
        content: '';
        width: 10px;
        height: 10px;
        background-color: #fff;
        position: absolute;
        left: -25px;
        top: 12px;
        border-radius: 50%;
        z-index: 5;
      }
    }
    .client {
      background: #f8f8f8;
      border-radius: 0px 70px 0px 0px;
      padding: 70px;
      position: relative;
      left: -22.39vw;
      z-index: 10;
      display: flex;
      .title {
        text-align: left;
        margin-right: 100px;
        .title-label::after {
          left: 0;
        }
        .title-label {
          width: 160px;
          font-size: 24px;
          line-height: 33px;
        }
        .title-en {
          line-height: 22px;
        }
      }
      .cooperation-case {
        width: calc(100% - 230px);
        display: flex;
        align-items: flex-end;
        justify-content: space-between;
        flex-wrap: wrap;
        // margin-bottom: 50px;

        a {
          width: 30% !important;
          text-align: center;
          display: flex;
          flex-direction: column;
          align-items: center;
          margin-bottom: 50px;
          color: #63676f;

          .name {
            white-space: nowrap;
            font-size: 16px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #222222;
            line-height: 22px;
          }
          img {
            width: 52px;
            height: 54px;
          }
          &:nth-child(4n + 1) {
            width: 20%;

            img {
              margin: 0;
            }
          }

          img {
            margin: 0 auto;
          }
        }

        @media screen and (max-width: 900px) {
          display: block;

          .img-box {
            position: relative;

            &::before {
              content: '';
              padding-top: 100%;
              float: left;
            }

            &::after {
              content: '';
              display: block;
              clear: both;
            }

            img {
              max-width: 80%;
              max-height: 80%;
              position: absolute;
              left: 0;
              right: 0;
              bottom: 0;
              top: 0;
              margin: auto !important;
            }
          }

          a {
            display: inline-block;
            width: 33.33% !important;
            text-align: center;
            font-size: 12px;

            .name {
              width: 80%;
              margin: 0 auto;
              overflow: hidden;
              text-overflow: ellipsis;
              text-align: center;
            }
          }
        }
      }
    }
  }
}
</style>
