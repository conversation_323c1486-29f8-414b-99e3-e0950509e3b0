<script setup>
import { RouterLink, RouterView, useRouter } from 'vue-router'
import { ref, reactive, getCurrentInstance, onMounted, watch } from 'vue'
// let nowTime = ref('')
// function timeChange(date) {
//   let y = date.getFullYear()
//   let m = date.getMonth() + 1
//   let d = date.getDate()
//   let h = date.getHours()
//   let i = date.getMinutes()
//   let s = date.getSeconds()
//   return `${y}年${m.toString().padStart(2, '0')}月${d.toString().padStart(2, '0')}日 ${h.toString().padStart(2, '0')}:${i.toString().padStart(2, '0')}:${s.toString().padStart(2, '0')}`
// }
// setInterval(() => {
//   nowTime.value = timeChange(new Date)
// }, 1000);
const active = ref(false)
const isShow = ref(false)
const isTrue = ref(false)
const isTrue_1 = ref(false)
const isTrue_2 = ref(false)
const hFuns = ref('0')
const pus = useRouter()
const souh = ref(pus.options.history.location)
window.addEventListener('scroll', () => {
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop
  // let offsetTop = document.querySelector(".header").offsetTop;
  // 设置背景颜色的透明读
  if (scrollTop > 200) {
    setTimeout(() => {
      active.value = true
    }, 500)
  } else {
    setTimeout(() => {
      active.value = false
    }, 500)
  }
  // console.log('scrollTop', scrollTop, 'active', active.value);
})
// const {proxy} = getCurrentInstance()
onMounted(() => {
  document.addEventListener(
    'click',
    e => {
      // console.log('proxy',proxy);
      // let s1 = proxy.$refs.s1;
      // if (!s1.contains(e.target)) {
      isShow.value = false
      // }
    },
    true
  )
})
watch(
  () => souh,
  (newval, val) => {
    if (newval.value.indexOf('/Software') !== -1) {
      hFuns.value = '1'
    } else if (newval.value.indexOf('/hardware') !== -1) {
      hFuns.value = '2'
    } else if (newval.value.indexOf('/Solution') !== -1) {
      hFuns.value = '3'
    } else if (newval.value.indexOf('/Case') !== -1) {
      hFuns.value = '4'
    } else if (newval.value.indexOf('/About') !== -1) {
      hFuns.value = '5'
    } else {
      hFuns.value = '0'
    }
  },
  { immediate: true }
)
const clickFun = val => {
  hFuns.value = val
}
const hovers = val => {
  if (val === 0) {
    isTrue.value = true
  } else if (val === 1) {
    isTrue_1.value = true
  } else {
    isTrue_2.value = true
  }
}
const moverlave = val => {
  if (val === 0) {
    isTrue.value = false
  } else if (val === 1) {
    isTrue_1.value = false
  } else {
    isTrue_2.value = false
  }
}
const routerFun = val => {
  pus.push(val)
  isTrue.value = false
  isTrue_1.value = false
  isTrue_2.value = false
}
</script>

<template>
  <div class="header" :class="{ 'ative-header': active }">
    <div class="header-w">
      <RouterLink :to="{ path: '/', name: 'home' }" class="logo-a">
        <img src="@/assets/images/logo.png" class="logo" alt="" />
        <span style="font-size: 0.875rem">卫软科技</span>
      </RouterLink>
      <RouterLink :to="{ path: '/', name: 'home' }"><span :class="hFuns === '0' ? 'a1' : 'a'">首页</span></RouterLink>
      <RouterLink
        :to="{ path: '/Software', name: 'followUp' }"
        @mouseover="hovers(0)"
        @mouseleave="moverlave(0)"
        @click="clickFun('1')"
        ><span
          style="
            height: 58px;
            padding-bottom: 33px;
            display: flex;
            margin-bottom: -32px;
            align-items: flex-end;
            font-size: 0.875rem;
          "
          :class="hFuns === '1' ? 'a1' : 'a'"
          >软件服务
        </span>
      </RouterLink>
      <RouterLink :to="{ path: '/hardware', name: 'followpackage' }" @mouseover="hovers(1)" @mouseleave="moverlave(1)"
        ><span
          style="
            height: 58px;
            padding-bottom: 33px;
            display: flex;
            margin-bottom: -32px;
            align-items: flex-end;
            font-size: 0.875rem;
          "
          :class="hFuns === '2' ? 'a1' : 'a'"
          >智能硬件</span
        ></RouterLink
      >
      <RouterLink
        :to="{ path: '/Solution/grassroots', name: 'grassroots' }"
        @mouseover="hovers(2)"
        @mouseleave="moverlave(2)"
        ><span
          style="
            height: 58px;
            padding-bottom: 33px;
            display: flex;
            margin-bottom: -32px;
            align-items: flex-end;
            font-size: 0.875rem;
          "
          :class="hFuns === '3' ? 'a1' : 'a'"
          >解决方案</span
        ></RouterLink
      >
      <RouterLink :to="{ path: '/Case', name: 'case' }"
        ><span :class="hFuns === '4' ? 'a1' : 'a'">客户案例</span></RouterLink
      >
      <RouterLink :to="{ path: '/About', name: 'about' }"
        ><span :class="hFuns === '5' ? 'a1' : 'a'">关于卫软</span></RouterLink
      >
      <transition name="fade">
        <div v-show="isTrue" class="hover fade-transition" @mouseover="hovers(0)" @mouseleave="moverlave(0)">
          <div class="hover_1" @click="routerFun('/Software/FollowUp')">
            <div class="left"></div>
            <div class="right">
              <div class="right_top">智慧随访系统</div>
              <div class="right_bottom">旨在提高医疗机构对患者随访工作的效率和质量</div>
            </div>
          </div>
          <div class="hover_2" @click="routerFun('/Software/HealthCheckup')">
            <div class="left"></div>
            <div class="right">
              <div class="right_top">居民健康体检系统</div>
              <div class="right_bottom">专为基层医疗机构设计的体检解决方案</div>
            </div>
          </div>
          <div class="hover_3" @click="routerFun('/Software/Remoteconsultation')">
            <div class="left"></div>
            <div class="right">
              <div class="right_top">远程会诊系统</div>
              <div class="right_bottom">为基层医疗机构提供与等级医院进行远程会诊的解决方案</div>
            </div>
          </div>
          <div class="hover_4" @click="routerFun('/Software/Telemedicine')">
            <div class="left"></div>
            <div class="right">
              <div class="right_top">医学装备管理系统</div>
              <div class="right_bottom">数倍提高工作效率，解决资产盘点问题</div>
            </div>
          </div>
        </div>
      </transition>
      <transition name="fade">
        <div class="hovers fade-transition" v-show="isTrue_1" @mouseover="hovers(1)" @mouseleave="moverlave(1)">
          <div class="hover_1" @click="routerFun('/hardware/Gateway')">
            <div class="left"></div>
            <div class="right">
              <div class="right_top">边缘计算网关</div>
              <div class="right_bottom">设备联网 安全可靠</div>
            </div>
          </div>
          <div class="hover_2" @click="routerFun('/hardware/Robot')">
            <div class="left"></div>
            <div class="right">
              <div class="right_top">AI基层辅助机器人</div>
              <div class="right_bottom">自动化回访 减少人力</div>
            </div>
          </div>
          <div class="hover_3" @click="routerFun('/hardware/hardwares')">
            <div class="left"></div>
            <div class="right">
              <div class="right_top">卫软穿戴设备</div>
              <div class="right_bottom">实时监测 安心保障</div>
            </div>
          </div>
          <div class="hover_4" @click="routerFun('/hardware/Allonemachine')">
            <div class="left"></div>
            <div class="right">
              <div class="right_top">健康检测一体机</div>
              <div class="right_bottom">基础检查 一机搞定</div>
            </div>
          </div>
          <div class="hover_5" @click="routerFun('/hardware/Followpackage')">
            <div class="left"></div>
            <div class="right">
              <div class="right_top">智慧随访包</div>
              <div class="right_bottom">一机两用 高端便捷</div>
            </div>
          </div>
        </div>
      </transition>
      <transition name="fade">
        <div class="settle fade-transition" v-show="isTrue_2" @mouseover="hovers(2)" @mouseleave="moverlave(2)">
          <div class="settle_box" @click="routerFun('/Solution/grassroots')">
            <div class="settle_box_left"></div>
            <div class="settle_box_right">
              <div class="settle_box_right_top">
                <div class="settle_box_right_tops">基层医防融合一体化服务</div>
                <div class="hao"></div>
              </div>
              <div class="settle_box_right_bottom">
                用最小的学习成本实现最大的效率提升，万物智能互联，将一切化繁为简
              </div>
            </div>
          </div>
          <div class="settle_boxs" @click="routerFun('/Solution/tightplatform')">
            <div class="settle_boxs_left"></div>
            <div class="settle_boxs_right">
              <div class="settle_boxs_right_top">紧密型医共体平台</div>
              <div class="settle_boxs_right_bottom">整合推进区域医疗资源共享，加快推进医疗卫生信息建设</div>
            </div>
          </div>
        </div>
      </transition>
    </div>
  </div>
  <div class="fold">
    <svg-icon ref="s1" class="svg" name="menufold" @click="isShow = !isShow" />
    <div v-show="isShow" class="menu">
      <RouterLink :to="{ path: '/', name: 'home' }">首页</RouterLink>
      <RouterLink :to="{ path: '/Software', name: 'followUp' }">软件服务</RouterLink>
      <RouterLink :to="{ path: '/hardware', name: 'followpackage' }">智能硬件</RouterLink>
      <RouterLink :to="{ path: '/Solution/grassroots', name: 'grassroots' }">解决方案</RouterLink>
      <RouterLink :to="{ path: '/Case', name: 'case' }">客户案例</RouterLink>
      <RouterLink :to="{ path: '/About', name: 'about' }">关于卫软</RouterLink>
    </div>
  </div>
</template>
<style scoped lang="scss">
.ative-header {
  // background-color: rgba(0, 0, 0, .85) !important;
  // backdrop-filter: blur(0) !important;
  // height: 0;
}
.hover {
  max-width: 120rem;
  overflow: hidden;
  position: absolute;
  width: 120rem;
  height: 15rem;
  background-color: #ffffff;
  top: 3.5rem;
  left: -23.125rem;
  display: flex;
  flex-wrap: wrap;
  .hover_1 {
    cursor: pointer;
    height: 3.75rem;
    display: flex;
    margin-left: 13.75rem;
    margin-top: 2.5rem;
    .left {
      background-image: url('@/assets/images/duanxin1.png');
      background-size: 100% 100%;
      width: 3.125rem;
      height: 3.125rem;
      margin-left: 7.5rem;
      margin-top: 4px;
    }
    .right {
      display: flex;
      flex-direction: column;
      margin-left: 1.25rem;
      .right_top {
        font-size: 1.125rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #222222;
      }
      .right_bottom {
        margin-top: 0.5rem;
        width: 15.625rem;
        height: 1.25rem;
        font-size: 0.875rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #666666;
        margin-top: 0.5rem;
      }
    }
  }
  .hover_2 {
    cursor: pointer;
    height: 3.75rem;
    display: flex;
    margin-top: 2.5rem;
    .left {
      background-image: url('@/assets/images/menu.png');
      background-size: 100% 100%;
      width: 3.125rem;
      height: 3.125rem;
      margin-left: 7.5rem;
      margin-top: 4px;
    }
    .right {
      display: flex;
      flex-direction: column;
      margin-left: 1.25rem;
      .right_top {
        font-size: 1.125rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #222222;
      }
      .right_bottom {
        width: 15.625rem;
        height: 1.25rem;
        font-size: 0.875rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #666666;
        margin-top: 0.5rem;
      }
    }
  }
  .hover_3 {
    cursor: pointer;
    height: 3.75rem;
    display: flex;
    margin-top: 2.5rem;
    .left {
      background-image: url('@/assets/images/zishouji.png');
      background-size: 100% 100%;
      width: 3.125rem;
      height: 3.125rem;
      margin-left: 7.5rem;
      margin-top: 4px;
    }
    .right {
      display: flex;
      flex-direction: column;
      margin-left: 1.25rem;
      .right_top {
        font-size: 1.125rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #222222;
      }
      .right_bottom {
        width: 15.625rem;
        height: 2.5rem;
        font-size: 0.875rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #666666;
        margin-top: 0.5rem;
      }
    }
  }
  .hover_4 {
    cursor: pointer;
    height: 2.5rem;
    display: flex;
    margin-left: 13.75rem;
    margin-top: 0.625rem;
    .left {
      background-image: url('@/assets/images/yixue.png');
      background-size: 100% 100%;
      width: 3.125rem;
      height: 3.125rem;
      margin-left: 7.5rem;
      margin-top: 4px;
    }
    .right {
      display: flex;
      flex-direction: column;
      margin-left: 1.25rem;
      .right_top {
        font-size: 1.125rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #222222;
      }
      .right_bottom {
        width: 15.625rem;
        height: 1.25rem;
        font-size: 0.875rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #666666;
        margin-top: 0.5rem;
      }
    }
  }
}
.hovers {
  max-width: 120rem;
  overflow: hidden;
  position: absolute;
  width: 120rem;
  height: 15rem;
  background-color: #ffffff;
  top: 3.5rem;
  left: -23.125rem;
  display: flex;
  flex-wrap: wrap;
  .hover_1 {
    cursor: pointer;
    width: 28.125rem;
    height: 3.75rem;
    display: flex;
    margin-left: 13.125rem;
    margin-top: 2.5rem;
    .left {
      background-image: url('@/assets/images/wifi.png');
      background-size: 100% 100%;
      width: 3.75rem;
      height: 3.75rem;
      margin-left: 8.125rem;
    }
    .right {
      display: flex;
      flex-direction: column;
      margin-left: 1.25rem;
      .right_top {
        width: 6.75rem;
        height: 1.5625rem;
        font-size: 1.125rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #222222;
        margin-bottom: 0.5rem;
      }
      .right_bottom {
        width: 7.3125rem;
        height: 1.25rem;
        font-size: 0.875rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #666666;
      }
    }
  }
  .hover_2 {
    cursor: pointer;
    width: 28.125rem;
    height: 3.75rem;
    display: flex;
    margin-top: 2.5rem;
    .left {
      background-image: url('@/assets/images/jiqiren.png');
      background-size: 100% 100%;
      width: 3.75rem;
      height: 3.75rem;
      margin-left: 8.125rem;
    }
    .right {
      display: flex;
      flex-direction: column;
      margin-left: 1.25rem;
      .right_top {
        height: 1.5625rem;
        font-size: 1.125rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #222222;
        margin-bottom: 0.5rem;
      }
      .right_bottom {
        width: 11.125rem;
        height: 1.25rem;
        font-size: 0.875rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #666666;
      }
    }
  }
  .hover_3 {
    cursor: pointer;
    height: 3.75rem;
    width: 28.125rem;
    display: flex;
    margin-top: 2.5rem;
    .left {
      background-image: url('@/assets/images/shoubiaos.png');
      background-size: 100% 100%;
      width: 3.75rem;
      height: 3.75rem;
      margin-left: 8.125rem;
    }
    .right {
      display: flex;
      flex-direction: column;
      margin-left: 1.25rem;
      .right_top {
        width: 6.75rem;
        height: 1.5625rem;
        font-size: 1.125rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #222222;
        margin-bottom: 0.5rem;
      }
      .right_bottom {
        width: 7.3125rem;
        height: 1.25rem;
        font-size: 0.875rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #666666;
      }
    }
  }
  .hover_4 {
    cursor: pointer;
    height: 3.75rem;
    width: 28.125rem;
    display: flex;
    margin-left: 13.125rem;
    margin-top: 2.5rem;
    .left {
      background-image: url('@/assets/images/yitiji.png');
      background-size: 100% 100%;
      width: 3.75rem;
      height: 3.75rem;
      margin-left: 8.125rem;
    }
    .right {
      display: flex;
      flex-direction: column;
      margin-left: 1.25rem;
      .right_top {
        width: 7.875rem;
        height: 1.5625rem;
        font-size: 1.125rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #222222;
        margin-bottom: 0.5rem;
      }
      .right_bottom {
        width: 7.3125rem;
        height: 1.25rem;
        font-size: 0.875rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #666666;
      }
    }
  }
  .hover_5 {
    cursor: pointer;
    height: 3.75rem;
    width: 28.125rem;
    display: flex;
    margin-top: 2.5rem;
    .left {
      background-image: url('@/assets/images/xiaoyiliao.png');
      background-size: 100% 100%;
      width: 3.75rem;
      height: 3.75rem;
      margin-left: 8.125rem;
    }
    .right {
      display: flex;
      flex-direction: column;
      margin-left: 1.25rem;
      .right_top {
        width: 5.625rem;
        height: 1.5625rem;
        font-size: 1.125rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #222222;
        margin-bottom: 0.5rem;
      }
      .right_bottom {
        width: 7.3125rem;
        height: 1.25rem;
        font-size: 0.875rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #666666;
      }
    }
  }
}
.header {
  width: 100%;
  height: 56px;
  // background: linear-gradient(180deg, rgba(46, 50, 58, 0.4) 0%, rgba(46, 50, 58, 0.116) 100%);
  // backdrop-filter: blur(7px);
  background-color: #ffffff;
  font-size: 18px;
  position: fixed;
  z-index: 100;
  top: 0;
  left: 0;
  transition: all 0.5s;
  border-bottom: 1px solid #e6e6e6;
  @media screen and (max-width: 1024px) {
    display: none;
  }

  .header-w {
    width: 1180px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
    // padding-left: 125px;
  }

  a {
    // color: #333;
    // color: rgba(255, 255, 255, 0.7);
    color: #76797e;
    font-weight: 400;
    // position: relative;
    &::after {
      content: '';
      position: absolute;
      bottom: -14px;
      left: 0;
      // left: calc(50% - 10px);
      width: 100%;
      height: 4px;
      border-radius: 2px;
      background-color: transparent;
      // background-image: linear-gradient(to right, #fe9531, #e91f01, #fe9531);
    }
    &:hover {
      color: #0281e0;
      &::after {
        background-color: #0281e0;
      }
    }
  }
}
.settle {
  width: 120rem;
  height: 15rem;
  background: #ffffff;
  position: absolute;
  top: 3.5rem;
  left: -23.125rem;
  display: flex;
  .settle_box {
    cursor: pointer;
    display: flex;
    height: 6.875rem;
    margin-top: 2.5rem;
    margin-left: 25.9375rem;
    .settle_box_left {
      width: 9.375rem;
      height: 6.875rem;
      background-image: url('@/assets/images/qiuqiu.png');
      background-size: 100% 100%;
    }
    .settle_box_right {
      flex: 1;
      display: flex;
      flex-direction: column;
      margin-left: 1.25rem;
      .settle_box_right_top {
        display: flex;
        .settle_box_right_tops {
          height: 1.5625rem;
          font-size: 1.125rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #222222;
        }
        .hao {
          width: 2.75rem;
          height: 1.25rem;
          background-image: url('@/assets/images/xiaonew.png');
          background-size: 100% 100%;
        }
      }
      .settle_box_right_bottom {
        width: 18.75rem;
        height: 2.5rem;
        font-size: 0.875rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #666666;
        margin-top: 0.9375rem;
      }
    }
  }
  .settle_boxs {
    cursor: pointer;
    height: 6.875rem;
    display: flex;
    margin-top: 2.5rem;
    margin-left: 9.375rem;
    .settle_boxs_left {
      background-image: url('@/assets/images/landiannao.png');
      background-size: 100% 100%;
      width: 9.375rem;
      height: 6.875rem;
      margin-right: 0.9375rem;
    }
    .settle_boxs_right {
      flex: 1;
      display: flex;
      flex-direction: column;
      .settle_boxs_right_top {
        height: 1.5625rem;
        font-size: 1.125rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #222222;
      }
      .settle_boxs_right_bottom {
        width: 18.75rem;
        height: 2.5rem;
        font-size: 0.875rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #666666;
        margin-top: 0.9375rem;
      }
    }
  }
}
.logo-a {
  // position: absolute;
  // left: 0;
  display: flex;
  align-items: center;
  font-size: 24px;
  font-family: STSongti-SC, STSongti-SC;
  font-weight: bold;
  margin-right: 100px;
  span {
    color: #222 !important;
  }

  &.router-link-active::after {
    display: none;
  }

  .logo {
    width: 32px;
    height: 16px;
    margin-right: 10px;
  }
}
.a1 {
  font-size: 1rem !important;
}
.a {
  font-size: 0.875rem !important;
}
.router-link-active {
  color: #0281e0 !important;
  font-weight: 500;

  &::after {
    background-color: #0281e0 !important;
    span {
      font-size: 1rem !important;
    }
  }
}

.fold {
  display: none;

  @media screen and (max-width: 1024px) {
    display: block;
    position: fixed;
    z-index: 100;
    top: 3%;
    right: 3%;

    .svg {
      font-size: 20px;
    }

    // .svg

    .menu {
      position: absolute;
      right: 0;
      top: 100%;
      width: 26vw;
      background: #26282b;
      border-radius: 2px;
      overflow: hidden;

      a {
        display: block;
        width: 100%;
        color: rgba(255, 255, 255, 0.7);
        line-height: 30px;
        padding-left: 15%;
        font-size: 1.8vw;
      }

      .router-link-active {
        background-color: #424548;
        color: #fff;
      }
    }
  }
}
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.7s ease;
}
.fade-enter,
.fade-leave-to {
  opacity: 0;
}
</style>
