<script setup>
import { RouterLink, RouterView, useRouter } from 'vue-router'
const $r = useRouter()
const CliFun = val => {
  $r.push(val)
}
</script>
<template>
  <footer>
    <div class="w">
      <div class="corporate-info">
        <div class="info-left">
          <div class="top-info">
            <img src="@/assets/images/logo.png" class="logo" alt="" />
            <span>卫来健康</span>
          </div>
          <div class="info-list">
            <p class="contact">
              <a href="tel:+4007555100">
                <img src="@/assets/images/dadianhua.png" alt="" style="width: 14px; height: 14px" />
                ************
              </a>
            </p>
            <p class="address">地址： 南京市江宁区乾德路2号创新中心1层181（江宁高新园）</p>
            <p class="mailto">
              <a href="mailto:<EMAIL>"> 邮箱： <EMAIL> </a>
            </p>
            <div class="other">
              <div class="qrcode-box">
                <div class="qrcode">
                  <img src="@/assets/images/qrcode.jpg" alt="" />
                </div>
                <img src="@/assets/images/weixin.png" alt="" style="width: 25px; height: 20px" />
              </div>
              <a href="" target="_blank" rel="noopener noreferrer">
                <img src="@/assets/images/weibo.png" alt="" style="width: 25px; height: 20px" />
              </a>
            </div>
          </div>
        </div>
        <div class="group" style="padding-right: 40px">
          <div class="group-label">首页</div>
          <div class="group-list">
            <div class="group-item allwidth" @click="CliFun('/')" style="cursor: pointer">首页</div>
          </div>
        </div>
      </div>
      <div class="group" style="padding: 0 15px; display: flex; flex-direction: column">
        <div class="group-label">软件服务</div>
        <div class="group-list">
          <div class="group-item" @click="CliFun('/Software/FollowUp')" style="cursor: pointer">随访系统</div>
          <div class="group-item" @click="CliFun('/Software/HealthCheckup')" style="cursor: pointer">
            居民健康体检系统
          </div>
          <div class="group-item" @click="CliFun('/Software/Remoteconsultation')" style="cursor: pointer">
            远程会诊系统
          </div>
          <div class="group-item" @click="CliFun('/Software/Telemedicine')" style="cursor: pointer">
            医学装备管理系统
          </div>
        </div>
      </div>
      <div class="group" style="display: flex; flex-direction: column">
        <div class="group-label">智能硬件</div>
        <div class="group-list">
          <div class="group-item allwidth" @click="CliFun('/hardware/Followpackage')" style="cursor: pointer">
            村医智能随访包
          </div>
          <div class="group-item" @click="CliFun('/hardware/Gateway')" style="cursor: pointer">物联网边缘计算网关</div>
          <div class="group-item" @click="CliFun('/hardware/Robot')" style="cursor: pointer">AI基层辅助机器人</div>
          <div class="group-item" @click="CliFun('/hardware/hardwares')" style="cursor: pointer">卫软智能健康手表</div>
          <div class="group-item" @click="CliFun('/hardware/Allonemachine')" style="cursor: pointer">
            健康监测一体机
          </div>
        </div>
      </div>
      <div class="group" style="display: flex; flex-direction: column">
        <div class="group-label">解决方案</div>
        <div class="group-list">
          <div class="group-item allwidth" @click="CliFun('/Solution/grassroots')" style="cursor: pointer">
            基层医防融合一体化服务
          </div>
          <div class="group-item" @click="CliFun('/Solution/tightplatform')" style="cursor: pointer">
            紧密型医共体平台
          </div>
        </div>
      </div>
      <div class="group" style="display: flex; flex-direction: column">
        <div class="group-label">客户案例</div>
        <div class="group-list">
          <div class="group-item allwidth" @click="CliFun('/Case')" style="cursor: pointer">客户案例</div>
        </div>
      </div>
      <div class="group" style="display: flex; flex-direction: column; align-items: center">
        <div class="group-label">关于卫软</div>
        <div class="group-list">
          <div class="group-item allwidth" @click="CliFun('/About')" style="cursor: pointer">公司简介</div>
        </div>
      </div>
      <!--<div class="left">
        <p class="contact">
          <a href="tel:+4007555100">
            <svg-icon class="svg" name="tel" color="#8C9097" />
            ************
          </a>
          <a href="mailto:<EMAIL>">
            <svg-icon class="svg" name="mailto" color="#8C9097" />
            <EMAIL>
          </a>
          <!~~ <a href="mailto:<EMAIL>">
            <svg-icon class="svg" name="address" color="#8C9097" />
            江苏省南京市江宁区丽泽路99号国际友好合作大厦3号楼8F
          </a> ~~>
          <span>
            <svg-icon class="svg" name="address" color="#8C9097" />
            <!~~ <svg width="16" height="20" viewBox="0 0 16 20" fill="none" xmlns="http://www.w3.org/2000/svg"
              style="font-size: 20px; margin-right: 6px;">
              <path
                d="M8 0C5.87827 0 3.84344 0.842855 2.34315 2.34315C0.842855 3.84344 0 5.87827 0 8C0 15 8 20 8 20C8 20 16 15 16 8C16 5.87827 15.1571 3.84344 13.6569 2.34315C12.1566 0.842855 10.1217 0 8 0ZM8 11C7.40666 11 6.82664 10.8241 6.33329 10.4944C5.83994 10.1648 5.45542 9.69623 5.22836 9.14805C5.0013 8.59987 4.94189 7.99667 5.05764 7.41473C5.1734 6.83279 5.45912 6.29824 5.87868 5.87868C6.29824 5.45912 6.83279 5.1734 7.41473 5.05764C7.99667 4.94189 8.59987 5.0013 9.14805 5.22836C9.69623 5.45542 10.1648 5.83994 10.4944 6.33329C10.8241 6.82664 11 7.40666 11 8C11 8.39397 10.9224 8.78407 10.7716 9.14805C10.6209 9.51203 10.3999 9.84274 10.1213 10.1213C9.84274 10.3999 9.51203 10.6209 9.14805 10.7716C8.78407 10.9224 8.39397 11 8 11Z"
                fill="#8C9097" />
            </svg> ~~>
            南京市江宁区乾德路2号创新中心1层181（江宁高新园）
            <!~~ 江苏省南京市江宁区丽泽路99号国际友好合作大厦3号楼8F ~~>
          </span>
        </p>
        <p class="group">
          <RouterLink :to="{}">使用条款</RouterLink> | <RouterLink :to="{}">网站地图</RouterLink> |
          <RouterLink :to="{}">隐私政策</RouterLink> | <RouterLink :to="{}">招聘隐私政策</RouterLink> |
          <RouterLink :to="{}">监察举报</RouterLink> |
          <RouterLink :to="{}">联系我们</RouterLink>
        </p>
        <p class="copyright">© 2023 卫软（江苏）科技有限公司 版权所有</p>
        <!~~ <p class="copyright">苏ICP备2021050267-2 号苏安网安备 32011402011074号</p> ~~>
        <p>
          <a href="https://beian.miit.gov.cn/" target="_blank">苏ICP备2021050267-2 号</a>
          <a
            href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=32011402011074"
            target="_blank"
            style="margin-left: 20px; display: flex; align-items: center"
          >
            <img
              src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAMAAABhEH5lAAACVVBMVEUAAADs47nIoFvezJff1KDHp3r17LXPsYn79bvt6bL7+c/w7M3NqmM5L1KulyfVuWnUvCnNr19TRFJpWlvMpWvJpGh0aGzZwF3Dl2SMfmfJoTPEnTu+lUzSsmu7llnh0WvPtITYwXGyqWfcx4GtmXKjl3/FnlG+m1XBsYXhz3/dypvo34Tc0WvMvpbdyo/MvoPl2o/fzoLEn1i8lFLq3JfUv23dyZnezH/j1pvdzJff0XTy5pjo3orn4bPv6qDg0arv48MAAF0CAloEAFYLAFTUsDLQpybCEAzFAADPsWvNsGYPEVg+OlVJRFJGOk8aAU7LpEs/NkohGUc0K0XRlj5xXj7QrD3RrzjZrTLVojLNpzDVrivKoiq9cSq9ZCjatiXIniXQXiXRqxzBJRTBFBLHCQXi4XPYv2/g02zYwmjUuFrSs1onKFa2o1TFm1Q9PFQvMlQPCVInKE/FqU1oZEwAAEwsKUsjDku4pUptXUnbs0jOrkhzbkhdW0hYSUjVtEVQQUU7L0UUD0XWuUTQrERnaEQ2HEQOCkQqAEQYCUCEbj/Zwj7Yvj3Sgj3RoDxgTzvErzrNpzqfgzrQqjfkpTeIcTdNBTfdqDaAZjPbvjLUtjLQrTGTdjG4QjCbgC60Ni7ZvC3Pdy3hwSrMQSq6VSnLoie7oSevlSbPfibDOybcuCTGnyS+WSPKNyPlxiHJoSC8nR/XXR/ZsR3Lox3egR3RtBzeexzDKxzFIhyPABzPohvIPhnMSBjUVhfXrRbOqBarCRLMPRHUMQ/GJA/DHQ/HDg3IAAOEZvMZAAAAQXRSTlMAF/yaelkvIR0PBwX++vn4+Pbx6+Pc19fVzcrJxcK/vbq5ubi4rKylpKOimJaUk5KPjIqFgYF6eHFoY1taVUkoAURwGQAAAAEwSURBVBjTYoAADltVSRY2BgTg0BXz8/EJFNZACBp2+uaGhBSXtKtABRjNhXyrdm9feXhKDa8pI1jIqtG5Yo/7juSU5IiiIH2QCJdOU05famqKu7vHltpSJS6gkJ1AUEGkx7G1i496bGt15rMGCtksCPaesDcxI6334PT8wg0mQCHtRf2OlRGxcbFxkZOcgt00gUKAya+b5+R/ZOPCFYmHpjmGbhIHCln0bC1z2Tl/yZq5Bzq8l81kBgoZhMfPcXJ2KW/zd3EMXTUbJMSS1Z00o8tv6vLAuobV9V5aQCFuPcFZ+1xd46Pdone5eimAnc+p6FkdExMV5ZYwkUfCHiyizJ+e2bx5/dKEsGxPJlmQKgdL0byAlvCk/ZPDAphEzLjB/mZnNVaXk5aSUTNiZQdyAaf1W12n0bFYAAAAAElFTkSuQmCC"
            />苏公网安备 32011402011074 号</a
          >
        </p>
      </div>
      <div class="right">
        <img src="@/assets/images/footer-logo.png" alt="" />
        <div class="box">
          <div class="qrcode-box">
            <div class="qrcode">
              <img src="@/assets/images/qrcode.jpg" alt="" />
            </div>
            <svg-icon class="svg wechat" name="wechat" color="#686c75" />
          </div>
          <a href="" target="_blank" rel="noopener noreferrer"><svg-icon class="svg" name="weibo" color="#686c75" /></a>
          <a href="" target="_blank" rel="noopener noreferrer"
            ><svg-icon class="svg" name="bilibili" color="#686c75"
          /></a>
        </div>
      </div>-->
    </div>
    <el-divider style="margin: 12px 0;"/>
    <div class="w foot-bd">
      <div>
        <a href="https://beian.miit.gov.cn/" target="_blank">苏ICP备2021050267 号</a>
        <a
          href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=32011402011074"
          target="_blank"
          style="margin-left: 20px; display: flex; align-items: center"
        >
          <img
            src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAMAAABhEH5lAAACVVBMVEUAAADs47nIoFvezJff1KDHp3r17LXPsYn79bvt6bL7+c/w7M3NqmM5L1KulyfVuWnUvCnNr19TRFJpWlvMpWvJpGh0aGzZwF3Dl2SMfmfJoTPEnTu+lUzSsmu7llnh0WvPtITYwXGyqWfcx4GtmXKjl3/FnlG+m1XBsYXhz3/dypvo34Tc0WvMvpbdyo/MvoPl2o/fzoLEn1i8lFLq3JfUv23dyZnezH/j1pvdzJff0XTy5pjo3orn4bPv6qDg0arv48MAAF0CAloEAFYLAFTUsDLQpybCEAzFAADPsWvNsGYPEVg+OlVJRFJGOk8aAU7LpEs/NkohGUc0K0XRlj5xXj7QrD3RrzjZrTLVojLNpzDVrivKoiq9cSq9ZCjatiXIniXQXiXRqxzBJRTBFBLHCQXi4XPYv2/g02zYwmjUuFrSs1onKFa2o1TFm1Q9PFQvMlQPCVInKE/FqU1oZEwAAEwsKUsjDku4pUptXUnbs0jOrkhzbkhdW0hYSUjVtEVQQUU7L0UUD0XWuUTQrERnaEQ2HEQOCkQqAEQYCUCEbj/Zwj7Yvj3Sgj3RoDxgTzvErzrNpzqfgzrQqjfkpTeIcTdNBTfdqDaAZjPbvjLUtjLQrTGTdjG4QjCbgC60Ni7ZvC3Pdy3hwSrMQSq6VSnLoie7oSevlSbPfibDOybcuCTGnyS+WSPKNyPlxiHJoSC8nR/XXR/ZsR3Lox3egR3RtBzeexzDKxzFIhyPABzPohvIPhnMSBjUVhfXrRbOqBarCRLMPRHUMQ/GJA/DHQ/HDg3IAAOEZvMZAAAAQXRSTlMAF/yaelkvIR0PBwX++vn4+Pbx6+Pc19fVzcrJxcK/vbq5ubi4rKylpKOimJaUk5KPjIqFgYF6eHFoY1taVUkoAURwGQAAAAEwSURBVBjTYoAADltVSRY2BgTg0BXz8/EJFNZACBp2+uaGhBSXtKtABRjNhXyrdm9feXhKDa8pI1jIqtG5Yo/7juSU5IiiIH2QCJdOU05famqKu7vHltpSJS6gkJ1AUEGkx7G1i496bGt15rMGCtksCPaesDcxI6334PT8wg0mQCHtRf2OlRGxcbFxkZOcgt00gUKAya+b5+R/ZOPCFYmHpjmGbhIHCln0bC1z2Tl/yZq5Bzq8l81kBgoZhMfPcXJ2KW/zd3EMXTUbJMSS1Z00o8tv6vLAuobV9V5aQCFuPcFZ+1xd46Pdone5eimAnc+p6FkdExMV5ZYwkUfCHiyizJ+e2bx5/dKEsGxPJlmQKgdL0byAlvCk/ZPDAphEzLjB/mZnNVaXk5aSUTNiZQdyAaf1W12n0bFYAAAAAElFTkSuQmCC"
          />苏公网安备 32011402011074 号</a
        >
      </div>
      <span>© 2023 卫软（江苏）科技有限公司 版权所有</span>
      <span></span>
    </div>
    <div class="phone">
      <div class="top">
        <img src="@/assets/images/footer-logo.png" alt="" />
        <div class="box">
          <div class="qrcode-box" style="display: flex; align-items: center">
            <div class="qrcode">
              <img src="@/assets/images/qrcode.jpg" alt="" />
            </div>
            <svg-icon class="svg wechat" name="wechat" color="#686c75" />
          </div>
          <a href="" target="_blank" rel="noopener noreferrer"><svg-icon class="svg" name="weibo" color="#686c75" /></a>
          <a href="" target="_blank" rel="noopener noreferrer"
            ><svg-icon class="svg" name="bilibili" color="#686c75"
          /></a>
        </div>
      </div>
      <p class="contact">
        <a href="tel:+4007555100">
          <svg-icon class="svg" name="tel" color="#8C9097" />
          ************
        </a>
        <a href="mailto:<EMAIL>">
          <svg-icon class="svg" name="mailto" color="#8C9097" />
          <EMAIL>
        </a>
      </p>
      <span class="span">
        <svg-icon class="svg" name="address" color="#8C9097" />
        南京市江宁区乾德路2号创新中心1层181（江宁高新园）
        <!-- 江苏省南京市江宁区丽泽路99号国际友好合作大厦3号楼8F -->
      </span>
      <div class="inline">
        <RouterLink :to="{}">使用条款</RouterLink> | <RouterLink :to="{}">网站地图</RouterLink> |
        <RouterLink :to="{}">隐私政策</RouterLink>
      </div>
      <div class="inline">
        <RouterLink :to="{}">招聘隐私政策</RouterLink> | <RouterLink :to="{}">监察举报</RouterLink> |
        <RouterLink :to="{}">联系我们</RouterLink>
      </div>
      <p class="copyright">© 2023 卫软（江苏）科技有限公司 版权所有</p>
      <!-- <p class="copyright">苏ICP备2021050267-2 号苏安网安备 32011402011074号</p> -->
      <p class="bp">
        <a href="https://beian.miit.gov.cn/" target="_blank">苏ICP备2021050267 号</a>
        <a
          href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=32011402011074"
          target="_blank"
          style="margin-left: 20px; display: flex; align-items: center"
        >
          <img
            src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAMAAABhEH5lAAACVVBMVEUAAADs47nIoFvezJff1KDHp3r17LXPsYn79bvt6bL7+c/w7M3NqmM5L1KulyfVuWnUvCnNr19TRFJpWlvMpWvJpGh0aGzZwF3Dl2SMfmfJoTPEnTu+lUzSsmu7llnh0WvPtITYwXGyqWfcx4GtmXKjl3/FnlG+m1XBsYXhz3/dypvo34Tc0WvMvpbdyo/MvoPl2o/fzoLEn1i8lFLq3JfUv23dyZnezH/j1pvdzJff0XTy5pjo3orn4bPv6qDg0arv48MAAF0CAloEAFYLAFTUsDLQpybCEAzFAADPsWvNsGYPEVg+OlVJRFJGOk8aAU7LpEs/NkohGUc0K0XRlj5xXj7QrD3RrzjZrTLVojLNpzDVrivKoiq9cSq9ZCjatiXIniXQXiXRqxzBJRTBFBLHCQXi4XPYv2/g02zYwmjUuFrSs1onKFa2o1TFm1Q9PFQvMlQPCVInKE/FqU1oZEwAAEwsKUsjDku4pUptXUnbs0jOrkhzbkhdW0hYSUjVtEVQQUU7L0UUD0XWuUTQrERnaEQ2HEQOCkQqAEQYCUCEbj/Zwj7Yvj3Sgj3RoDxgTzvErzrNpzqfgzrQqjfkpTeIcTdNBTfdqDaAZjPbvjLUtjLQrTGTdjG4QjCbgC60Ni7ZvC3Pdy3hwSrMQSq6VSnLoie7oSevlSbPfibDOybcuCTGnyS+WSPKNyPlxiHJoSC8nR/XXR/ZsR3Lox3egR3RtBzeexzDKxzFIhyPABzPohvIPhnMSBjUVhfXrRbOqBarCRLMPRHUMQ/GJA/DHQ/HDg3IAAOEZvMZAAAAQXRSTlMAF/yaelkvIR0PBwX++vn4+Pbx6+Pc19fVzcrJxcK/vbq5ubi4rKylpKOimJaUk5KPjIqFgYF6eHFoY1taVUkoAURwGQAAAAEwSURBVBjTYoAADltVSRY2BgTg0BXz8/EJFNZACBp2+uaGhBSXtKtABRjNhXyrdm9feXhKDa8pI1jIqtG5Yo/7juSU5IiiIH2QCJdOU05famqKu7vHltpSJS6gkJ1AUEGkx7G1i496bGt15rMGCtksCPaesDcxI6334PT8wg0mQCHtRf2OlRGxcbFxkZOcgt00gUKAya+b5+R/ZOPCFYmHpjmGbhIHCln0bC1z2Tl/yZq5Bzq8l81kBgoZhMfPcXJ2KW/zd3EMXTUbJMSS1Z00o8tv6vLAuobV9V5aQCFuPcFZ+1xd46Pdone5eimAnc+p6FkdExMV5ZYwkUfCHiyizJ+e2bx5/dKEsGxPJlmQKgdL0byAlvCk/ZPDAphEzLjB/mZnNVaXk5aSUTNiZQdyAaf1W12n0bFYAAAAAElFTkSuQmCC"
          />苏公网安备 32011402011074 号</a
        >
      </p>
    </div>
  </footer>
</template>
<style lang="scss" scoped>
footer {
  width: 100%;
  height: 338px;
  background-color: #fff;
  padding-top: 50px;

  @media screen and (max-width: 1024px) {
    height: auto;
    padding: 0;
  }

  .w {
    @media screen and (max-width: 1024px) {
      display: none;
    }

    display: flex;
    justify-content: space-between;

    .corporate-info {
      position: relative;
      width: 40%;
      display: flex;
      justify-content: space-between;
      .info-left {
        .top-info {
          display: flex;
          align-items: center;
          margin-bottom: 5px;

          img {
            width: 76px;
            height: 38px;
          }
          span {
            margin-left: 10px;
            font-size: 14px;
            font-family: STSongti-SC, STSongti-SC;
            color: #222222;
          }
        }
        .info-list {
          p {
            color: #666666;
            font-size: 16px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            line-height: 22px;
            margin-bottom: 15px;
            a {
              color: #666666;
            }
          }
          p.mailto {
            font-size: 14px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
          }
          p.contact {
            font-size: 16px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #666666;
            line-height: 33px;
            margin-bottom: 20px;
          }
          p.address {
            font-size: 14px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
          }
          .other {
            display: flex;
            align-items: center;
            .qrcode-box {
              position: relative;
              margin-right: 30px;
            }
            .qrcode {
              display: none;
              position: absolute;
              top: -100px;
              left: -35px;
              background: #ffffff;
              box-shadow: 0px 4px 10px rgba(167, 180, 191, 0.3);
              // display: flex;
              align-items: center;
              justify-content: center;

              &::after {
                content: '';
                position: absolute;
                bottom: -5px;
                width: 0;
                height: 0;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #fff;
              }

              img {
                width: 100px;
                height: 100px;
              }
            }

            .qrcode-box:hover .qrcode {
              display: flex;
            }
            img {
              width: 28px;
            }
          }
        }
      }
    }
    .group {
      .group-label {
        font-size: 16px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        color: #666666;
        padding-bottom: 11px;
        position: relative;
        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 0;
          width: 50px;
          height: 1px;
          background-color: #0281e0;
          // border: 1px solid #0281e0;
        }
      }
      .group-list {
        display: flex;
        align-items: flex-start;
        margin-top: 14px;
        flex-direction: column;
        .group-item {
          font-size: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #666666;
          margin-bottom: 15px;
          &.allwidth {
            width: 100%;
          }
        }
      }
    }
    &.foot-bd {
      font-size: 14px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #666666;
      div {
        display: flex;
      }
      a {
        color: #666666;
      }
    }

    .right {
      img {
        width: 167px;
        height: auto;
        @media screen and (max-width: 800px) {
          width: 50px;
        }
      }

      .box {
        position: relative;
        margin-top: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 30px;

        .wechat {
          cursor: pointer;
        }

        .qrcode {
          display: none;
          position: absolute;
          top: -100px;
          left: -35px;
          background: #ffffff;
          box-shadow: 0px 4px 10px rgba(167, 180, 191, 0.3);
          // display: flex;
          align-items: center;
          justify-content: center;

          &::after {
            content: '';
            position: absolute;
            bottom: -5px;
            width: 0;
            height: 0;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 5px solid #fff;
          }

          img {
            width: 100px;
            height: 100px;
          }
        }

        .qrcode-box:hover .qrcode {
          display: flex;
        }
      }
    }
  }

  .phone {
    @media screen and (min-width: 1024px) {
      display: none;
    }
    padding: 20px 0 20px;
    margin: 0 auto;
    width: 94%;
    font-size: 1.4vw;
    color: #76797e;
    .svg {
      font-size: 2vw;
      @media screen and (max-width: 750px) {
        font-size: 16px;
      }
    }
    a {
      display: flex;
      align-items: center;
      color: #76797e;
      font-size: 1.4vw;
    }
    .span {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
    }

    .top {
      display: flex;
      justify-content: space-between;

      img {
        width: 16vw;
        height: auto;
      }

      .box {
        margin-right: 15.5%;
        width: 20%;
        position: relative;
        margin-top: 8px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 2.5vw;

        .wechat {
          cursor: pointer;
        }

        .qrcode {
          display: none;
          position: absolute;
          top: -8vw;
          left: -2.8vw;
          background: #ffffff;
          box-shadow: 0px 4px 10px rgba(167, 180, 191, 0.3);
          // display: flex;
          align-items: center;
          justify-content: center;

          &::after {
            content: '';
            position: absolute;
            bottom: -5px;
            width: 0;
            height: 0;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 5px solid #fff;
          }

          img {
            width: 8vw;
            height: 8vw;
          }
        }

        .qrcode-box:hover .qrcode {
          display: flex;
        }
      }
    }

    .contact {
      display: flex;
      justify-content: space-between;
      white-space: nowrap;
      margin: 20px 0 8px;

      a {
        margin-right: 10%;

        .svg {
          margin-right: 0.7vw;
        }
      }
    }
    .inline {
      display: flex;
      justify-content: space-between;
      margin-bottom: 5px;
      a {
        width: 20%;
        text-align: center;
        white-space: nowrap;
      }
    }
    .copyright {
      margin: 16px 0 7px;
    }
    .bp {
      display: flex;

      a {
        // color: #3B3F47;

        img {
          margin-right: 5px;
        }
      }
    }
  }
}
</style>
