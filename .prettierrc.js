module.exports = {
  // 指定换行的行长度
  printWidth: 120,
  // 用制表符而不是空格缩进行
  useTabs: false,
  // 指定每个缩进级别的空格数
  tabWidth: 2,
  // 在语句末尾打印分号
  semi: false,
  // 使用单引号而不是双引号
  singleQuote: true,
  // object对象中key值是否加引号（quoteProps: "<as-needed|consistent|preserve>"）
  // as-needed只有在需求要的情况下加引号，consistent是有一个需要引号就统一加，preserve是保留用户输入的引号
  quoteProps: 'preserve',
  // 在JSX中使用单引号而不是双引号。
  jsxSingleQuote: false,
  // 在多行逗号分隔的句法结构中尽可能打印尾随逗号。 可选值"<es5|none|all>"
  trailingComma: 'none',
  // 打印对象字面量中括号之间的空格
  bracketSpacing: true,
  // 将多行 HTML（HTML、JSX、Vue、Angular）元素的 > 放在最后一行的末尾，而不是单独放在下一行（不适用于自闭合元素）。
  bracketSameLine: false,
  // 将多行 JSX 元素的 > 放在最后一行的末尾，而不是单独放在下一行（不适用于自闭合元素）。
  jsxBracketSameLine: false,
  // 在唯一的箭头函数参数周围包含括号。 可选值"<always|avoid>" (x) => x / x => x
  arrowParens: 'avoid',
  // 使用默认的折行标准 always\never\preserve
  proseWrap: 'preserve',
  // 指定HTML文件的全局空格敏感度 css\strict\ignore
  htmlWhitespaceSensitivity: 'css',
  // Vue文件脚本和样式标签缩进
  vueIndentScriptAndStyle: false,
  // 换行符使用 lf 结尾是 可选值"<auto|lf|crlf|cr>"
  endOfLine: 'auto'
}
