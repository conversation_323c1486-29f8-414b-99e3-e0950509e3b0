<script setup>
import myFooter from '@/components/myFooter.vue'
import myHeader from '@/components/myHeader.vue'
import { ref, reactive, getCurrentInstance } from 'vue'
import { useRouter, useRoute } from 'vue-router'
const router = useRouter()
const route = useRoute()
const isLfet = ref(false)
const boxleft = ref(0)
const pua = useRouter()
const { proxy } = getCurrentInstance()
function nextRight() {
  const divW = proxy.$refs.divW
  const ul = proxy.$refs.ul
  const itemBox = proxy.$refs.itemBox
  if (boxleft.value > -(ul.offsetWidth - itemBox.offsetWidth)) {
    // boxleft.value -= (divW.offsetWidth + 45)
    boxleft.value -= (ul.offsetWidth - itemBox.offsetWidth) / 5
  }
}
function prevLeft() {
  const divW = proxy.$refs.divW
  const ul = proxy.$refs.ul
  const itemBox = proxy.$refs.itemBox
  if (boxleft.value < 0) {
    boxleft.value += (ul.offsetWidth - itemBox.offsetWidth) / 5
    // boxleft.value += (divW.offsetWidth + 45)
  }
}
const clickFun = val => {
  pua.push(val)
  console.log(1)
}
// function goNewWeb() {
//   var popup = window.open("http://*************:8603/", "_blank");
//   if (popup) {
//     setTimeout(function () {
//       popup.postMessage('mARCr9o7P0j3H/SjKDq9gmQ1/VX1d5VHRDaif8ZY4XU=', 'http://*************:8603/');
//     }, 500);
//   }
// }
// function goNewWeb1() {
//   var popup = window.open("http://127.0.0.1:5173/", "_blank");
//   if (popup) {
//     setTimeout(function () {
//       popup.postMessage('mARCr9o7P0j3H/SjKDq9gmQ1/VX1d5VHRDaif8ZY4XU=', 'http://127.0.0.1:5173/');
//     }, 500);
//   }
// }
</script>

<template>
  <my-header />
  <div class="home">
    <div class="banner">
      <img src="@/assets/images/homeHeader.png" alt="" />
      <div class="banner-info">
        <div class="w">
          <!-- <p @click="goNewWeb">携手国内多家TOP医院， <br> 将信任传递每个人</p>
          <span @click="goNewWeb1">被信任，是一种能力，更是责任与担当，卫来健康携手国内多家TOP医院， <br> 为用户提供安全、可信、可靠的数字健康解决方案。 </span> -->
          <p>专注于基层医疗数字化解决方案</p>
          <span>
            将数字健康技术广泛应用，触达每一个村落与用户， <br />
            看更广，行更远
          </span>
          <button @click="router.push({ path: '/Solution/grassroots', name: 'grassroots' })">了解更多</button>
        </div>
      </div>
    </div>
    <div class="wigroup">
      <div class="w">
        <div class="wigroup-left title">
          <div class="title-label">走进卫软</div>
          <div class="title-en">ABOUT US</div>
          <button @click="router.push({ path: '/About', name: 'about' })">
            公司简介
            <svg-icon name="Right" color="#0281E0" />
          </button>
        </div>
        <div class="wigroup-right">
          卫软（江苏）科技有限公司是一家专注于基层医疗数字化解决方案提供商。围绕基层医疗提供数字化医防融合平台、公卫服务运营平台、智能健康检测一体机、物联网边缘计算网关、AI机器人、卫软穿戴设备等相关产品的高新技术企业。
        </div>
      </div>
    </div>
    <div class="product">
      <div class="title">
        <div class="title-label">优势产品</div>
        <div class="title-en">PRODUCTS</div>
      </div>
      <div class="w">
        <el-row class="product-content">
          <el-col :span="6">
            <div class="grid-content black-font" style="background-color: #fff" @click="clickFun('/Software/FollowUp')">
              <span>智慧随访软件</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="grid-content">
              <img src="@/assets/images/home-product1.png" alt="" />
            </div>
          </el-col>
          <el-col :span="6">
            <div class="grid-content" style="background-color: #7d7fff" @click="clickFun('/Software/Telemedicine')">
              <span>医学装备管理系统</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="grid-content">
              <img src="@/assets/images/product2.png" alt="" />
            </div>
          </el-col>
          <el-col :span="12">
            <div class="grid-content" style="background-color: #0281e0">
              <div style="width: 73%" @click="clickFun('/Software/HealthCheckup')">
                <span>基层医防融合一体化服务</span>
                <p>用最小的学习成本实现最大的效率提升，万物智能互联，将一切化繁为简</p>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="grid-content">
              <img src="@/assets/images/home-product3.png" alt="" />
            </div>
          </el-col>
          <el-col :span="6">
            <div class="grid-content" style="background-color: #383838" @click="clickFun('/hardware/hardwares')">
              <span>卫软智能穿戴设备</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="grid-content black-font" style="background-color: #fff" @click="clickFun('/hardware/Gateway')">
              <span>物联网边缘计算网关</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="grid-content">
              <img src="@/assets/images/home-product4.png" alt="" />
            </div>
          </el-col>
          <el-col :span="6">
            <div class="grid-content" style="background-color: #2ab5e0" @click="clickFun('/hardware/Robot')">
              <span>AI基层辅助机器人</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="grid-content">
              <img src="@/assets/images/home-product5.png" alt="" />
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
    <div class="solutions">
      <div class="title">
        <div class="title-label">解决方案</div>
        <div class="title-en">SOLUTIONS</div>
      </div>
      <div class="w">
        <div class="solutions-box">
          <div class="solutions-box-info">
            <div class="left">
              <span>基层医防融合一体化服务</span>
              <p>采用先进的云计算和大数据技术，提供适合基层医疗机构的系统平台和智…</p>
            </div>
            <img src="@/assets/images/home-solutions1.png" alt="" />
          </div>
          <button @click="router.push({ path: '/Solution/grassroots', name: 'grassroots' })">了解详情</button>
        </div>
        <div class="solutions-box">
          <div class="solutions-box-info">
            <div class="left">
              <span>紧密型医共体平台 {{ '\u3000' }}</span>
              <p>充分发挥县医院的城乡纽带作用和县域龙头作用。形成县乡医疗卫生机构…</p>
            </div>
            <img src="@/assets/images/home-solutions2.png" alt="" />
          </div>
          <button @click="router.push({ path: '/Solution/tightplatform', name: 'tightplatform' })">了解详情</button>
        </div>
      </div>
    </div>
    <div class="cooperative">
      <div class="w">
        <div class="title">
          <div class="title-label">合作医院</div>
          <div class="title-en">COOPERATIVE HOSPITAL</div>
        </div>
        <div class="cooperation-case">
          <a rel="noopener noreferrer">
            <div class="img-box">
              <img src="@/assets/images/anl1.png" alt="复旦大学附属中山医院" />
            </div>
            <div class="name">复旦大学附属中山医院</div>
          </a>
          <a rel="noopener noreferrer">
            <div class="img-box">
              <img src="@/assets/images/anl2.png" alt="南京市鼓楼医院" />
            </div>
            <div class="name">南京市鼓楼医院</div>
          </a>
          <a rel="noopener noreferrer">
            <div class="img-box">
              <img src="@/assets/images/anl3.png" alt="江苏省人民医院" />
            </div>
            <div class="name">江苏省人民医院</div>
          </a>
          <a rel="noopener noreferrer">
            <div class="img-box">
              <img src="@/assets/images/anl4.png" alt="华中科技大学附属同济医院" />
            </div>
            <div class="name">华中科技大学附属同济医院</div>
          </a>
          <a rel="noopener noreferrer">
            <div class="img-box">
              <img src="@/assets/images/anl5.png" alt="浙江大学附属第二医院" />
            </div>
            <div class="name">浙江大学附属第二医院</div>
          </a>
          <a rel="noopener noreferrer">
            <div class="img-box">
              <img src="@/assets/images/anl6.png" alt="武汉市第一医院" />
            </div>
            <div class="name">武汉市第一医院</div>
          </a>
          <a rel="noopener noreferrer">
            <div class="img-box">
              <img src="@/assets/images/anl7.png" alt="泰州市人民医院" />
            </div>
            <div class="name">泰州市人民医院</div>
          </a>
          <a rel="noopener noreferrer">
            <div class="img-box">
              <img src="@/assets/images/anl8.png" alt="深圳市罗湖区人民医院" />
            </div>
            <div class="name">深圳市罗湖区人民医院</div>
          </a>
        </div>
      </div>
    </div>
  </div>
  <my-footer />
</template>
<style lang="scss" scoped>
.home {
  width: 100%;
  overflow: hidden;
}

.banner {
  max-width: 2560px;
  width: 100%;
  position: relative;
  margin: 0 auto;
  display: flex;
  img {
    width: 100%;
    height: auto;
  }
  .banner-info {
    width: 100%;
    height: 100%;
    padding-top: 80px;
    position: absolute;
    left: 0;
    top: 0;
    color: #fff;
    display: flex;
    flex-direction: column;
    justify-content: center;
    text-align: center;
    font-family: 'Alibaba PuHuiTi 2.0';
    p {
      font-weight: 500;
      font-size: 50px;
      line-height: 67px;
      margin-bottom: 15px;
    }
    span {
      font-weight: 400;
      font-size: 20px;
      line-height: 37px;
    }
    button {
      width: 200px;
      height: 54px;
      border-radius: 27px;
      border: 1px solid #ffffff;
      background-color: transparent;
      font-weight: 400;
      font-size: 24px;
      // line-height: 34px;
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 30px auto 0;
      cursor: pointer;
    }
  }
  @media screen and (max-width: 1024px) {
    margin-bottom: 24px;
    overflow: hidden;
    img {
      height: 600px;
      width: auto;
      position: relative;
      left: calc(-594px + 50%);
    }
    .banner-info {
      display: flex;
      .w {
        width: 100vw;
        padding: 0 12%;
      }
      p {
        font-size: 32px;
        line-height: 48px;
      }
      span {
        font-size: 14px;
        line-height: 20px;
        br {
          display: none;
        }
      }
      button {
        width: 94px;
        height: 34px;
        font-size: 14px;
        margin-top: 40px;
      }
    }
  }

  @media screen and (max-width: 800px) {
    margin-bottom: 24px;
    overflow: hidden;

    img {
      height: 600px;
      width: auto;
      position: relative;
      left: calc(-594px + 50%);
    }
    .banner-info {
      display: flex;
      .w {
        width: 100vw;
        padding: 0 12%;
      }
      p {
        font-size: 18px;
        line-height: 24px;
      }
      span {
        font-size: 12px;
        line-height: 16px;
        br {
          display: none;
        }
      }
      button {
        width: 94px;
        height: 34px;
        font-size: 12px;
      }
    }
  }
}
.title {
  margin: 70px 0 40px;
  font-weight: 500;
  font-size: 24px;
  color: #3b3f47;
  text-align: center;
  .title-label {
    display: inline-block;
    position: relative;
    font-size: 36px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    color: #222222;
    line-height: 56px;
    padding-bottom: 14px;
    &::after {
      content: '';
      width: 50px;
      height: 4px;
      background-color: #0281e0;
      position: absolute;
      left: calc(50% - 25px);
      bottom: 0;
    }
  }
  .title-en {
    font-size: 16px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    color: #666666;
    line-height: 28px;
    margin-top: 10px;
  }

  @media screen and (max-width: 1024px) {
    margin: 24px 0 8px;
    font-size: 16px;
  }
}
.wigroup {
  height: 383px;
  margin: 0 auto;
  width: 100%;
  max-width: 2560px;
  background-color: #f1f0f0;
  background-image: url('@/assets/images/wigroup-banner.png');
  background-repeat: no-repeat;
  background-size: 71% 100%;
  background-position: 100% 0;
  display: flex;
  .w {
    padding: 0 10%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    .wigroup-left {
      position: absolute;
      left: 0;
      text-align: right;
      .title-label {
        &::after {
          left: auto;
          right: 0;
        }
      }
      button {
        margin-top: 30px;
        width: 130px;
        height: 44px;
        background: #ffffff;
        border-radius: 8px;
        border: 1px solid #0281e0;
        color: #0281e0;
        font-size: 18px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        display: flex;
        align-items: center;
        justify-content: center;
        float: right;
        cursor: pointer;
      }
    }
    .wigroup-right {
      width: 790px;
      height: 108px;
      font-size: 20px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #ffffff;
      line-height: 37px;
      position: absolute;
      right: 90px;
    }
  }
}
.product {
  max-width: 2560px;
  margin: 0 auto;
  width: 100%;
  padding: 1px 0;
  background-image: url('@/assets/images/home-solutions_bg.png');
  background-size: 100% 100%;
  .product-content {
    margin-top: 40px;
    margin-bottom: 4.375rem;
    border-radius: 15px;
    overflow: hidden;
    .grid-content {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      span {
        font-size: 24px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #ffffff;
        line-height: 42px;
      }
      p {
        margin: 20px 0 0;
        font-size: 16px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #ffffff;
        line-height: 37px;
      }
      img {
        width: 100%;
      }
      &.black-font {
        span {
          color: #222;
        }
        p {
          color: #222;
        }
      }
    }
  }
}
.solutions {
  width: 100%;
  max-width: 2560px;
  margin: 0 auto;
  background-color: #fff;
  padding-bottom: 70px;
  overflow: auto;

  .w {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .solutions-box {
      background: #ffffff;
      box-shadow: 0px 2px 20px 0px rgba(2, 129, 224, 0.2);
      border-radius: 15px;
      width: 48%;
      padding: 40px;
      .solutions-box-info {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 40px;
        .left {
          width: 45%;
          span {
            font-size: 24px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #222222;
            line-height: 47px;
          }
          p {
            font-size: 16px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #666666;
            margin-top: 20px;
          }
        }
        img {
          width: 47%;
        }
      }
      button {
        display: block;
        margin: 0 auto;
        width: 270px;
        height: 44px;
        background: #0281e0;
        border-radius: 8px;
        border: 0;
        color: #fff;
        font-size: 18px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #ffffff;
      }
    }
  }
}
.cooperative {
  width: 100%;
  max-width: 2560px;
  margin: 0 auto;
  background-color: #f3faff;
  overflow: auto;
}

.w {
  @media screen and (max-width: 1024px) {
    width: 91%;
  }
}

.programm-box {
  position: relative;
  transition: all 0.5s;
  width: 100%;
  display: flex;
  justify-content: space-between;

  .items-box {
    overflow: hidden;
  }

  .ul {
    width: 3240px;
    display: flex;
    transition: all 0.5s;
  }

  @media screen and (max-width: 1024px) {
    // position: static !important;
    .items-box {
      width: 100%;
    }

    .ul {
      position: static;
      display: flex;
      width: 100%;
      flex-wrap: wrap;
      justify-content: space-between;

      .item-box {
        width: 31.5%;
        margin-right: 0;
        margin-bottom: 10px;
        height: auto;

        &::before {
          content: '';
          padding-top: 100%;
          float: left;
        }

        &::after {
          content: '';
          display: block;
          clear: both;
        }

        .mask {
          height: 24%;
        }
      }
    }
  }

  .item-box {
    width: 320px;
    height: 320px;
    // display: inline-block;
    background-size: 100% 100%;
    position: relative;
    border-radius: 5px;
    overflow: hidden;
    margin-right: 45px;
    .mask {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 68px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(80, 86, 90, 0.6);
      backdrop-filter: blur(7px);
      font-family: 'Alibaba PuHuiTi 2.0';
      font-weight: 700;
      font-size: 20px;
      color: #fff;

      @media screen and (max-width: 800px) {
        font-size: 12px;
      }
    }
  }

  .prev,
  .next {
    position: absolute;
    top: calc(50% - 27px);
    width: 44px;
    height: 44px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(80, 81, 83, 0.5);
    backdrop-filter: blur(5px);
    color: #fff;
    font-size: 20px;
    // font-weight: 100;
    cursor: pointer;

    @media screen and (max-width: 1024px) {
      display: none;
    }
  }

  .prev {
    left: -104px;
    transform: rotate(180deg);

    @media screen and (max-width: 1920px) {
      left: -64px;
    }
  }

  .next {
    right: -104px;

    @media screen and (max-width: 1920px) {
      right: -64px;
    }
  }
}

.team {
  margin: 100px auto;
  width: 100%;
  max-width: 1920px;
  // height: 446px;
  background-size: 100% 100%;
  display: flex;
  justify-content: center;
  flex-direction: column;
  font-size: 18px;
  position: relative;

  .w {
    position: absolute;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    margin: auto;
  }

  &::before {
    content: '';
    padding-top: 23.22%;
    float: left;
  }

  &::after {
    content: '';
    display: block;
    clear: both;
  }

  p {
    font-style: normal;
    font-weight: 400;
    line-height: 34px;
    color: #f4f9ff;
  }

  button {
    width: 238px;
    height: 72px;
    background-color: #fff;
    border: 0;
    border-radius: 5px;
    font-weight: 400;
    font-size: 18px;
    line-height: 34px;
    color: #3c2c9f;
    display: flex;
    align-items: center;
    justify-content: center;
    // margin-top: 80px;
    cursor: pointer;
  }

  @media screen and (min-width: 1024px) and (max-width: 1920px) {
    margin: 50px auto;

    p {
      font-size: 16px;
    }

    button {
      width: 178.5px;
      height: 54px;
      font-size: 16px;
    }
  }

  @media screen and (max-width: 1024px) {
    margin: 25px auto;
    height: 144px !important;

    br {
      display: none;
    }

    p {
      width: 80%;
      font-size: 14px;
    }

    button {
      width: 90px;
      height: 30px;
      font-size: 14px;
      white-space: nowrap;
    }
  }
}

.cooperation-case {
  width: 100%;
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  flex-wrap: wrap;
  // margin-bottom: 50px;

  a {
    width: 25%;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 50px;
    color: #63676f;
    img {
      width: 80px;
      height: 75px;
    }
    .name {
      white-space: nowrap;
      font-size: 20px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #222222;
      line-height: 28px;
    }

    &:nth-child(4n + 1) {
      width: 20%;

      img {
        margin: 0;
      }
    }

    img {
      margin: 0 auto;
    }
  }

  @media screen and (max-width: 900px) {
    display: block;

    .img-box {
      position: relative;

      &::before {
        content: '';
        padding-top: 100%;
        float: left;
      }

      &::after {
        content: '';
        display: block;
        clear: both;
      }

      img {
        max-width: 80%;
        max-height: 80%;
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        top: 0;
        margin: auto !important;
      }
    }

    a {
      display: inline-block;
      width: 33.33% !important;
      text-align: center;
      font-size: 12px;

      .name {
        width: 80%;
        margin: 0 auto;
        overflow: hidden;
        text-overflow: ellipsis;
        text-align: center;
      }
    }
  }
}
</style>
