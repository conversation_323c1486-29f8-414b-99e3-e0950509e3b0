<script setup>
import { ref, reactive, getCurrentInstance } from 'vue'
import { useRouter, useRoute } from 'vue-router'
const router = useRouter()
const route = useRoute()
const page = ref(route.query.page)
const id = ref(route.query.id)
console.log(route.query)
const coop = reactive([
  {},
  {
    title: '企业合伙人机制',
    data: '我们推行的合伙人机制在公司内部是一种去中心化的组织模式，没有谁是组织的核心，强调的是责任权威和流程权威，而不是行政权威。<br><br>也就是说谁承担责任就由谁来决策，不是谁的官大谁来决策，这是组织效能体现的核心，也是互联网思维在企业管理模式上的应用。它能够有效提高组织的决策效率，发挥每个合伙人的责任意识，而不是仅仅依靠企业家本人来承担企业的所有责任。产业平台生态思维把合伙人机制去中心化的模式用在外部产业平台经营上，就是产业平台生态思维。产业生态思维就是形成相互依存的事业合伙关系，而不是整合与被整合、控制与被控制、管理与被管理的关系。合伙人机制不等于资源整合，而是相互合伙、相互赋能、相互成就。只有这样，平台自己才会成为产业生态的中心，因为跟平台合作能共享更大的价值，那大家都愿意跟平台合作。在产业生态的构建中，中西医药门店、诊所、医院、投资机构、政府等，甚至是竞争对手都不再是短期交易关系，而是通过合伙人模式从现有的产业平台升级为了一个更有价值的产业生态，这就是我们推行合伙人机制的核心诉求。'
  },
  {
    title: '产业平台生态思维',
    data: '把合伙人机制去中心化的模式用在外部产业平台经营上，就是产业平台生态思维。产业生态思维就是形成相互依存的事业合伙关系，而不是整合与被整合、控制与被控制、管理与被管理的关系。合伙人机制不等于资源整合，而是相互合伙、相互赋能、相互成就。只有这样，平台自己才会成为产业生态的中心，因为跟平台合作能共享更大的价值，那大家都愿意跟平台合作。在产业生态的构建中，中西医药门店、诊所、医院、投资机构、政府等，甚至是竞争对手都不再是短期交易关系，而是通过合伙人模式从现有的产业平台升级为了一个更有价值的产业生态，这就是我们推行合伙人机制的核心诉求。'
  },
  {
    title: '合作共赢',
    data: '合伙人可以以是个人，也可以是企业，在合伙人机制的结构中，个人和企业都是一个价值创造的相关方和相关能力，包括技术能力、引流能力、渠道能力、转化变现能力、客户运营能力、资本能力、政府沟通能力等等，所有能够为企业的成功提供价值的个人或其他企业都将是依存互补、合作共赢的关系，从而去获得价值的共识共担共创共享。'
  },
  {
    title: '分工协同和互补',
    data: '如果卫健康可穿戴平台把80%的精力放在门店的体验和运作上，那就是行业资源的浪费。而资本运作出身的企业家，把精力放在了钻研产品技术上，那就是企业资源的浪费。我们推行合伙机制就是推行分工协同机制，因为没有分工协同就不会有增量，没有增量就没有分配空间，没有分配空间就没有合伙的利益空间，分工协同和互补是合伙人机制成立的必要条件。'
  },
  {
    title: '交易结构',
    data: '交易结构是资本运作中的术语，我们把它用在合伙人机制的构建中，是因为没有明确的交易结构设计，就无法让参与到合伙人机制中的每个合伙人清晰地知道自己将会获得什么，也就无法产生激励效应。这对内部合伙人和外部合伙人都是实用的，因为没有明确交易结构的合伙人机制会使得参与到机制当中的合伙人产生疑惑和不安全感，从而降低了本应产生的激励效用。'
  },
  {
    title: '原动力',
    data: '每个合伙人参与合伙人机制都希望获得自己应该获得的价值，但是，不同的合伙主体（个人或企业）期望应该得到的价值是不同。充分了解合伙人希望获得什么，才能如何在保证冠健康平台整体价值实现的基础上，在合伙人的交易结构中，去实现每个合伙主体的利益诉求和价值诉求。合伙人机制是历史发展的必然，是时代进步的必然，是产业发展到这个阶段的必然，是人类社会发展到这个历史阶段的必然，是社会分工和价值分配的一次伟大的升级。我们推行合伙机，让其成为我们对内、对外的管理模式主流，成为所有跟我们合伙的个人和企业的普遍追求，使合伙人机制更好地激发每个人的活力，激发每个主体（人或机构）为自己同时也为企业奋斗的动能。这就是企业合伙人机制的合作共赢的魅力！'
  }
])
const job = reactive([
  {},
  {
    name: '市场总监',
    pay: '8-10k',
    data: [
      '招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案；',
      '招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案；',
      '招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案；'
    ]
  },
  {
    name: '运营总监',
    pay: '8-10k',
    data: [
      '招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案；',
      '招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案；',
      '招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案；'
    ]
  },
  {
    name: '运营',
    pay: '8-10k',
    data: [
      '招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案；',
      '招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案；',
      '招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案；'
    ]
  },
  {
    name: '前端开发工程师',
    pay: '8-10k',
    data: [
      '招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案；',
      '招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案；',
      '招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案；'
    ]
  },
  {
    name: '服务端开发工程师',
    pay: '8-10k',
    data: [
      '招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案；',
      '招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案；',
      '招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案；'
    ]
  },
  {
    name: 'UED设计师',
    pay: '8-10k',
    data: [
      '招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案；',
      '招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案；',
      '招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案招聘需求文案；'
    ]
  }
])
</script>
<template>
  <div class="w">
    <div v-if="page === 'coop'" class="coop">
      <div class="title">
        {{ coop[id].title }}
      </div>
      <div class="content" v-html="coop[id].data" />
    </div>
    <div v-if="page === 'contact'" class="contact">
      <div class="title">卫软（江苏）科技有限公司</div>
      <p>联系地址：</p>
      <span>南京市江宁区乾德路2号创新中心1层181（江宁高新园）</span>
      <!-- <span>江苏省南京市江宁区丽泽路99号国际友好合作大厦3号楼8F</span> -->
      <img src="@/assets/images/map1.png" alt="" style="width: 100%; height: auto" />
      <p>联系邮箱：</p>
      <span><a href="mailto:<EMAIL>"><EMAIL></a></span>
      <p>联系电话：</p>
      <span><a href="tel:+4007555100">************</a></span>
    </div>
    <div v-if="page === 'job'" class="job">
      <div class="title">
        人才招聘&ensp;//{{ job[id].name }}
        <span>{{ job[id].pay }}</span>
      </div>
      <p v-for="(i, index) in job[id].data" :key="index">
        <strong>{{ index + 1 }}.</strong>{{ i }}
      </p>
    </div>
  </div>
</template>
<style lang="scss" scope>
.coop {
  .title {
    font-family: 'PingFang SC';
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 20px;
    letter-spacing: -0.3px;
    color: #3b3f47;
    margin: 20px 0 10px;
  }

  .content {
    font-family: 'PingFang SC';
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    letter-spacing: -0.3px;
    color: #63676e;
  }
}

.contact {
  .title {
    font-family: 'PingFang SC';
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 20px;
    /* identical to box height */
    letter-spacing: -0.3px;
    color: #3b3f47;
    margin: 20px 0 10px;
  }

  p {
    font-family: 'PingFang SC';
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    /* identical to box height */
    letter-spacing: -0.3px;
    color: #63676e;
  }

  span {
    font-family: 'PingFang SC';
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    letter-spacing: -0.3px;
    color: #3b3f47;
    // margin-bottom: 5px;
    a {
      color: #3b3f47;
    }
  }
}

.job {
  font-family: 'PingFang SC';
  font-style: normal;
  font-weight: 500;
  line-height: 20px;

  .title {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    color: #3b3f47;
    margin: 20px 0 10px;
    span {
      color: #2f75d3;
    }
  }
  p {
    color: #63676e;
    font-weight: 500;
    font-size: 14px;
    line-height: 22px;
    letter-spacing: -0.3px;

    strong {
      color: #3b3f47;
      // font-weight: 600;
    }
  }
}
</style>
